# eKYC Framework Library ProGuard Rules
# These rules apply when building the AAR library itself with obfuscation enabled

# ===== PRESERVE PUBLIC API SURFACE =====
# Keep the main entry point class and its public methods
-keep public class com.scb.techx.ekycframework.util.EkycUtilities {
    public *;
}

# Keep all callback interfaces that apps need to implement
-keep public interface com.scb.techx.ekycframework.util.EkycUtilities$* {
    public *;
}

# Keep configuration and constants classes
-keep public class com.scb.techx.ekycframework.ui.processor.Config {
    public *;
}
-keep public class com.scb.techx.ekycframework.ui.processor.Config$* {
    public *;
}

-keep public class com.scb.techx.ekycframework.HandleCallback {
    public *;
}

-keep public class com.scb.techx.ekycframework.Constants {
    public *;
}
-keep public class com.scb.techx.ekycframework.Constants$* {
    public *;
}

# Keep theme customization classes (public API)
-keep public class com.scb.techx.ekycframework.ui.theme.** {
    public *;
}

# Keep public model classes that apps interact with
-keep public class com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue {
    public *;
}

# Keep result model classes used in callbacks
-keep public class com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult {
    public *;
}

# ===== OBFUSCATED URL PROVIDER PRESERVATION =====
# CRITICAL: Keep the ObfuscatedUrlProvider completely intact
-keep class com.scb.techx.ekycframework.ui.processor.ObfuscatedUrlProvider {
    public *;
    private *;
}

# Preserve all obfuscation utility methods to prevent breaking
-keepclassmembers class com.scb.techx.ekycframework.ui.processor.ObfuscatedUrlProvider {
    private *** decodeUrl(...);
    private *** xorBase64Decode(...);
    private *** hexCaesarDecode(...);
    private *** substitutionDecode(...);
    private *** dynamicAssemblyDecode(...);
    private *** reverseCombineDecode(...);
    private *** xorDecrypt(...);
    private *** caesarDecrypt(...);
    private *** hexToBytes(...);
    private *** applySubstitution(...);
    private *** createUrlSubstitutionMap(...);
    private *** validateUrl(...);
}

# Keep utility classes that apps use directly
-keep public class com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase {
    public *;
}

# Keep UI model classes used by apps
-keep public class com.scb.techx.ekycframework.ui.model.ocrprefill.OcrPrefill {
    public *;
}

# Keep data classes used in callbacks
-keep public class com.scb.techx.ekycframework.util.EkycUtilities$NdidError {
    public *;
}
-keep public class com.scb.techx.ekycframework.util.EkycUtilities$NdidData {
    public *;
}
-keep public class com.scb.techx.ekycframework.util.EkycUtilities$DopaResult {
    public *;
}

# Keep classes annotated with @Keep (already marked as public API)
-keep @androidx.annotation.Keep class * {
    public *;
}
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# ===== PRESERVE DEBUGGING INFORMATION =====
# Keep source file and line numbers for better crash reports
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

-dontwarn javax.annotation.Nullable
-dontwarn com.facetec.sdk.**
-keep class com.facetec.sdk.**
{ *; }

-keepattributes InnerClasses

-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }

# Keep Conscrypt classes
-keep class org.conscrypt.** { *; }
-dontwarn org.conscrypt.**

# Keep OpenJSSE classes (if you are using it, less common than Conscrypt)
-keep class org.openjsse.** { *; }
-dontwarn org.openjsse.**

# Keep OkHttp's internal platform classes that might reference Conscrypt/OpenJSSE
-keep class okhttp3.internal.platform.** { *; }
-dontwarn okhttp3.internal.platform.**

-dontwarn org.bouncycastle.**

# ===== RETROFIT & RXJAVA INTEGRATION =====
# Essential rules for Retrofit + RxJava to work with obfuscation
-keep class retrofit2.adapter.rxjava3.** { *; }
-dontwarn retrofit2.adapter.rxjava3.**

# Keep Retrofit interfaces and annotations
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# Keep RxJava classes
-keep class io.reactivex.rxjava3.** { *; }
-dontwarn io.reactivex.rxjava3.**

# ===== GSON SERIALIZATION =====
# Keep Gson classes and annotations
-keep class com.google.gson.** { *; }
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# Keep classes with @SerializedName
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# ===== PRESERVE SIGNATURES =====
# Critical for generic type preservation
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault

# ===== EKYC FRAMEWORK CLASSES =====
# Keep all API interfaces and their implementations
-keep interface com.scb.techx.ekycframework.data.**.api.** { *; }
-keep class com.scb.techx.ekycframework.data.**.datarepository.** { *; }

# Keep all model classes (request/response entities)
-keep class com.scb.techx.ekycframework.data.**.model.** { *; }
-keep class com.scb.techx.ekycframework.domain.**.model.** { *; }

# Keep mapper classes
-keep class com.scb.techx.ekycframework.data.**.mapper.** { *; }

# Keep classes that extend HashMap (AuthenticatedHeaders)
-keep class * extends java.util.HashMap { *; }

# ===== INTERNAL IMPLEMENTATION OBFUSCATION RULES =====
# Allow obfuscation of internal classes while preserving functionality

# Keep API interfaces but allow obfuscation of implementation details
-keep interface com.scb.techx.ekycframework.data.**.api.** {
    public *;
}

# Keep repository interfaces but allow implementation obfuscation
-keep interface com.scb.techx.ekycframework.domain.**.repository.** {
    public *;
}

# Keep model classes structure but allow field name obfuscation where safe
-keep class com.scb.techx.ekycframework.data.**.model.** {
    public *;
}
-keep class com.scb.techx.ekycframework.domain.**.model.** {
    public *;
}

# Keep Gson serialized fields (critical for API communication)
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# Keep classes that extend HashMap (AuthenticatedHeaders)
-keep class * extends java.util.HashMap {
    public *;
}

# ===== RETROFIT & RXJAVA PRESERVATION =====
# Critical for API functionality - preserve method signatures and generic types
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault,InnerClasses,EnclosingMethod

# Keep Retrofit interfaces and their method signatures
-keepclassmembers interface com.scb.techx.ekycframework.data.**.api.** {
    @retrofit2.http.* <methods>;
}

# Keep RxJava types and their generic information
-keep class io.reactivex.rxjava3.** { *; }
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Single
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Observable
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Completable

# Keep Retrofit call adapter factory
-keep class retrofit2.adapter.rxjava3.** { *; }
-dontwarn retrofit2.adapter.rxjava3.**

# Keep Retrofit and Gson classes
-keep class retrofit2.** { *; }
-keep class com.google.gson.** { *; }
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# ===== THIRD-PARTY LIBRARIES =====
# FaceTec SDK
-dontwarn com.facetec.sdk.**
-keep class com.facetec.sdk.** { *; }

# OkHttp and networking
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-keep class okhttp3.internal.platform.** { *; }
-dontwarn okhttp3.internal.platform.**

# Cryptography libraries
-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

-keep class org.conscrypt.** { *; }
-dontwarn org.conscrypt.**

-keep class org.openjsse.** { *; }
-dontwarn org.openjsse.**

# ===== ANDROID FRAMEWORK =====
# Keep Android annotations
-dontwarn javax.annotation.Nullable
-keepattributes InnerClasses

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep classes with native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# ===== OPTIMIZATION SETTINGS =====
# Enable aggressive optimization while preserving functionality
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# ===== INTERNAL FRAMEWORK CLASSES =====
# Allow obfuscation of internal implementation classes
# These can be obfuscated as they're not part of the public API

# Processors, presenters, and internal utilities can be obfuscated
-keep,allowobfuscation class com.scb.techx.ekycframework.ui.processor.** {
    !public *;
}
-keep,allowobfuscation class com.scb.techx.ekycframework.data.**.datarepository.** {
    !public *;
}
-keep,allowobfuscation class com.scb.techx.ekycframework.data.**.mapper.** {
    !public *;
}

# Keep use case classes but allow obfuscation of non-public members
-keep,allowobfuscation class com.scb.techx.ekycframework.domain.**.usecase.** {
    !public *;
}

# ===== FINAL SAFETY RULES =====
# Ensure critical functionality is preserved
-keepclassmembers class * {
    @retrofit2.http.* <methods>;
}

# Keep method signatures for reflection-based frameworks
-keepclassmembers class * {
    public <init>(...);
}

# Preserve generic signatures for proper type resolution
-keepattributes Signature