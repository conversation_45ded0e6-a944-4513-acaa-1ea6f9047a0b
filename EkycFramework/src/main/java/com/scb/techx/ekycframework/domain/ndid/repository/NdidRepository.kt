package com.scb.techx.ekycframework.domain.ndid.repository

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.RequestCancel
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequest
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidIdpResponse
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequestRequest
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestResponse
import io.reactivex.rxjava3.core.Single

interface NdidRepository {

    fun getNdidStatus(authenticatedHeaders: AuthenticatedHeaders): Single<NdidStatus>

    fun postRequestCancel(authenticatedHeaders: AuthenticatedHeaders): Single<RequestCancel>

    fun getIdpList(
        authenticatedHeaders: AuthenticatedHeaders,
        request: NdidIdpRequest
    ): Single<NdidIdpResponse>

    fun getNdidRequest(
        authenticatedHeaders: AuthenticatedHeaders,
        request: NdidIdpRequestRequest
    ): Single<NdidRequestResponse>

}