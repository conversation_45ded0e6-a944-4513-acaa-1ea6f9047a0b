package com.scb.techx.ekycframework.ui.ndidverification.idplist.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jakewharton.rxbinding4.view.clicks
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.data.ndid.datarepository.NdidDataRepository
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.USER_CANCELLED
import com.scb.techx.ekycframework.data.ndid.api.NdidApi
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.THEME_SETTING_ERROR
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ndidverification.model.NdidVerificationEnrollmentDisplay
import com.scb.techx.ekycframework.ui.ndidverification.themehelper.NdidThemeHelper
import com.scb.techx.ekycframework.ui.ndidverification.idplist.adapter.IdpListAdapter
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpData
import com.scb.techx.ekycframework.ui.ndidverification.idplist.model.IdpListModel
import com.scb.techx.ekycframework.ui.ndidverification.idplist.presenter.NdidIdpListContract
import com.scb.techx.ekycframework.ui.ndidverification.idplist.presenter.NdidIdpListPresenter
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

class IdpListFragment : Fragment(), NdidIdpListContract.View {

    private val ndidVerificationActivity by lazy { activity as NdidVerificationActivity }
    lateinit var presenter: NdidIdpListPresenter
    var idpNodeId = ""
    var idpIndustryCode = ""
    var idpCompanyCode = ""
    var idpRegistered = false
    lateinit var pref: EkycPreferenceUtil

    lateinit var rvBankList: RecyclerView
    lateinit var btnIdpNext: Button
    lateinit var btnIdpCancel: Button

    lateinit var tvBankTitle: TextView
    lateinit var tvBankDescription: TextView

    var idpList: MutableList<IdpListModel> = mutableListOf()
    var registeredIdpList: MutableList<IdpListModel> = mutableListOf()

    val repository = NdidDataRepository(GetApiClientUseCase.getApiClient().create(NdidApi::class.java))

    private val idpListAdapter by lazy {
        IdpListAdapter(ndidVerificationActivity) { idpNodeId: String, idpCompanyCode: String, idpIndustryCode: String, idpRegistered: Boolean ->
            onSelectBank(idpNodeId, idpCompanyCode, idpIndustryCode, idpRegistered)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        pref = EkycPreferenceUtil(ndidVerificationActivity)
        presenter = NdidIdpListPresenter(
            this,
            pref,
            ndidVerificationActivity,
            Schedulers.io(),
            AndroidSchedulers.mainThread(),
            NdidDataRepository(GetApiClientUseCase.getApiClient().create(NdidApi::class.java))
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        setHasOptionsMenu(true)
        return inflater.inflate(R.layout.fragment_idp_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        presenter.getIdpList()
        initFindByID(view)
        setTheme()
        initListener()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            ndidVerificationActivity.handleCallback(
                success = false,
                description = USER_CANCELLED,
                null,
                null,
                null
            )
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    private fun initFindByID(view: View) {
        rvBankList = view.findViewById(R.id.rv_bank_list)
        btnIdpNext = view.findViewById(R.id.btn_idp_next)
        btnIdpCancel = view.findViewById(R.id.btn_idp_cancel)
        tvBankTitle = view.findViewById(R.id.tv_bank_title)
        tvBankDescription = view.findViewById(R.id.tv_bank_description)
    }

    private fun setTheme() {
        try {
            NdidThemeHelper.setPrimaryColorTextTheme(tvBankTitle, true)
            NdidThemeHelper.setSecondaryColorTextTheme(tvBankDescription)
            NdidThemeHelper.setButtonColor(btnIdpNext)
            NdidThemeHelper.setBorderButtonColor(btnIdpCancel)
        } catch (e: IllegalArgumentException) {
            ndidVerificationActivity.handleCallback(
                false,
                THEME_SETTING_ERROR,
                null,
                null,
                null
            )
        }
    }

    private fun initListener() {
        btnIdpNext.clicks().subscribe {
            presenter.getNdidRequest(idpNodeId, idpCompanyCode, idpRegistered, idpIndustryCode)
        }

        btnIdpCancel.clicks().subscribe {
            ndidVerificationActivity.handleCallback(
                success = false,
                description = USER_CANCELLED,
                ndidStatus = null,
                ndidError = null,
                ndidData = null
            )
        }
    }


    private fun initAdapter() {
        rvBankList.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(ndidVerificationActivity)
            adapter = idpListAdapter
            idpListAdapter.setData(registeredIdpList, idpList)
        }
    }

    private fun onSelectBank(idpNodeId: String, idpCompanyCode: String, idpIndustryCode: String, idpRegistered: Boolean) {
        this.idpNodeId = idpNodeId
        this.idpCompanyCode = idpCompanyCode
        this.idpRegistered = idpRegistered
        this.idpIndustryCode = idpIndustryCode
        btnIdpNext.isEnabled = true
        idpListAdapter.setSelected(idpNodeId)
    }

    override fun showDialog(
        message: String,
        isPositiveOnly: Boolean,
        positiveCallback: NdidVerificationActivity.PositiveCallback,
        negativeCallback: NdidVerificationActivity.NegativeCallback?,
        positiveButtonText: String?,
        negativeButtonText: String?
    ) {
        ndidVerificationActivity.showDialog(
            message,
            isPositiveOnly,
            positiveCallback,
            negativeCallback,
            positiveButtonText,
            negativeButtonText
        )
    }

    override fun showLoadingDialog() {
        ndidVerificationActivity.showLoadingDialog()
    }

    override fun hideLoadingDialog() {
        ndidVerificationActivity.hideLoadingDialog()
    }

    override fun handleHttpException(throwable: Throwable) {
        ndidVerificationActivity.handleHttpException(throwable)
    }

    override fun handleErrorEkyc(code: String) {
        ndidVerificationActivity.handleErrorEkyc(code)
    }

    override fun callBackIdpResponse(response: IdpData?) {
        response?.registeredIdpList?.forEach {
            registeredIdpList.add(
                IdpListModel(
                    nodeId = it.nodeId,
                    marketingNameTh = it.marketingNameTh,
                    marketingNameEn = it.marketingNameEn,
                    smallIconPath = it.smallIconPath,
                    mediumIconPath = it.mediumIconPath,
                    largeIconPath = it.largeIconPath,
                    companyCode = it.companyCode,
                    industryCode = it.industryCode,
                    isSelected = false
                )
            )
        }
        response?.idpList?.forEach {
            idpList.add(
                IdpListModel(
                    nodeId = it.nodeId,
                    marketingNameTh = it.marketingNameTh,
                    marketingNameEn = it.marketingNameEn,
                    smallIconPath = it.smallIconPath,
                    mediumIconPath = it.mediumIconPath,
                    largeIconPath = it.largeIconPath,
                    companyCode = it.companyCode,
                    industryCode = it.industryCode,
                    isSelected = false
                )
            )
        }
        initAdapter()
    }

    override fun navigateToCountDown(ndidVerificationEnrollmentDisplay: NdidVerificationEnrollmentDisplay) {
        ndidVerificationActivity.navigateToEnrollment(ndidVerificationEnrollmentDisplay)
    }
}