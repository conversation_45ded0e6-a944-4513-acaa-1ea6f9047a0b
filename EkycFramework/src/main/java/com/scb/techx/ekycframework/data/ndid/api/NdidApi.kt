package com.scb.techx.ekycframework.data.ndid.api

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.data.ndid.model.NdidStatusEntity
import com.scb.techx.ekycframework.data.ndid.model.RequestCancelEntity
import com.scb.techx.ekycframework.Constants
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidIdpRequestEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidIdpResponseEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidRequestRequestEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidRequestResponseEntity
import io.reactivex.rxjava3.core.Single
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Url

interface NdidApi {

    @GET
    fun getNdidStatus(
        @Url dynamicUrl: String,
        @HeaderMap authenticatedHeaders: AuthenticatedHeaders
    ): Single<NdidStatusEntity>

    @POST
    fun postRequestCancel(
        @Url dynamicUrl: String,
        @HeaderMap authenticatedHeaders: AuthenticatedHeaders
    ): Single<RequestCancelEntity>

    @Headers(Constants.ContentType)
    @POST
    fun getIdpList(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: NdidIdpRequestEntity
    ): Single<NdidIdpResponseEntity>

    @Headers(Constants.ContentType)
    @POST
    fun getNdidRequest(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: NdidRequestRequestEntity
    ): Single<NdidRequestResponseEntity>

}