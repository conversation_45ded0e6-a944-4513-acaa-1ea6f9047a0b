package com.scb.techx.ekycframework.ui.ndidverification.idplist.viewholder

import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.THEME_SETTING_ERROR
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ndidverification.themehelper.NdidThemeHelper

class IdpHeaderViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
    private val tvBanKSelectionHeader: TextView by lazy { view.findViewById(R.id.tv_banK_selection_header) }
    private val ndidVerificationActivity by lazy { view.context as NdidVerificationActivity }
    fun bind(type: String) {
        with(itemView) {
            tvBanKSelectionHeader.text = type
            setTheme()
        }
    }

    private fun setTheme() {
        try {
            NdidThemeHelper.setPrimaryColorTextTheme(tvBanKSelectionHeader, true)
        } catch (e: IllegalArgumentException) {
            ndidVerificationActivity.handleCallback(
                false,
                THEME_SETTING_ERROR,
                null,
                null,
                null
            )
        }
    }
}