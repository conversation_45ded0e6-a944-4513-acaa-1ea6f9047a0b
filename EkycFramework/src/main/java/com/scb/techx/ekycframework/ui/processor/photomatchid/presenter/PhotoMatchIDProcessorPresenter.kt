package com.scb.techx.ekycframework.ui.processor.photomatchid.presenter

import com.facetec.sdk.FaceTecFaceScanResultCallback
import com.facetec.sdk.FaceTecIDScanResultCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1000
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7102
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.common.usecase.GetErrorMessageFromExceptionUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.GetMatch3D2DUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.FormatOcrFieldUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.GetEnrollment3DUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.mapper.UserConfirmedValueDisplayMapper
import io.reactivex.rxjava3.core.Scheduler

class PhotoMatchIDProcessorPresenter (
    private val processor: PhotoMatchIDProcessorContract.Processor,
    private val pref: EkycPreferenceUtil,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val faceTecRepository: FaceTecRepository
) : PhotoMatchIDProcessorContract.Presenter {
    private fun setThrownCallback(throwable: Throwable) {
        processor.failCallbackWithMessage(
            GetErrorMessageFromExceptionUseCase.execute(throwable)
        )
    }

    override fun sendAPIForFaceScan(
        faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
        authedHeaders: AuthenticatedHeaders,
        request: Enrollment3DRequest
    ) {
        GetEnrollment3DUseCase.execute(
            faceTecRepository,
            authedHeaders,
            request
        ).subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .subscribe({ response ->
                if (response.code == CUS_EKYC_1000) {
                    if (response.data?.wasProcessed == true) {
                        processor.proceedToNextStepFaceScan(
                            faceTecFaceScanResultCallback,
                            response.data.scanResultBlob
                        )
                    } else {
                        ClearTokenUseCase.execute(pref)
                        processor.failCallbackWithMessage(response.description)
                        processor.faceTecCancelFaceScan(faceTecFaceScanResultCallback)
                    }
                } else {
                    ClearTokenUseCase.execute(pref)
                    processor.failCallbackWithMessage(response.description)
                    processor.faceTecCancelFaceScan(faceTecFaceScanResultCallback)
                }
            }, {
                it.printStackTrace()
                ClearTokenUseCase.execute(pref)
                setThrownCallback(it)
                processor.faceTecCancelFaceScan(faceTecFaceScanResultCallback)
            })
    }

    override fun sendAPIForFrontIdCardScan(
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanFrontRequest
    ) {
        GetMatch3D2DUseCase.executeFront(
            faceTecRepository,
            authedHeaders,
            request
        ).subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .subscribe({
                if (it.code == CUS_EKYC_1000
                    || it.code == CUS_EKYC_7102) {
                    if (it.data?.wasProcessed == true) {
                        processor.proceedToNextStepIDScan(
                            faceTecIDScanResultCallback,
                            it.data.scanResultBlob
                        )
                    } else {
                        ClearTokenUseCase.execute(pref)
                        processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        processor.failCallbackWithMessage(it.description)
                    }
                } else {
                    ClearTokenUseCase.execute(pref)
                    processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                    processor.failCallbackWithMessage(it.description)
                }
            }, {
                it.printStackTrace()
                ClearTokenUseCase.execute(pref)
                processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                setThrownCallback(it)
            })
    }

    override fun sendAPIForBackIdCardScan(
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanBackRequest
    ) {
        GetMatch3D2DUseCase.executeBack(
            faceTecRepository,
            authedHeaders,
            request
        )
            .subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .subscribe({ response ->
                if (response.code == CUS_EKYC_1000) {
                    if (pref.enableConfirmInfo) {
                        UserConfirmedValueDisplayMapper().transform(response.data?.documentData)?.let {
                            processor.navigateToReviewInformation(it)
                            processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        }
                    }
                    else {
                        val ocrFormatterUtil = FormatOcrFieldUseCase()
                        ClearTokenUseCase.execute(pref)
                        processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        processor.successCallbackWithMessage(
                            response.description,
                            ocrFormatterUtil.userConfirmValueMapFromOcrData(response.data?.ocrData)
                        )
                    }

                    if (response.data?.wasProcessed == true) {
                        processor.proceedToNextStepIDScan(
                            faceTecIDScanResultCallback,
                            response.data.scanResultBlob
                        )
                    } else {
                        ClearTokenUseCase.execute(pref)
                        processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        processor.failCallbackWithMessage(response.description)
                    }
                } else {
                    ClearTokenUseCase.execute(pref)
                    processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                    processor.failCallbackWithMessage(response.description)
                }

            }, {
                it.printStackTrace()
                ClearTokenUseCase.execute(pref)
                setThrownCallback(it)
                processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
            })
    }
}