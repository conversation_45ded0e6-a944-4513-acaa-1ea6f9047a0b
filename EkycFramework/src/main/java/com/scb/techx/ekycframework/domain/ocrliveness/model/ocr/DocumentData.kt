package com.scb.techx.ekycframework.domain.ocrliveness.model.ocr

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class DocumentData(
    @SerializedName("scannedValues")
    var scannedValues: ScannedValues?,
    @SerializedName("templateInfo")
    var templateInfo: TemplateInfo?,
    @SerializedName("userConfirmedValues")
    var userConfirmedValues: UserConfirmedValues?
)