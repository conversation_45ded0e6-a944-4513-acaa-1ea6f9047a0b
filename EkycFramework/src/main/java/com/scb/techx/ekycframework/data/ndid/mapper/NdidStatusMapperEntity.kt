package com.scb.techx.ekycframework.data.ndid.mapper

import com.scb.techx.ekycframework.data.ndid.model.NdidStatusEntity
import com.scb.techx.ekycframework.data.ndid.model.DataStatusEntity
import com.scb.techx.ekycframework.data.ndid.model.NdidDataStatusEntity
import com.scb.techx.ekycframework.data.ndid.model.IdpStatusEntity
import com.scb.techx.ekycframework.data.ndid.model.NdidErrorStatusEntity
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatusData
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatusNdidData
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidErrorStatus

class NdidStatusMapperEntity {

    fun mapFromEntity(entity: NdidStatusEntity): NdidStatus {
        return NdidStatus(
            entity.code,
            entity.description,
            transformDataEntity(entity.dataEntity)
        )
    }

    private fun transformDataEntity(entity: DataStatusEntity?): NdidStatusData {
        return NdidStatusData(
            entity?.sessionId
                ?: "",
            entity?.referenceId
                ?: "",
            entity?.status
                ?: "",
            entity?.expireTime
                ?: 0,
            transformIdpStatus(entity?.idpEntity),
            transformNdidErrorStatusEntity(entity?.ndidErrorEntity),
            transforNdidStatus(entity?.ndidData)
        )
    }

    private fun transforNdidStatus(entity: NdidDataStatusEntity?): NdidStatusNdidData {
        return NdidStatusNdidData(
            entity?.requestId
                ?: ""
        )
    }

    private fun transformIdpStatus(entity: IdpStatusEntity?): IdpStatus {
        return IdpStatus(
            entity?.nodeId
                ?: "",
            entity?.industryCode
                ?: "",
            entity?.companyCode
                ?: "",
            entity?.shortName
                ?: "",
            entity?.marketingNameTh
                ?: "",
            entity?.marketingNameEn
                ?: "",
            entity?.smallIconPath
                ?: "",
            entity?.mediumIconPath
                ?: "",
            entity?.largeIconPath
                ?: "",
            entity?.deepLinkIos
                ?: "",
            entity?.deepLinkAndroid
                ?: "",
            entity?.deepLinkHuawei
                ?: ""
        )
    }

    private fun transformNdidErrorStatusEntity(entity: NdidErrorStatusEntity?): NdidErrorStatus {
        return NdidErrorStatus(
            entity?.code
                ?: "",
            entity?.description
                ?: "",
            entity?.messageTh
                ?: "",
            entity?.messageEn
                ?: ""
        )
    }
}