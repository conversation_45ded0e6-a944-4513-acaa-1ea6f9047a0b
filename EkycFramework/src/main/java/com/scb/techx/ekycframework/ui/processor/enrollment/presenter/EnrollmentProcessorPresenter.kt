package com.scb.techx.ekycframework.ui.processor.enrollment.presenter

import com.facetec.sdk.FaceTecFaceScanResultCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1000
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.common.usecase.GetErrorMessageFromExceptionUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.GetEnrollment3DUseCase
import io.reactivex.rxjava3.core.Scheduler

class EnrollmentProcessorPresenter (
    private val processor: EnrollmentProcessorContract.Processor,
    private val pref: EkycPreferenceUtil,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val repository: FaceTecRepository
) : EnrollmentProcessorContract.Presenter {
    override fun sendApiForEnrollment3D(
        faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
        authenticatedHeaders: AuthenticatedHeaders,
        request: Enrollment3DRequest
    ) {
        GetEnrollment3DUseCase.execute(
            repository,
            authenticatedHeaders,
            request
        ).subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .subscribe({ response ->
                if (CUS_EKYC_1000 == response.code) {
                    val result = response.data
                    if (result?.wasProcessed == true) {
                        ClearTokenUseCase.execute(pref)
                        processor.proceedToNextStepFaceScan(
                            faceTecFaceScanResultCallback,
                            result.scanResultBlob?: ""
                        )
                    } else {
                        ClearTokenUseCase.execute(pref)
                        processor.faceTecCancelFaceScan(
                            faceTecFaceScanResultCallback,
                            response.description
                        )
                    }
                } else {
                    ClearTokenUseCase.execute(pref)
                    processor.faceTecCancelFaceScan(
                        faceTecFaceScanResultCallback,
                        response.description
                    )
                }
            }, {
                ClearTokenUseCase.execute(pref)
                processor.faceTecCancelFaceScan(
                    faceTecFaceScanResultCallback,
                    GetErrorMessageFromExceptionUseCase.execute(it)
                )
            })
    }
}