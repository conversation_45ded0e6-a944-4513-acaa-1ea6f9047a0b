package com.scb.techx.ekycframework.domain.ocrliveness.model.request

import androidx.annotation.Keep

@Keep
data class ConfirmationInfoRequest(
    val checkExpiredIdCard: Boolean,
    val checkDopa: Boolean,
    val data: Data
) {
    @Keep
    data class Data(
        val nationalId: String,
        val titleTh: String,
        val titleEn: String ,
        val firstNameTh: String,
        val firstNameEn: String,
        val middleNameTh: String,
        val middleNameEn: String,
        val lastNameTh: String,
        val lastNameEn: String,
        val dateOfBirth: String,
        val dateOfIssue: String,
        val dateOfExpiry: String,
        val laserId: String
    )
}
