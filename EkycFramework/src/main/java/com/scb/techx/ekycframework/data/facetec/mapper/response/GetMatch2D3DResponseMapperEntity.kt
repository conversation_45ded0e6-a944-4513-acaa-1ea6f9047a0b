package com.scb.techx.ekycframework.data.facetec.mapper.response

import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DOcrData
import com.scb.techx.ekycframework.data.facetec.model.response.Match3D2DIdScanDataEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Match3D2DIdScanResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Match3D2DOcrDataEntity

class GetMatch2D3DResponseMapperEntity {
    fun mapFromEntity(entity: Match3D2DIdScanResponseEntity): Match3D2DIdScanResponse {
        return Match3D2DIdScanResponse(
            code = entity.code,
            description = entity.description,
            data = entity.data?.let {
                mapMatch3D2DIdScanDataFromEntity(it)
            }
        )
    }

    private fun mapMatch3D2DIdScanDataFromEntity(
        entity: Match3D2DIdScanDataEntity
    ): Match3D2DIdScanData {
        return Match3D2DIdScanData(
            documentData = entity.documentData,
            scanResultBlob = entity.scanResultBlob,
            wasProcessed = entity.wasProcessed,
            ocrData = entity.ocrData?.let {
                mapMatch3D2DIdOcrDataFromEntity(it)
            }
        )
    }

    private fun mapMatch3D2DIdOcrDataFromEntity(
        entity: Match3D2DOcrDataEntity
    ): Match3D2DOcrData {
        return Match3D2DOcrData(
            nationalId = entity.nationalId,
            titleTh = entity.titleTh,
            titleEn = entity.titleEn,
            firstNameTh = entity.firstNameTh,
            middleNameTh = entity.middleNameTh,
            lastNameTh = entity.lastNameTh,
            firstNameEn = entity.firstNameEn,
            middleNameEn = entity.middleNameEn,
            lastNameEn = entity.lastNameEn,
            dateOfBirth = entity.dateOfBirth,
            dateOfBirthFlag = entity.dateOfBirthFlag,
            dateOfIssue = entity.dateOfIssue,
            dateOfExpiry = entity.dateOfExpiry,
            laserId = entity.laserId
        )
    }
}