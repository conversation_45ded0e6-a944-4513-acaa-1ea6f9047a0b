package com.scb.techx.ekycframework.data.facetec.model.response

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class SessionFaceTecResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: SessionFaceTecDataEntity?
)

@Keep
data class SessionFaceTecDataEntity(
    @SerializedName("sessionFaceTec")
    val sessionFaceTec: String?,
    @SerializedName("productionKey")
    val productionKey: String?,
    @SerializedName("deviceKey")
    val deviceKey: String?,
    @SerializedName("encryptionKey")
    val encryptionKey: String?,
)
