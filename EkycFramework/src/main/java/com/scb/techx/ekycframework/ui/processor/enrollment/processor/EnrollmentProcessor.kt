package com.scb.techx.ekycframework.ui.processor.enrollment.processor

import android.content.Context
import com.facetec.sdk.FaceTecFaceScanProcessor
import com.facetec.sdk.FaceTecFaceScanResultCallback
import com.facetec.sdk.FaceTecSDK
import com.facetec.sdk.FaceTecSessionActivity
import com.facetec.sdk.FaceTecSessionResult
import com.facetec.sdk.FaceTecSessionStatus
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.ui.processor.enrollment.presenter.EnrollmentProcessorContract
import com.scb.techx.ekycframework.ui.processor.enrollment.presenter.EnrollmentProcessorPresenter
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.data.facetec.datarepository.FaceTecDataRepository
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.data.facetec.api.FaceTecAPI
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.SUCCESS_MESSAGE
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.USER_CANCELLED
import com.scb.techx.ekycframework.Constants.LIVENESS
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

class EnrollmentProcessor(
    private val context: Context
) : FaceTecFaceScanProcessor, EnrollmentProcessorContract.Processor {

    private val repository: FaceTecRepository by lazy {
        FaceTecDataRepository(GetApiClientUseCase.getApiClient().create(FaceTecAPI::class.java))
    }
    private var pref = EkycPreferenceUtil(context)
    val presenter: EnrollmentProcessorPresenter

    init {
        FaceTecSessionActivity.createAndLaunchSession(
            context,
            this@EnrollmentProcessor,
            pref.sessionFaceTec
        )
        presenter = EnrollmentProcessorPresenter(
            this,
            pref,
            Schedulers.io(),
            AndroidSchedulers.mainThread(),
            repository
        )
    }

    override fun processSessionWhileFaceTecSDKWaits(
        faceTecSessionResult: FaceTecSessionResult,
        faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback
    ) {
        if (faceTecSessionResult.status != FaceTecSessionStatus.SESSION_COMPLETED_SUCCESSFULLY) {
            ClearTokenUseCase.execute(pref)
            HandleCallback.livenessCheckCallback?.onResult(
                success = false,
                description = USER_CANCELLED
            )
            faceTecFaceScanResultCallback.cancel()
            return
        }
        val request = Enrollment3DRequest(
            faceScan = faceTecSessionResult.faceScanBase64,
            auditTrailImage = faceTecSessionResult.auditTrailCompressedBase64[0],
            lowQualityAuditTrailImage = faceTecSessionResult
                .lowQualityAuditTrailCompressedBase64[0],
            function = LIVENESS
        )
        val authenticatedHeaders = ApiMainHeadersProvider().getAuthenticatedHeaders(
            authorization = Config.token,
            deviceKey = pref.deviceKey,
            userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId),
            sessionId = Config.x_session_id,
            tid = GetDeviceSettingUseCase.getUUID(),
            ekycToken = pref.ekycToken,
            correlationId = GetDeviceSettingUseCase.getUUID(),
            sdkVersion = GetDeviceSettingUseCase.getVersionName()
        )
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback,
            authenticatedHeaders,
            request
        )
    }

    override fun proceedToNextStepFaceScan(
        faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
        scanResultBlob: String?,
    ) {
        HandleCallback.livenessCheckCallback?.onResult(true,
            SUCCESS_MESSAGE
        )
        faceTecFaceScanResultCallback?.proceedToNextStep(scanResultBlob ?: "")
    }

    override fun faceTecCancelFaceScan(
        faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
        description: String
    ) {
        HandleCallback.livenessCheckCallback?.onResult(false, description)
        faceTecFaceScanResultCallback?.cancel()
    }
}