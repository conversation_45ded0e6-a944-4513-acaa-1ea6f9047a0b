package com.scb.techx.ekycframework.data.facetec.datarepository

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.SessionFaceTecResponse
import com.scb.techx.ekycframework.data.facetec.api.FaceTecAPI
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.data.facetec.mapper.response.GetConfirmationInfoResponseMapperEntity
import com.scb.techx.ekycframework.data.facetec.mapper.request.GetConfirmationInfoRequestMapperToEntity
import com.scb.techx.ekycframework.data.facetec.mapper.response.GetEnrollment3DResponseMapperEntity
import com.scb.techx.ekycframework.data.facetec.mapper.request.GetEnrollment3DRequestMapperToEntity
import com.scb.techx.ekycframework.data.facetec.mapper.request.GetMatch2D3DRequestMapperToEntity
import com.scb.techx.ekycframework.data.facetec.mapper.response.GetMatch2D3DResponseMapperEntity
import com.scb.techx.ekycframework.data.facetec.mapper.response.GetSessionFaceTecResponseMapperEntity
import com.scb.techx.ekycframework.ui.processor.Config.Companion.GetSessionEndpoint.GET_SESSION_FACETEC_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.LivenessEndpoint.ENROLLMENT_3D_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.OcrEndpoint.CONFIRM_INFO_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.OcrEndpoint.ID_SCAN_ONLY_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.OcrEndpoint.MATCH_3D_2D_IDSCAN_BACK_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.OcrEndpoint.MATCH_3D_2D_IDSCAN_FRONT_ENDPOINT
import io.reactivex.rxjava3.core.Single

class FaceTecDataRepository(private val service: FaceTecAPI): FaceTecRepository {
    private val getConfirmationInfoResponseMapperToEntity = GetConfirmationInfoResponseMapperEntity()
    private val getConfirmationInfoRequestMapperEntity = GetConfirmationInfoRequestMapperToEntity()
    private val getEnrollment3DResponseMapperEntity = GetEnrollment3DResponseMapperEntity()
    private val getEnrollment3DRequestMapperToEntity = GetEnrollment3DRequestMapperToEntity()
    private val getMatch2D3DRequestMapperToEntity = GetMatch2D3DRequestMapperToEntity()
    private val getMatch2D3DResponseMapperEntity = GetMatch2D3DResponseMapperEntity()
    private val getSessionFaceTecResponseMapperEntity = GetSessionFaceTecResponseMapperEntity()

    override fun getConfirmationInfo(
        authedHeaders: AuthenticatedHeaders,
        request: ConfirmationInfoRequest
    ): Single<ConfirmationInfoResponse> {
        return service.getConfirmationInfo(
            CONFIRM_INFO_ENDPOINT,
            authedHeaders,
            getConfirmationInfoRequestMapperEntity.mapToEntity(request)
        ).map {
            getConfirmationInfoResponseMapperToEntity.mapFromEntity(it)
        }
    }

    override fun getEnrollment3D(
        authedHeaders: AuthenticatedHeaders,
        request: Enrollment3DRequest
    ): Single<Enrollment3DResponse> {
        return service.getEnrollment3D(
            ENROLLMENT_3D_ENDPOINT,
            authedHeaders,
            getEnrollment3DRequestMapperToEntity.mapToEntity(request)
        ).map {
            getEnrollment3DResponseMapperEntity.mapFromEntity(it)
        }
    }

    override fun getMatch3D2DIdScanFront(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanFrontRequest
    ): Single<Match3D2DIdScanResponse> {
        return service.getMatch3D2DIdScanFront(
            MATCH_3D_2D_IDSCAN_FRONT_ENDPOINT,
            authedHeaders,
            getMatch2D3DRequestMapperToEntity.mapToEntity(request)
        ).map {
            getMatch2D3DResponseMapperEntity.mapFromEntity(it)
        }
    }

    override fun getMatch3D2DIdScanBack(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanBackRequest
    ): Single<Match3D2DIdScanResponse> {
        return service.getMatch3D2DIdScanBack(
            MATCH_3D_2D_IDSCAN_BACK_ENDPOINT,
            authedHeaders,
            getMatch2D3DRequestMapperToEntity.mapToEntity(request)
        ).map {
            getMatch2D3DResponseMapperEntity.mapFromEntity(it)
        }
    }

    override fun getIdScanOnly(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanFrontRequest
    ): Single<Match3D2DIdScanResponse> {
        return service.getIdScanOnly(
            ID_SCAN_ONLY_ENDPOINT,
            authedHeaders,
            getMatch2D3DRequestMapperToEntity.mapToEntity(request)
        ).map {
            getMatch2D3DResponseMapperEntity.mapFromEntity(it)
        }
    }

    override fun getIdScanOnly(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanBackRequest
    ): Single<Match3D2DIdScanResponse> {
        return service.getIdScanOnly(
            ID_SCAN_ONLY_ENDPOINT,
            authedHeaders,
            getMatch2D3DRequestMapperToEntity.mapToEntity(request)
        ).map {
            getMatch2D3DResponseMapperEntity.mapFromEntity(it)
        }
    }

    override fun getSessionFaceTec(
        authedHeaders: AuthenticatedHeaders
    ): Single<SessionFaceTecResponse> {
        return service.getSessionFaceTec(
            GET_SESSION_FACETEC_ENDPOINT,
            authedHeaders
        ).map {
            getSessionFaceTecResponseMapperEntity.mapFromEntity(it)
        }
    }
}