package com.scb.techx.ekycframework.ui.ndidverification.model

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class NdidVerificationEnrollmentDisplay(
    val referenceId: String,
    val expireTime: Int,
    val marketNameTh: String,
    val marketNameEn: String,
    val smallIconPath: String,
    val largeIconPath: String,
    val deeplinkAndroid: String,
    val deeplinkHuawei: String
) : Parcelable
