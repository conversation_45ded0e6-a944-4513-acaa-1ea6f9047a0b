package com.scb.techx.ekycframework.ui.ocridcard.presenter

import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ocridcard.repository.OcrIdCardRepository
import com.scb.techx.ekycframework.Constants
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowRequest
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.core.Scheduler
import retrofit2.HttpException

class OcrIdCardPresenter(
    private val view: OcrIdCardContract.View,
    private val pref: EkycPreferenceUtil,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val repository: OcrIdCardRepository
) : OcrIdCardContract.Presenter {
    override fun initOcr(ocr: Boolean) {
        if (pref.sdkEncryptionKeyOcr.isEmpty() && pref.sdkEncryptionIv.isEmpty()) {
            val request = InitFlowRequest(ocr)
            repository.getInitFlow(
                ApiMainHeadersProvider().getAuthenticatedHeaders(
                    authorization = Config.token,
                    sessionId = Config.x_session_id,
                    tid = GetDeviceSettingUseCase.getUUID(),
                    ekycToken = pref.ekycToken,
                    correlationId = GetDeviceSettingUseCase.getUUID()
                ),
                request
            ).subscribeOn(processScheduler)
                .observeOn(androidScheduler)
                .doOnSubscribe { view.showLoadingDialog() }
                .doOnSuccess { view.hideLoadingDialog() }
                .doOnError { view.hideLoadingDialog() }
                .subscribe({ response ->
                    when (response.code) {
                        Constants.EkycStatusCode.CUS_EKYC_1000 -> {
                            if (response.data.sdkEncryptionKeyOcr.isNotEmpty() && response.data.sdkEncryptionIv.isNotEmpty()) {
                                pref.sdkEncryptionKeyOcr = response.data.sdkEncryptionKeyOcr
                                pref.sdkEncryptionIv = response.data.sdkEncryptionIv
                                pickFragmentBetweenEnableAndFrontIdScan()
                            } else {
                                pref.sdkEncryptionKeyOcr = ""
                                pref.sdkEncryptionIv = ""
                                view.handleCallback(
                                    success = false,
                                    description = response.description,
                                    userOcrValue = null,
                                    dopaResult = null,
                                    userConfirmedValue = null
                                )
                            }
                        }
                        else -> {
                            view.handleCallback(
                                success = false,
                                description = response.description,
                                userOcrValue = null,
                                dopaResult = null,
                                userConfirmedValue = null
                            )
                        }
                    }
                }, { throwable ->
                    view.handleHttpException(throwable)
                })
        } else if (pref.sdkEncryptionKeyOcr.isNotEmpty() && pref.sdkEncryptionIv.isNotEmpty()) {
            pickFragmentBetweenEnableAndFrontIdScan()
        } else {
            pref.sdkEncryptionKeyOcr = ""
            pref.sdkEncryptionIv = ""
            view.handleCallback(
                success = false,
                description = Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE,
                userOcrValue = null,
                userConfirmedValue = null,
                dopaResult = null
            )
        }
    }

    private fun pickFragmentBetweenEnableAndFrontIdScan() {
        if (view.isCameraEnable()) {
            view.navigateToFrontIdScan()
        } else {
            view.navigateToEnableCamera()
        }
    }

    override fun handleHttpException(throwable: Throwable, view: OcrIdCardContract.View) {
        val error = throwable as HttpException
        ClearTokenUseCase.execute(pref)
        when (error.response()?.code().toString()) {
            Constants.EkycStatusCode.INVALID_TOKEN -> {
                view.handleCallback(
                    success = false,
                    description = Constants.EkycCallbackMessage.INVALID_TOKEN_MESSAGE,
                    userOcrValue = null,
                    userConfirmedValue = null,
                    dopaResult = null
                )
            }
            Constants.EkycStatusCode.TIMEOUT -> {
                view.handleCallback(
                    success = false,
                    description = Constants.EkycCallbackMessage.TIMEOUT_MESSAGE,
                    userOcrValue = null,
                    userConfirmedValue = null,
                    dopaResult = null
                )
            }
            else -> {
                view.handleCallback(
                    success = false,
                    description = Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE,
                    userOcrValue = null,
                    userConfirmedValue = null,
                    dopaResult = null
                )
            }
        }
    }

    override fun start() {
        initOcr(ocr = true)
    }

    override fun stop() {
        // Do nothing
    }
}