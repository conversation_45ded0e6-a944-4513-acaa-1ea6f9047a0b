package com.scb.techx.ekycframework.domain.ocrliveness.usecase

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DResponse
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import io.reactivex.rxjava3.core.Single

class GetEnrollment3DUseCase {
    companion object {
        fun execute(
            repository: FaceTecRepository,
            header: AuthenticatedHeaders,
            request: Enrollment3DRequest
        ): Single<Enrollment3DResponse> {
            return repository.getEnrollment3D(
                authedHeaders = header,
                request = request
            )
        }
    }
}