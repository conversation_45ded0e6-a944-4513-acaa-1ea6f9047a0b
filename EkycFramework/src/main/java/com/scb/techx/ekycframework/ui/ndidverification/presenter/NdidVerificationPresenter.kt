package com.scb.techx.ekycframework.ui.ndidverification.presenter

import android.content.Context
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.Constants.NdidStatus.NDID_ERROR_CODE_40200
import com.scb.techx.ekycframework.Constants.EN_LOCALE
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1000
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_2003
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_3001
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7301
import com.scb.techx.ekycframework.Constants.NdidStatus.AS_DATA_COMPLETED
import com.scb.techx.ekycframework.Constants.NdidStatus.AS_ERRORED
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_CONFIRMED_ACCEPT
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_CONFIRMED_ACCEPT_TIMEOUT
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_CONFIRMED_REJECT
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_ERRORED
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_PENDING
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_PENDING_TIMEOUT
import com.scb.techx.ekycframework.Constants.NdidStatus.NDID_CONNECTION_FAIL
import com.scb.techx.ekycframework.Constants.NdidStatus.USER_SELECT_IDP
import com.scb.techx.ekycframework.ui.base.presenter.BasePresenter
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.domain.ndid.mapper.NdidVerificationEnrollmentDisplayMapper
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.ndid.usecase.GetNdidStatusUseCase
import com.scb.techx.ekycframework.util.EkycUtilities
import io.reactivex.rxjava3.core.Scheduler

class NdidVerificationPresenter(
    private val view: NdidVerificationContract.View,
    private val pref: EkycPreferenceUtil,
    private val context: Context,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val ndidRepository: NdidRepository
) : BasePresenter(pref), NdidVerificationContract.Presenter {
    override fun getNdidStatus() {
        GetNdidStatusUseCase.execute(pref, ndidRepository)
            .subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .doOnSubscribe { view.showLoadingDialog() }
            .doOnSuccess { view.hideLoadingDialog() }
            .doOnError { view.hideLoadingDialog() }
            .subscribe({ response ->
                when (response.code) {
                    CUS_EKYC_3001,
                    CUS_EKYC_1000,
                    CUS_EKYC_7301 -> {
                        when (response.data.status) {
                            USER_SELECT_IDP,
                            IDP_PENDING,
                            IDP_CONFIRMED_ACCEPT -> {
                                view.navigateToEnrollment(
                                    NdidVerificationEnrollmentDisplayMapper().transform(response.data)
                                )
                            }
                            else -> {
                                view.handleNotPendingNdidStatus(response)
                            }
                        }
                    }
                    CUS_EKYC_2003 -> {
                        view.navigateToIdpList()
                    }
                    else -> {
                        view.handleErrorEkyc(response.code)
                    }
                }
            }, { throwable ->
                view.handleHttpException(throwable)
            })
    }

    override fun handleNdidNotPendingStatus(
        response: NdidStatus
    ) {
        when (response.data.status) {
            AS_DATA_COMPLETED -> {
                view.navigateToSuccess(
                    response.description,
                    response.data.status,
                    response.data.referenceId,
                    response.data.ndidStatusNdidData.requestId
                )
            }
            IDP_ERRORED,
            AS_ERRORED -> {
                view.showDialog(
                    if (EN_LOCALE == HandleCallback.language) response.data.ndidError
                        .messageEn else response.data.ndidError.messageTh,
                    true,
                    object : NdidVerificationActivity.PositiveCallback {
                        override fun onPositive() {
                            if (NDID_ERROR_CODE_40200 == response.data.ndidError.code) {
                                view.handleCallback(
                                    false,
                                    response.description,
                                    response.data.status,
                                    EkycUtilities.NdidError(
                                        response.data.ndidError.code
                                    ),
                                    EkycUtilities.NdidData(
                                        response.data.referenceId,
                                        response.data.ndidStatusNdidData.requestId
                                    )
                                )
                            } else {
                                view.navigateToIdpList()
                            }
                        }
                    },
                    null
                )
            }
            IDP_PENDING_TIMEOUT,
            IDP_CONFIRMED_ACCEPT_TIMEOUT,
            NDID_CONNECTION_FAIL-> {
                view.handleCallback(
                    false,
                    response.description,
                    response.data.status,
                    null,
                    EkycUtilities.NdidData(
                        response.data.referenceId,
                        response.data.ndidStatusNdidData.requestId
                    )
                )
            }
            IDP_CONFIRMED_REJECT-> {
                view.showDialog(
                    context.resources.getString(R.string.Ekyc_ndid_holding_idp_reject),
                    true,
                    object : NdidVerificationActivity.PositiveCallback {
                        override fun onPositive() {
                            view.navigateToIdpList()
                        }
                    },
                    null,
                    context.resources.getString(R.string.positive_confirm_dialog_idp_reject),
                    null
                )
            }
            else -> {
                view.navigateToIdpList()
            }
        }
    }

    override fun start() {
        getNdidStatus()
    }

    override fun stop() {
        // Implement Nothing
    }
}