package com.scb.techx.ekycframework.util.facetechelper.presenter

import android.content.Context
import com.facetec.sdk.FaceTecSDK
import com.scb.techx.ekycframework.Constants
import com.scb.techx.ekycframework.domain.common.usecase.*
import com.scb.techx.ekycframework.domain.getsession.usecase.FaceTecGetSessionUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.util.facetechelper.model.FaceTecFeatureType
import io.reactivex.rxjava3.core.Scheduler

class FaceTecInitializeHelperPresenter(
    val helper: FaceTecInitializeHelperContract.Helper,
    val repository: FaceTecRepository
): FaceTecInitializeHelperContract.Presenter {
    private fun handleFaceTecGetSessionThrowError(
        faceTecFeature: FaceTecFeatureType,
        throwable: Throwable,
        pref: EkycPreferenceUtil
    ) {
        throwable.printStackTrace()
        ClearTokenUseCase.execute(pref)
        if (faceTecFeature == FaceTecFeatureType.LIVENESS) {
            helper.handleCallbackFalseEnrollment(GetErrorMessageFromExceptionUseCase.execute(throwable))
        } else {
            helper.handleCallbackFalseOCRLiveness(GetErrorMessageFromExceptionUseCase.execute(throwable))
        }
    }

    override fun openProcessor(
        success: Boolean,
        faceTecFeature: FaceTecFeatureType,
        context: Context,
        isExpiredIdCard: Boolean,
        pref: EkycPreferenceUtil
    ) {
        if (success) {
            if (faceTecFeature == FaceTecFeatureType.LIVENESS) {
                helper.openEnrollmentProcessor(
                    context
                )
            } else if (faceTecFeature == FaceTecFeatureType.OCRLIVENESS)  {
                helper.openOCRLivenessProcessor(
                    context,
                    isExpiredIdCard
                )
            } else {
                helper.openPhotoIDScanProcessor(context)
            }
        } else {
            ClearTokenUseCase.execute(pref)
            if (faceTecFeature == FaceTecFeatureType.LIVENESS) {
                helper.handleCallbackFalseEnrollment(Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE)
            } else {
                helper.handleCallbackFalseOCRLiveness(Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE)
            }
        }
    }

    override fun getSessionFaceTec(
        context: Context,
        faceTecFeature: FaceTecFeatureType,
        isExpiredIdCard: Boolean,
        pref: EkycPreferenceUtil,
        processScheduler: Scheduler,
        androidScheduler: Scheduler
    ) {
        if ((pref.sessionFaceTec.isEmpty() && pref.productionKey.isEmpty() && pref.deviceKey.isEmpty() && pref.encryptionKey.isEmpty()) || Config.isNewEkycToken) {
            FaceTecGetSessionUseCase.execute(pref, context, repository).subscribeOn(processScheduler)
                .observeOn(androidScheduler)
                .subscribe({ response ->
                    if (response.code == Constants.EkycStatusCode.CUS_EKYC_1000) {
                        pref.sessionFaceTec = response.data?.sessionFaceTec.orEmpty()
                        pref.productionKey = response.data?.productionKey.orEmpty()
                        pref.deviceKey = response.data?.deviceKey.orEmpty()
                        pref.encryptionKey = response.data?.encryptionKey?.trimIndent().orEmpty()
                        helper.initializeFaceTecSDK(
                            context,
                            { successful ->
                                openProcessor(successful, faceTecFeature, context, isExpiredIdCard, pref)
                            },
                            pref)
                    } else {
                        ClearTokenUseCase.execute(pref)
                        if (faceTecFeature == FaceTecFeatureType.LIVENESS) {
                            helper.handleCallbackFalseEnrollment(response.description)
                        } else {
                            helper.handleCallbackFalseOCRLiveness(response.description)
                        }
                    }
                }, {
                    handleFaceTecGetSessionThrowError(faceTecFeature, it, pref)
                })
        } else {
            helper.initializeFaceTecSDK(
                context,
                { successful ->
                    openProcessor(successful, faceTecFeature, context, isExpiredIdCard, pref)
                },
                pref
            )
        }
    }
}