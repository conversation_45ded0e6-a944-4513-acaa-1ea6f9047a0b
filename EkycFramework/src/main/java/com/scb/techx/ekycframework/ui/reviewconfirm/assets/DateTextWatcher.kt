package com.scb.techx.ekycframework.ui.reviewconfirm.assets

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.Constants.DOUBLE_DASH
import com.scb.techx.ekycframework.Constants.EMPTY
import com.scb.techx.ekycframework.Constants.NO_VALUE_FIELD_CHAR
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycContract

class DateTextWatcher(
    private val nowEdittext: EditText,
    private val nextEditText: EditText?,
    private val isDateOfBirth: Boolean,
    private val activity: ReviewInformationEkycActivity,
    private val presenter: ReviewInformationEkycContract.Presenter
) : TextWatcher{
    private val lastFieldForBirthDate: EditText by lazy {
        activity.findViewById(R.id.et_year_date_of_birth)
    }

    override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
        // Do nothing
    }

    override fun onTextChanged(text: CharSequence?, p1: Int, p2: Int, p3: Int) {
        nextEditText?.let {
            if ((nowEdittext.text.toString().length == 2
                        || (text.toString() == NO_VALUE_FIELD_CHAR && isDateOfBirth))
                && nextEditText.text.toString().isEmpty()
                && nowEdittext.isFocused
            ) {
                nextEditText.requestFocus()
            }
        }
    }

    override fun afterTextChanged(editable: Editable?) {
        nowEdittext.removeTextChangedListener(this)
        var date = editable.toString()
        date = date.replace("[^0-9-]".toRegex(), EMPTY)
        date = if (isDateOfBirth && nowEdittext != lastFieldForBirthDate) {
            date.replace(DOUBLE_DASH, NO_VALUE_FIELD_CHAR)
        } else {
            date.replace(NO_VALUE_FIELD_CHAR, EMPTY)
        }
        nowEdittext.setText(date)
        try {
            nowEdittext.setSelection(date.length)
        } catch (e: IndexOutOfBoundsException) {
            nowEdittext.setSelection(0)
        }

        nowEdittext.addTextChangedListener(this)
    }
}