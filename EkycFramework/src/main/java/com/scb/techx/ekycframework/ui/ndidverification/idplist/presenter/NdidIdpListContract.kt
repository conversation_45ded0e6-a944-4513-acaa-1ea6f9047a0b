package com.scb.techx.ekycframework.ui.ndidverification.idplist.presenter

import com.scb.techx.ekycframework.ui.base.BasePresenter
import com.scb.techx.ekycframework.ui.base.BaseView
import com.scb.techx.ekycframework.ui.ndidverification.model.NdidVerificationEnrollmentDisplay
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpData

interface NdidIdpListContract {

    interface View : BaseView {
        fun callBackIdpResponse(response: IdpData?)
        fun navigateToCountDown(ndidVerificationEnrollmentDisplay: NdidVerificationEnrollmentDisplay)
        fun handleErrorEkyc(code: String)
        fun showLoadingDialog()
        fun hideLoadingDialog()
    }

    interface Presenter : BasePresenter {
        fun getIdpList()
        fun getNdidRequest(idpNodeId: String, idpCompanyCode: String, idpRegistered: <PERSON><PERSON><PERSON>, idpIndustryCode: String)
    }

}