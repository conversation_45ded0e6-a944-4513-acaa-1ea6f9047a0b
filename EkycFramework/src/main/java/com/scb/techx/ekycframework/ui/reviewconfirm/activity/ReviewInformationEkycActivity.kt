package com.scb.techx.ekycframework.ui.reviewconfirm.activity

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.ContextThemeWrapper
import android.view.View
import android.view.inputmethod.EditorInfo.IME_ACTION_DONE
import android.view.inputmethod.InputMethodManager
import android.widget.RelativeLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.Button
import android.widget.CheckBox
import android.widget.ImageButton
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import com.facetec.sdk.FaceTecSDK
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.data.facetec.datarepository.FaceTecDataRepository
import com.scb.techx.ekycframework.data.facetec.api.FaceTecAPI
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.Constants.DATE_SPLITTER
import com.scb.techx.ekycframework.Constants.EMPTY
import com.scb.techx.ekycframework.Constants.EXPIRE_DATE
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.USER_CANCELLED
import com.scb.techx.ekycframework.Constants.ID_SEPARATE_CHAR
import com.scb.techx.ekycframework.Constants.LASER_ID_FORMAT
import com.scb.techx.ekycframework.Constants.NATIONAL_ID_FORMAT
import com.scb.techx.ekycframework.Constants.NO_VALUE_FIELD_CHAR
import com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.DateTextWatcher
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.IDTextWatcher
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.MandatoryFieldTextWatcher
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.OptionalFieldTextWatcher
import com.scb.techx.ekycframework.ui.reviewconfirm.helper.KeyboardHandlingHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.helper.ReviewTextFieldErrorSettingHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.helper.ReviewThemeHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycContract
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycPresenter
import com.scb.techx.ekycframework.ui.assets.EkycLoadingAlert
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.FormatOcrFieldUseCase
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.Locale

class ReviewInformationEkycActivity :
    AppCompatActivity(),
    ReviewInformationEkycContract.View
{
    private val loadingAlert = EkycLoadingAlert(this)
    private var userConfirmedValueDisplay: UserConfirmedValueDisplay? = null
    private var dopaResult: DopaResult? = null
    lateinit var presenter: ReviewInformationEkycPresenter
    private var isCheckExpired = true
    val pref by lazy { EkycPreferenceUtil(this) }
    private val themeHelper by lazy { ReviewThemeHelper(presenter, this) }
    private val errorSettingHelper by lazy { ReviewTextFieldErrorSettingHelper(this,presenter) }

    private val ocrFormatterUtil: FormatOcrFieldUseCase by lazy { FormatOcrFieldUseCase() }
    private val rlRoot: RelativeLayout by lazy { findViewById(R.id.rl_root) }
    private val rlReview: RelativeLayout by lazy { findViewById(R.id.rl_review) }
    private val ibCancel: ImageButton by lazy { findViewById(R.id.ib_cancel) }
    private val tvMainHeader: TextView by lazy { findViewById(R.id.tv_main_header) }
    private val vSectionHeader: View by lazy { findViewById(R.id.v_section_header) }
    private val tvSectionHeader: TextView by lazy { findViewById(R.id.tv_section_header) }
    private val tvTitleEn: TextView by lazy { findViewById(R.id.tv_title_en) }
    private val tvFirstNameEn: TextView by lazy { findViewById(R.id.tv_firstname_en) }
    private val tvMiddleNameEn: TextView by lazy { findViewById(R.id.tv_middle_en) }
    private val tvLastNameEn: TextView by lazy { findViewById(R.id.tv_lastname_en) }
    private val tvTitleTh: TextView by lazy { findViewById(R.id.tv_title_th) }
    private val tvFirstNameTh: TextView by lazy { findViewById(R.id.tv_firstname_th) }
    private val tvMiddleNameTh: TextView by lazy { findViewById(R.id.tv_middle_th) }
    private val tvLastNameTh: TextView by lazy { findViewById(R.id.tv_lastname_th) }
    private val tvDateOfBirth: TextView by lazy { findViewById(R.id.tv_date_of_birth) }
    private val tvIssuedDate: TextView by lazy { findViewById(R.id.tv_issued_date) }
    private val tvExpirationDate: TextView by lazy { findViewById(R.id.tv_expiration_date) }
    private val tvNationalId: TextView by lazy { findViewById(R.id.tv_id_number) }
    private val tvLaserId: TextView by lazy { findViewById(R.id.tv_laser_id) }
    private val teTitleEn: TextInputEditText by lazy { findViewById(R.id.te_title_en) }
    private val teTitleTh: TextInputEditText by lazy { findViewById(R.id.te_title_th) }
    private val teFirstNameEn: TextInputEditText by lazy { findViewById(R.id.te_firstname_en) }
    private val teLastNameEn: TextInputEditText by lazy { findViewById(R.id.te_lastname_en) }
    private val teMiddleNameEn: TextInputEditText by lazy { findViewById(R.id.te_middle_en) }
    private val teFirstNameTh: TextInputEditText by lazy { findViewById(R.id.te_firstname_th) }
    private val teLastNameTh: TextInputEditText by lazy { findViewById(R.id.te_lastname_th) }
    private val teMiddleNameTh: TextInputEditText by lazy { findViewById(R.id.te_middle_th) }
    private val etDayDateOfBirth: EditText by lazy { findViewById(R.id.et_day_date_of_birth) }
    private val etMonthDateOfBirth: EditText by lazy { findViewById(R.id.et_month_date_of_birth) }
    private val etYearDateOfBirth: EditText by lazy { findViewById(R.id.et_year_date_of_birth) }
    private val llErrorDateOfBirth: LinearLayout by lazy { findViewById(R.id.ll_error_date_of_birth) }
    private val tvErrorDateOfBirth: TextView by lazy { findViewById(R.id.tv_error_date_of_birth) }
    private val etDayDateOfIssued: EditText by lazy { findViewById(R.id.et_day_date_of_issued) }
    private val etMonthDateOfIssued: EditText by lazy { findViewById(R.id.et_month_date_of_issued) }
    private val etYearDateOfIssued: EditText by lazy { findViewById(R.id.et_year_date_of_issued) }
    private val llErrorDateOfIssued: LinearLayout by lazy { findViewById(R.id.ll_error_date_of_issued) }
    private val tvErrorDateOfIssued: TextView by lazy { findViewById(R.id.tv_error_date_of_issued) }
    private val etDayDateOfExpire: EditText by lazy { findViewById(R.id.et_day_date_of_expiration) }
    private val etMonthDateOfExpire: EditText by lazy { findViewById(R.id.et_month_date_of_expiration) }
    private val etYearDateOfExpire: EditText by lazy { findViewById(R.id.et_year_date_of_expiration) }
    private val llErrorDateOfExpire: LinearLayout by lazy { findViewById(R.id.ll_error_date_of_expiration) }
    private val tvErrorDateOfExpire: TextView by lazy { findViewById(R.id.tv_error_date_of_expiration) }
    private val teNationalId: TextInputEditText by lazy { findViewById(R.id.te_id_number) }
    private val teLaserId: TextInputEditText by lazy { findViewById(R.id.te_laser_id) }
    private val tlTitleEn: TextInputLayout by lazy { findViewById(R.id.tl_title_en) }
    private val tlTitleTh: TextInputLayout by lazy { findViewById(R.id.tl_title_th) }
    private val tlFirstNameEn: TextInputLayout by lazy { findViewById(R.id.tl_firstname_en) }
    private val tlFirstNameTh: TextInputLayout by lazy { findViewById(R.id.tl_firstname_th) }
    private val tlLastNameEn: TextInputLayout by lazy { findViewById(R.id.tl_lastname_en) }
    private val tlLastNameTh: TextInputLayout by lazy { findViewById(R.id.tl_lastname_th) }
    private val tlMiddleEn: TextInputLayout by lazy { findViewById(R.id.tl_middle_en) }
    private val tlMiddleTh: TextInputLayout by lazy { findViewById(R.id.tl_middle_th) }
    private val cbExpirationDate: CheckBox by lazy { findViewById(R.id.cb_expiration) }
    private val tlNationalId: TextInputLayout by lazy { findViewById(R.id.tl_id_number) }
    private val tlLaserId: TextInputLayout by lazy { findViewById(R.id.tl_laser_id) }
    private val tvConfirm: TextView by lazy { findViewById(R.id.tv_confirm) }
    private val btConfirm: Button by lazy { findViewById(R.id.bt_confirm) }
    private val ivLogo: ImageView by lazy { findViewById(R.id.iv_ndid_bank_logo_tile) }

    private var headersProvider = ApiMainHeadersProvider()
    private val keyboardHandlingHelper = KeyboardHandlingHelper(this)

    companion object {
        private const val OCR_FIELDS_RESULT = "OcrFieldsResult"
        private const val CHECK_EXPIRED_DATE = "CheckExpiredDate"

        @JvmStatic
        fun startActivity(
            context: Context?,
            userConfirmedValueDisplay: UserConfirmedValueDisplay,
            checkExpiredDate: Boolean,
        ) {
            context?.let {
                val intent = Intent(context, ReviewInformationEkycActivity::class.java)
                intent.putExtra(OCR_FIELDS_RESULT, userConfirmedValueDisplay)
                intent.putExtra(CHECK_EXPIRED_DATE, checkExpiredDate)
                it.startActivity(intent)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_review_information)
        keyboardHandlingHelper.setUpCloseButton(
            {showButton(false)},
            {showButton(true)}
        )
        presenter = ReviewInformationEkycPresenter(
            this,
            pref,
            this,
            Schedulers.io(),
            AndroidSchedulers.mainThread(),
            FaceTecDataRepository(
                GetApiClientUseCase.getApiClient().create(FaceTecAPI::class.java)
            )
        )
        supportActionBar?.hide()
        HandleCallback.language?.let {
            val locale = Locale(it)
            val config = this.resources.configuration
            config.setLocale(locale)
            this.resources.updateConfiguration(config, this.resources.displayMetrics)
        }
        userConfirmedValueDisplay = intent.getParcelableExtra(OCR_FIELDS_RESULT)
        isCheckExpired = intent.getBooleanExtra(CHECK_EXPIRED_DATE, true)
        themeHelper.setTheme()
        setText()

        userConfirmedValueDisplay?.let { ocrResult ->
            val prefillOcr = presenter.handleOcrPrefill(ocrResult)
            initUserConfirmedNameText(prefillOcr)
            initUserConfirmedIdCardText(prefillOcr)
            initUserConfirmedDateText(prefillOcr)
        }

        allEnFieldOnTextChangeSetting()
        allThFieldOnTextChangeSetting()
        allIdFieldOnTextChangeSetting()

        setAllImeAction()
        setBirthDateChangeFocus()
        setIssueDateChangeFocus()
        setExpireDateChangeFocus()

        keyboardHandlingHelper.setUpCloseKeyboardWhenPressOutsideEditText(this, rlRoot)

        checkEnabledButton()

        cbExpirationDate.setOnCheckedChangeListener { _, _ ->
            presenter.setUpNoExpirationDateCheckBoxOnCheck(cbExpirationDate.isChecked)
        }

        ibCancel.setOnClickListener {
            HandleCallback.ocrResultsCallback?.onSuccess(
                success = false,
                description = USER_CANCELLED,
                userOcrValue = null,
                userConfirmedValue = null
            )
            finish()
        }
        ibCancel.bringToFront()

        btConfirm.setOnClickListener {
            submitUserConfirmedValue()
        }
    }

    private fun showButton(show: Boolean) {
        if (show) {
            rlReview.requestFocus()
            rlReview.onFocusChangeListener
            rlReview.clearFocus()
            checkEnabledButton()
            keyboardHandlingHelper.hideSoftKeyboard(this)
            btConfirm.visibility = View.VISIBLE
        } else {
            btConfirm.visibility = View.GONE
        }
    }

    override fun onBackPressed() {
        currentFocus?.clearFocus()
    }

    private fun initUserConfirmedNameText(ocrResult: UserConfirmedValueDisplay) {
        if (!ocrResult.titleEn.isNullOrEmpty()) {
            teTitleEn.setText(ocrResult.titleEn?.trim())
            errorSettingHelper.setErrorText(
                presenter.determineValidateTypeTitleEnglish(teTitleEn.text.toString()),
                teTitleEn,
                tlTitleEn,
                true
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teTitleEn,
                tlTitleEn,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.titleTh.isNullOrEmpty()) {
            teTitleTh.setText(ocrResult.titleTh?.trim())
            errorSettingHelper.setErrorText(
                presenter.determineValidateTypeTitleThai(teTitleTh.text.toString()),
                teTitleTh,
                tlTitleTh,
                false
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teTitleTh,
                tlTitleTh,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.firstNameEn.isNullOrEmpty()) {
            teFirstNameEn.setText(ocrResult.firstNameEn?.trim())
            errorSettingHelper.setErrorText(
                presenter.determineValidateTypeTextEnglish(teFirstNameEn.text.toString()),
                teFirstNameEn,
                tlFirstNameEn,
                true
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teFirstNameEn,
                tlFirstNameEn,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.firstNameTh.isNullOrEmpty()) {
            teFirstNameTh.setText(ocrResult.firstNameTh?.trim())
            errorSettingHelper.setErrorText(
                presenter.determineValidateTypeTextThai(teFirstNameTh.text.toString()),
                teFirstNameTh,
                tlFirstNameTh,
                false
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teFirstNameTh,
                tlFirstNameTh,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.lastNameEn.isNullOrEmpty()) {
            teLastNameEn.setText(ocrResult.lastNameEn?.trim())
            errorSettingHelper.setErrorText(
                presenter.determineValidateTypeTextEnglish(teLastNameEn.text.toString()),
                teLastNameEn,
                tlLastNameEn,
                true
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teLastNameEn,
                tlLastNameEn,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.lastNameTh.isNullOrEmpty()) {
            teLastNameTh.setText(ocrResult.lastNameTh?.trim())
            errorSettingHelper.setErrorText(
                presenter.determineValidateTypeTextThai(teLastNameTh.text.toString()),
                teLastNameTh,
                tlLastNameTh,
                false
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teLastNameTh,
                tlLastNameTh,
                resources.getString(R.string.Ekyc_review_require)
            )
        }
    }

    private fun initUserConfirmedIdCardText(ocrResult: UserConfirmedValueDisplay) {
        if (!ocrResult.nationalId.isNullOrEmpty()) {
            teNationalId.setText(ocrFormatterUtil.formatNationalId(ocrResult.nationalId))
            errorSettingHelper.setErrorNationalId(
                presenter.determineValidateTypeNationalId(teNationalId.text.toString()),
                teNationalId,
                tlNationalId,
                false
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teNationalId,
                tlNationalId,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.laserId.isNullOrEmpty()) {
            teLaserId.setText(ocrFormatterUtil.formatLaserId(ocrResult.laserId))
            errorSettingHelper.setErrorLaserId(
                presenter.determineValidateTypeLaserId(teLaserId.text.toString()), teLaserId, tlLaserId
            )
        } else {
            errorSettingHelper.setFieldAsError(
                teLaserId,
                tlLaserId,
                resources.getString(R.string.Ekyc_review_require)
            )
        }
    }

    private fun initUserConfirmedDateText(ocrResult: UserConfirmedValueDisplay) {
        if (!ocrResult.dateOfBirth.isNullOrEmpty()) {
            val dateOfBirthFormattedString = ocrFormatterUtil.formatDate(ocrResult.dateOfBirth, true)
            try {
                val dateOfBirthList = dateOfBirthFormattedString.split(DATE_SPLITTER, limit = 3)
                etDayDateOfBirth.setText(dateOfBirthList[0])
                etMonthDateOfBirth.setText(dateOfBirthList[1])
                etYearDateOfBirth.setText(dateOfBirthList[2])
            }
            catch (e: IndexOutOfBoundsException) {
                etDayDateOfBirth.setText("")
                etMonthDateOfBirth.setText("")
                etYearDateOfBirth.setText("")
            }
            errorSettingHelper.setGoneErrorDateOfBirth(
                etDayDateOfBirth,
                etMonthDateOfBirth,
                etYearDateOfBirth,
                tvErrorDateOfBirth,
                llErrorDateOfBirth
            )
        } else {
            errorSettingHelper.setDateFieldAsError(
                etDayDateOfBirth,
                etMonthDateOfBirth,
                etYearDateOfBirth,
                tvErrorDateOfBirth,
                llErrorDateOfBirth,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.dateOfIssue.isNullOrEmpty()) {
            val issuedDateFormattedString = ocrFormatterUtil.formatDate(ocrResult.dateOfIssue, true)
            val dateOfIssuedList = issuedDateFormattedString.split(DATE_SPLITTER, limit = 3)
            if (dateOfIssuedList.size == 3) {
                etDayDateOfIssued.setText(dateOfIssuedList[0])
                etMonthDateOfIssued.setText(dateOfIssuedList[1])
                etYearDateOfIssued.setText(dateOfIssuedList[2])
                errorSettingHelper.setGoneErrorDateOfIssued(
                    etDayDateOfIssued,
                    etMonthDateOfIssued,
                    etYearDateOfIssued,
                    tvErrorDateOfIssued,
                    llErrorDateOfIssued
                )
            } else {
                etDayDateOfIssued.setText(EMPTY)
                etMonthDateOfIssued.setText(EMPTY)
                etYearDateOfIssued.setText(EMPTY)
                errorSettingHelper.setDateFieldAsError(
                    etDayDateOfIssued,
                    etMonthDateOfIssued,
                    etYearDateOfIssued,
                    tvErrorDateOfIssued,
                    llErrorDateOfIssued,
                    resources.getString(R.string.Ekyc_review_require)
                )
            }
        } else {
            errorSettingHelper.setDateFieldAsError(
                etDayDateOfIssued,
                etMonthDateOfIssued,
                etYearDateOfIssued,
                tvErrorDateOfIssued,
                llErrorDateOfIssued,
                resources.getString(R.string.Ekyc_review_require)
            )
        }

        if (!ocrResult.dateOfExpiry.isNullOrEmpty()) {
            val dateExpiryFormattedString =
                ocrFormatterUtil.formatExpirationDate(ocrResult.dateOfExpiry)
            val dateOfExpireList = dateExpiryFormattedString.split(DATE_SPLITTER, limit = 3)
            try {
                etDayDateOfExpire.setText(dateOfExpireList[0])
                etMonthDateOfExpire.setText(dateOfExpireList[1])
                etYearDateOfExpire.setText(dateOfExpireList[2])
                errorSettingHelper.setGoneErrorDateOfExpire(
                    etDayDateOfExpire,
                    etMonthDateOfExpire,
                    etYearDateOfExpire,
                    tvErrorDateOfExpire,
                    llErrorDateOfExpire,
                    isCheckExpired
                )
            } catch (e: IndexOutOfBoundsException) {
                if (FormatOcrFieldUseCase.LIFELONG == dateOfExpireList[0]) {
                    cbExpirationDate.isChecked = true
                    disableExpiration()
                } else {
                    etDayDateOfExpire.setText(EMPTY)
                    etMonthDateOfExpire.setText(EMPTY)
                    etYearDateOfExpire.setText(EMPTY)
                    setErrorDateOfExpire(resources.getString(R.string.Ekyc_review_require))
                }
            }
        } else {
            setErrorDateOfExpire(resources.getString(R.string.Ekyc_review_require))
        }
    }

    private fun setIssueDateChangeFocus() {
        etDayDateOfIssued.addTextChangedListener(
            DateTextWatcher(
                etDayDateOfIssued,
                etMonthDateOfIssued,
                false,
                this,
                presenter
            )
        )

        etMonthDateOfIssued.addTextChangedListener(
            DateTextWatcher(
                etMonthDateOfIssued,
                etYearDateOfIssued,
                false,
                this,
                presenter
            )
        )

        etYearDateOfIssued.addTextChangedListener(
            DateTextWatcher(
                etYearDateOfIssued,
                null,
                false,
                this,
                presenter
            )
        )

        etDayDateOfIssued.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etDayDateOfIssued.setText(EMPTY)
            }
            if (!focused) {
                checkDateMonthString(etDayDateOfIssued.text.toString(), etDayDateOfIssued)
                errorSettingHelper.setGoneErrorDateOfIssued(
                    etDayDateOfIssued,
                    etMonthDateOfIssued,
                    etYearDateOfIssued,
                    tvErrorDateOfIssued,
                    llErrorDateOfIssued
                )
                checkEnabledButton()
            }
        }

        etMonthDateOfIssued.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etMonthDateOfIssued.setText(EMPTY)
            }
            if (!focused) {
                checkDateMonthString(etMonthDateOfIssued.text.toString(), etMonthDateOfIssued)
                errorSettingHelper.setGoneErrorDateOfIssued(
                    etDayDateOfIssued,
                    etMonthDateOfIssued,
                    etYearDateOfIssued,
                    tvErrorDateOfIssued,
                    llErrorDateOfIssued
                )
                checkEnabledButton()
            }
        }

        etYearDateOfIssued.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etYearDateOfIssued.setText(EMPTY)
            }
            if (!focused) {
                errorSettingHelper.setGoneErrorDateOfIssued(
                    etDayDateOfIssued,
                    etMonthDateOfIssued,
                    etYearDateOfIssued,
                    tvErrorDateOfIssued,
                    llErrorDateOfIssued
                )
                checkEnabledButton()
            }
        }
    }

    private fun setBirthDateChangeFocus() {
        etDayDateOfBirth.addTextChangedListener(
            DateTextWatcher(
                etDayDateOfBirth,
                etMonthDateOfBirth,
                true,
                this,
                presenter
            )
        )

        etMonthDateOfBirth.addTextChangedListener(
            DateTextWatcher(
                etMonthDateOfBirth,
                etYearDateOfBirth,
                true,
                this,
                presenter
            )
        )

        etYearDateOfBirth.addTextChangedListener(
            DateTextWatcher(
                etYearDateOfBirth,
                null,
                true,
                this,
                presenter
            )
        )

        etDayDateOfBirth.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etDayDateOfBirth.setText(EMPTY)
            }
            if (!focused) {
                checkDateMonthString(etDayDateOfBirth.text.toString(), etDayDateOfBirth)
                errorSettingHelper.setGoneErrorDateOfBirth(
                    etDayDateOfBirth,
                    etMonthDateOfBirth,
                    etYearDateOfBirth,
                    tvErrorDateOfBirth,
                    llErrorDateOfBirth
                )
                checkEnabledButton()
            }
        }

        etMonthDateOfBirth.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etMonthDateOfBirth.setText(EMPTY)
            }
            if (!focused) {
                checkDateMonthString(etMonthDateOfBirth.text.toString(), etMonthDateOfBirth)
                errorSettingHelper.setGoneErrorDateOfBirth(
                    etDayDateOfBirth,
                    etMonthDateOfBirth,
                    etYearDateOfBirth,
                    tvErrorDateOfBirth,
                    llErrorDateOfBirth
                )
                checkEnabledButton()
            }
        }

        etYearDateOfBirth.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etYearDateOfBirth.setText(EMPTY)
            }
            if (!focused) {
                errorSettingHelper.setGoneErrorDateOfBirth(
                    etDayDateOfBirth,
                    etMonthDateOfBirth,
                    etYearDateOfBirth,
                    tvErrorDateOfBirth,
                    llErrorDateOfBirth
                )
                checkEnabledButton()
            }
        }
    }

    private fun setExpireDateChangeFocus() {
        etDayDateOfExpire.addTextChangedListener(
            DateTextWatcher(
                etDayDateOfExpire,
                etMonthDateOfExpire,
                false,
                this,
                presenter
            )
        )

        etMonthDateOfExpire.addTextChangedListener(
            DateTextWatcher(
                etMonthDateOfExpire,
                etYearDateOfExpire,
                false,
                this,
                presenter
            )
        )

        etYearDateOfExpire.addTextChangedListener(
            DateTextWatcher(
                etYearDateOfExpire,
                null,
                false,
                this,
                presenter
            )
        )

        etDayDateOfExpire.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etDayDateOfExpire.setText(EMPTY)
            }
            if (!focused) {
                checkDateMonthString(etDayDateOfExpire.text.toString(), etDayDateOfExpire)
                errorSettingHelper.setGoneErrorDateOfExpire(
                    etDayDateOfExpire,
                    etMonthDateOfExpire,
                    etYearDateOfExpire,
                    tvErrorDateOfExpire,
                    llErrorDateOfExpire,
                    isCheckExpired
                )
                checkEnabledButton()
            }
        }

        etMonthDateOfExpire.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etMonthDateOfExpire.setText(EMPTY)
            }
            if (!focused) {
                checkDateMonthString(etMonthDateOfExpire.text.toString(), etMonthDateOfExpire)
                errorSettingHelper.setGoneErrorDateOfExpire(
                    etDayDateOfExpire,
                    etMonthDateOfExpire,
                    etYearDateOfExpire,
                    tvErrorDateOfExpire,
                    llErrorDateOfExpire,
                    isCheckExpired
                )
                checkEnabledButton()
            }
        }

        etYearDateOfExpire.setOnFocusChangeListener { _, focused ->
            if (focused) {
                checkEnabledButton()
                etYearDateOfExpire.setText(EMPTY)
            }
            if (!focused) {
                errorSettingHelper.setGoneErrorDateOfExpire(
                    etDayDateOfExpire,
                    etMonthDateOfExpire,
                    etYearDateOfExpire,
                    tvErrorDateOfExpire,
                    llErrorDateOfExpire,
                    isCheckExpired
                )
                checkEnabledButton()
            }
        }
    }

    private fun allEnFieldOnTextChangeSetting() {
        teTitleEn.addTextChangedListener(
            MandatoryFieldTextWatcher(
                editText = teTitleEn,
                textInputLayout = tlTitleEn,
                isTitle = true,
                isEnglish = true,
                activity = this,
                presenter = presenter
            )
        )

        teFirstNameEn.addTextChangedListener(
            MandatoryFieldTextWatcher(
                editText = teFirstNameEn,
                textInputLayout = tlFirstNameEn,
                isTitle = false,
                isEnglish = true,
                activity = this,
                presenter = presenter
            )
        )

        teMiddleNameEn.addTextChangedListener(
            OptionalFieldTextWatcher(
                editText = teMiddleNameEn,
                textInputLayout = tlMiddleEn,
                isEnglish = true,
                activity = this,
                presenter = presenter
            )
        )

        teLastNameEn.addTextChangedListener(
            MandatoryFieldTextWatcher(
                editText = teLastNameEn,
                textInputLayout = tlLastNameEn,
                isTitle = false,
                isEnglish = true,
                activity =  this,
                presenter = presenter
            )
        )
    }

    private fun allThFieldOnTextChangeSetting() {
        teTitleTh.addTextChangedListener(
            MandatoryFieldTextWatcher(
                editText = teTitleTh,
                textInputLayout = tlTitleTh,
                isTitle = true,
                isEnglish = false,
                activity = this,
                presenter = presenter
            )
        )

        teFirstNameTh.addTextChangedListener(
            MandatoryFieldTextWatcher(
                editText = teFirstNameTh,
                textInputLayout = tlFirstNameTh,
                isTitle = false,
                isEnglish = false,
                activity = this,
                presenter = presenter
            )
        )

        teMiddleNameTh.addTextChangedListener(
            OptionalFieldTextWatcher(
                editText = teMiddleNameTh,
                textInputLayout = tlMiddleTh,
                isEnglish = false,
                activity = this,
                presenter = presenter
            )
        )

        teLastNameTh.addTextChangedListener(
            MandatoryFieldTextWatcher(
                editText = teLastNameTh,
                textInputLayout = tlLastNameTh,
                isTitle = false,
                isEnglish = false,
                activity = this,
                presenter = presenter
            )
        )
    }

    private fun allIdFieldOnTextChangeSetting() {
        teNationalId.addTextChangedListener(
            IDTextWatcher(
                teNationalId,
                NATIONAL_ID_FORMAT,
                tlNationalId,
                this,
                presenter
            )
        )

        teLaserId.addTextChangedListener(
            IDTextWatcher(
                teLaserId,
                LASER_ID_FORMAT,
                tlLaserId,
                this,
                presenter
            )
        )
    }

    private fun setAllImeAction() {
        setImeAction(teNationalId, teTitleTh)
        setImeAction(teTitleTh, teFirstNameTh)
        setImeAction(teFirstNameTh, teMiddleNameTh)
        setImeAction(teMiddleNameTh, teLastNameTh)
        setImeAction(teLastNameTh, teTitleEn)
        setImeAction(teTitleEn, teFirstNameEn)
        setImeAction(teFirstNameEn, teMiddleNameEn)
        setImeAction(teMiddleNameEn, teLastNameEn)
        setImeAction(teLastNameEn, etDayDateOfBirth)
        setImeAction(etDayDateOfBirth, etMonthDateOfBirth)
        setImeAction(etMonthDateOfBirth, etYearDateOfBirth)
        setImeAction(etYearDateOfBirth, etDayDateOfIssued)
        setImeAction(etDayDateOfIssued, etMonthDateOfIssued)
        setImeAction(etMonthDateOfIssued, etYearDateOfIssued)
        etYearDateOfIssued.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == IME_ACTION_DONE) {
                if (!etDayDateOfExpire.isEnabled && teLaserId.text.toString().isEmpty()) {
                    teLaserId.requestFocus()
                } else if (etDayDateOfExpire.isEnabled && etDayDateOfExpire.text.toString()
                        .isEmpty()
                ) {
                    etDayDateOfExpire.requestFocus()
                } else {
                    etYearDateOfIssued.clearFocus()
                }
            }
            false
        }
        setImeAction(etDayDateOfExpire, etMonthDateOfExpire)
        setImeAction(etMonthDateOfExpire, etYearDateOfExpire)
        setImeAction(etYearDateOfExpire, teLaserId)
        setImeAction(teLaserId, null)
    }

    override fun showEkycErrorDialog(message: String, dopaData: ConfirmationInfoResponse) {
        val builder = AlertDialog.Builder(ContextThemeWrapper(this, R.style.PopupStyle))
        builder.setMessage(message)
            .setPositiveButton(resources.getString(R.string.Ekyc_review_confirm_dialog)) { dialogInterface, _ ->
                presenter.decideLogicForPopupPositiveButton(dopaData, dialogInterface)
            }.setCancelable(false).show()
    }

    override fun dismissDialog(dialogInterface: DialogInterface) {
        dialogInterface.dismiss()
    }

    override fun callBackAfterFailDopa(
        dopaData: ConfirmationInfoResponse,
        dialogInterface: DialogInterface
    ) {
        ClearTokenUseCase.execute(pref)
        dopaResult =
            DopaResult(code = dopaData.data?.dopaCode, dopaData.data?.dopaDesc)
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = false,
            description = dopaData.description,
            userOcrValue = null,
            userConfirmedValue = dopaData.data?.userConfirmedValue,
            dopaResult = dopaResult
        )
        dismissDialog(dialogInterface)
        finish()
    }

    override fun callBackToClient(description: String) {
        ClearTokenUseCase.execute(pref)
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = false,
            description = description,
            userOcrValue = null,
            userConfirmedValue = null
        )
        finish()
    }

    override fun showLoadingDialog() {
        loadingAlert.startLoading()
    }

    override fun hideLoadingDialog() {
        loadingAlert.dismissLoading()
    }

    override fun confirmationInfoCallback(data: ConfirmationInfoResponse) {
        presenter.decideTypeOfSuccessCallBack(data, Config.checkDopa)
        ClearTokenUseCase.execute(pref)
        finish()
    }

    override fun callbackWithDopa(data: ConfirmationInfoResponse) {
        dopaResult = DopaResult(data.data?.dopaCode ?: EMPTY, data.data?.dopaDesc ?: EMPTY)
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = true,
            description = data.description,
            userOcrValue = null,
            userConfirmedValue = data.data?.userConfirmedValue,
            dopaResult = dopaResult
        )
    }

    override fun callbackWithoutDopa(data: ConfirmationInfoResponse) {
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = true,
            description = data.description,
            userOcrValue = null,
            userConfirmedValue = data.data?.userConfirmedValue
        )
    }

    private fun setImeAction(nowEdittext: EditText, nextEditText: EditText?) {
        val imm = this.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        nowEdittext.setOnEditorActionListener { v, actionId, _ ->
            if (actionId == IME_ACTION_DONE) {
                if (nextEditText?.text.toString().isNotEmpty()) {
                    nowEdittext.clearFocus()
                    imm.hideSoftInputFromWindow(v.windowToken, 0)
                    checkEnabledButton()
                } else {
                    nextEditText?.requestFocus()
                }
                true
            } else {
                false
            }
        }
    }

    private fun checkDateMonthString(text: String, editText: EditText) {
        if (text.length < 2 && text.matches("[0-9]".toRegex())) {
            editText.setText("0${text}")
        }
    }

    override fun setErrorDateOfExpire(error: String) {
        errorSettingHelper.setDateFieldAsError(
            etDayDateOfExpire,
            etMonthDateOfExpire,
            etYearDateOfExpire,
            tvErrorDateOfExpire,
            llErrorDateOfExpire,
            error
        )
    }

    override fun disableExpiration() {
        keyboardHandlingHelper.hideSoftKeyboard(this)
        etDayDateOfExpire.isClickable = false
        etMonthDateOfExpire.isClickable = false
        etYearDateOfExpire.isClickable = false
        disableFocusable(etDayDateOfExpire)
        disableFocusable(etMonthDateOfExpire)
        disableFocusable(etYearDateOfExpire)
        etDayDateOfExpire.setText(EMPTY)
        etMonthDateOfExpire.setText(EMPTY)
        etYearDateOfExpire.setText(EMPTY)
        errorSettingHelper.setDateFieldAsDisable(
            etDayDateOfExpire,
            etMonthDateOfExpire,
            etYearDateOfExpire,
            tvErrorDateOfExpire,
            llErrorDateOfExpire
        )
        checkEnabledButton()
    }

    private fun disableFocusable(editText: EditText) {
        editText.isFocusable = false
        editText.isCursorVisible = false
        editText.isFocusableInTouchMode = false
    }

    override fun enableExpiration() {
        etDayDateOfExpire.isClickable = true
        etMonthDateOfExpire.isClickable = true
        etYearDateOfExpire.isClickable = true
        enableFocusable(etDayDateOfExpire)
        enableFocusable(etMonthDateOfExpire)
        enableFocusable(etYearDateOfExpire)
        setErrorDateOfExpire(resources.getString(R.string.Ekyc_review_require))
        checkEnabledButton()
    }

    private fun enableFocusable(editText: EditText) {
        editText.isFocusable = true
        editText.isCursorVisible = true
        editText.isFocusableInTouchMode = true
    }

    private fun setText() {
        tvMainHeader.setText(R.string.Ekyc_review_main_title)
        tvSectionHeader.setText(R.string.Ekyc_review_header_title)
        tvTitleEn.setText(R.string.Ekyc_review_title_name_en)
        tvFirstNameEn.setText(R.string.Ekyc_review_first_name_en)
        tvMiddleNameEn.setText(R.string.Ekyc_review_middle_name_en)
        tvLastNameEn.setText(R.string.Ekyc_review_last_name_en)
        tvTitleTh.setText(R.string.Ekyc_review_title_name_th)
        tvFirstNameTh.setText(R.string.Ekyc_review_first_name_th)
        tvMiddleNameTh.setText(R.string.Ekyc_review_middle_name_th)
        tvLastNameTh.setText(R.string.Ekyc_review_last_name_th)
        tvNationalId.setText(R.string.Ekyc_review_card_id_number)
        tvLaserId.setText(R.string.Ekyc_review_laser_id_number)
        tvDateOfBirth.setText(R.string.Ekyc_review_date_of_birth)
        tvIssuedDate.setText(R.string.Ekyc_review_date_of_issue)
        tvExpirationDate.setText(R.string.Ekyc_review_date_of_expire)
        tvConfirm.text = resources.getString(R.string.Ekyc_review_dialog_description)
        btConfirm.setText(R.string.Ekyc_review_confirm)
    }

    override fun checkEnabledButton() {
        btConfirm.isEnabled = (tlTitleEn.helperText.isNullOrEmpty()
                && tlTitleTh.helperText.isNullOrEmpty()
                && tlFirstNameEn.helperText.isNullOrEmpty()
                && tlMiddleEn.helperText.isNullOrEmpty()
                && tlLastNameEn.helperText.isNullOrEmpty()
                && tlFirstNameTh.helperText.isNullOrEmpty()
                && tlMiddleTh.helperText.isNullOrEmpty()
                && tlLastNameTh.helperText.isNullOrEmpty()
                && tlNationalId.helperText.isNullOrEmpty()
                && !tvErrorDateOfBirth.isVisible
                && !tvErrorDateOfIssued.isVisible
                && !tvErrorDateOfExpire.isVisible
                && tlLaserId.helperText.isNullOrEmpty())

        if (btConfirm.isEnabled) {
            userConfirmedValueDisplay?.let { userConfirmedValue ->
                userConfirmedValue.titleEn = teTitleEn.text.toString().trim()
                userConfirmedValue.titleTh = teTitleTh.text.toString().trim()
                userConfirmedValue.firstNameEn = teFirstNameEn.text.toString().trim()
                userConfirmedValue.middleNameEn = teMiddleNameEn.text.toString().trim()
                userConfirmedValue.lastNameEn = teLastNameEn.text.toString().trim()
                userConfirmedValue.firstNameTh = teFirstNameTh.text.toString().trim()
                userConfirmedValue.middleNameTh = teMiddleNameTh.text.toString().trim()
                userConfirmedValue.lastNameTh = teLastNameTh.text.toString().trim()
                userConfirmedValue.nationalId = teNationalId.text.toString()
                userConfirmedValue.laserId = teLaserId.text.toString()
                userConfirmedValue.dateOfIssue =
                    "${etDayDateOfIssued.text}/${etMonthDateOfIssued.text}/${etYearDateOfIssued.text}"
                var dateOfBirth =
                    "${etDayDateOfBirth.text}/${etMonthDateOfBirth.text}/${etYearDateOfBirth.text}"
                dateOfBirth = dateOfBirth.replace(NO_VALUE_FIELD_CHAR, "00")
                userConfirmedValue.dateOfBirth = dateOfBirth
                if (cbExpirationDate.isChecked) {
                    userConfirmedValue.dateOfExpiry = EXPIRE_DATE
                } else {
                    userConfirmedValue.dateOfExpiry =
                        "${etDayDateOfExpire.text}/${etMonthDateOfExpire.text}/${etYearDateOfExpire.text}"
                }
            }
        }
    }

    private fun submitUserConfirmedValue() {
        val dateOfIssue =
            "${etDayDateOfIssued.text}/${etMonthDateOfIssued.text}/${etYearDateOfIssued.text}"
        val dateOfBirth =
            "${etDayDateOfBirth.text}/${etMonthDateOfBirth.text}/${etYearDateOfBirth.text}"
        val dateOfExpiry = if (cbExpirationDate.isChecked) EXPIRE_DATE else
            "${etDayDateOfExpire.text}/${etMonthDateOfExpire.text}/${etYearDateOfExpire.text}"
        val request = ConfirmationInfoRequest(
            checkExpiredIdCard = isCheckExpired,
            checkDopa = Config.checkDopa,
            data = ConfirmationInfoRequest.Data(
                nationalId = teNationalId.text.toString().replace(ID_SEPARATE_CHAR,  EMPTY),
                titleTh = teTitleTh.text.toString(),
                titleEn = teTitleEn.text.toString(),
                firstNameTh = teFirstNameTh.text.toString(),
                firstNameEn = teFirstNameEn.text.toString(),
                middleNameTh = teMiddleNameTh.text.toString(),
                middleNameEn = teMiddleNameEn.text.toString(),
                lastNameTh = teLastNameTh.text.toString(),
                lastNameEn = teLastNameEn.text.toString(),
                dateOfBirth = dateOfBirth.replace(NO_VALUE_FIELD_CHAR, "00"),
                dateOfIssue = dateOfIssue,
                dateOfExpiry = dateOfExpiry,
                laserId = teLaserId.text.toString().replace(ID_SEPARATE_CHAR, EMPTY)
            )
        )
        val authenticatedHeaders = headersProvider.getAuthenticatedHeaders(
            authorization = Config.token,
            deviceKey = pref.deviceKey,
            userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId),
            sessionId = Config.x_session_id,
            tid = GetDeviceSettingUseCase.getUUID(),
            ekycToken = pref.ekycToken,
            correlationId = GetDeviceSettingUseCase.getUUID(),
            sdkVersion = GetDeviceSettingUseCase.getVersionName()
        )
        presenter.getConfirmationInfo(request, authenticatedHeaders)
    }
}