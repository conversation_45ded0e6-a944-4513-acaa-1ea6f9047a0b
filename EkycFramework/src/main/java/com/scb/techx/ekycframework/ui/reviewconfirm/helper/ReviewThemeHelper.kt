package com.scb.techx.ekycframework.ui.reviewconfirm.helper

import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.view.View
import android.widget.RelativeLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.EditText
import android.widget.CheckBox
import android.widget.Button
import android.widget.ImageButton
import com.google.android.material.textfield.TextInputEditText
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.ui.themehelper.ThemeHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycPresenter
import com.scb.techx.ekycframework.ui.theme.ReviewTheme

class ReviewThemeHelper (
    val presenter: ReviewInformationEkycPresenter,
    val activity: ReviewInformationEkycActivity
    ) {
    private val themeHelper by lazy { ThemeHelper() }
    private val rlReview: RelativeLayout by lazy { activity.findViewById(R.id.rl_review) }
    private val ivCancel: ImageButton by lazy { activity.findViewById(R.id.ib_cancel) }
    private val tvMainHeader: TextView by lazy { activity.findViewById(R.id.tv_main_header) }
    private val vSectionHeader: View by lazy { activity.findViewById(R.id.v_section_header) }
    private val tvSectionHeader: TextView by lazy { activity.findViewById(R.id.tv_section_header) }
    private val tvTitleEn: TextView by lazy { activity.findViewById(R.id.tv_title_en) }
    private val tvFirstNameEn: TextView by lazy { activity.findViewById(R.id.tv_firstname_en) }
    private val tvMiddleNameEn: TextView by lazy { activity.findViewById(R.id.tv_middle_en) }
    private val tvLastNameEn: TextView by lazy { activity.findViewById(R.id.tv_lastname_en) }
    private val tvTitleTh: TextView by lazy { activity.findViewById(R.id.tv_title_th) }
    private val tvFirstNameTh: TextView by lazy { activity.findViewById(R.id.tv_firstname_th) }
    private val tvMiddleNameTh: TextView by lazy { activity.findViewById(R.id.tv_middle_th) }
    private val tvLastNameTh: TextView by lazy { activity.findViewById(R.id.tv_lastname_th) }
    private val tvDateOfBirth: TextView by lazy { activity.findViewById(R.id.tv_date_of_birth) }
    private val tvIssuedDate: TextView by lazy { activity.findViewById(R.id.tv_issued_date) }
    private val tvExpirationDate: TextView by lazy { activity.findViewById(R.id.tv_expiration_date) }
    private val tvNationalId: TextView by lazy { activity.findViewById(R.id.tv_id_number) }
    private val tvLaserId: TextView by lazy { activity.findViewById(R.id.tv_laser_id) }
    private val teTitleEn: TextInputEditText by lazy { activity.findViewById(R.id.te_title_en) }
    private val teTitleTh: TextInputEditText by lazy { activity.findViewById(R.id.te_title_th) }
    private val teFirstNameEn: TextInputEditText by lazy { activity.findViewById(R.id.te_firstname_en) }
    private val teLastNameEn: TextInputEditText by lazy { activity.findViewById(R.id.te_lastname_en) }
    private val teMiddleNameEn: TextInputEditText by lazy { activity.findViewById(R.id.te_middle_en) }
    private val teFirstNameTh: TextInputEditText by lazy { activity.findViewById(R.id.te_firstname_th) }
    private val teLastNameTh: TextInputEditText by lazy { activity.findViewById(R.id.te_lastname_th) }
    private val teMiddleNameTh: TextInputEditText by lazy { activity.findViewById(R.id.te_middle_th) }
    private val etDayDateOfBirth: EditText by lazy { activity.findViewById(R.id.et_day_date_of_birth) }
    private val etMonthDateOfBirth: EditText by lazy { activity.findViewById(R.id.et_month_date_of_birth) }
    private val etYearDateOfBirth: EditText by lazy { activity.findViewById(R.id.et_year_date_of_birth) }
    private val etDayDateOfIssued: EditText by lazy { activity.findViewById(R.id.et_day_date_of_issued) }
    private val etMonthDateOfIssued: EditText by lazy { activity.findViewById(R.id.et_month_date_of_issued) }
    private val etYearDateOfIssued: EditText by lazy { activity.findViewById(R.id.et_year_date_of_issued) }
    private val etDayDateOfExpire: EditText by lazy { activity.findViewById(R.id.et_day_date_of_expiration) }
    private val etMonthDateOfExpire: EditText by lazy { activity.findViewById(R.id.et_month_date_of_expiration) }
    private val etYearDateOfExpire: EditText by lazy { activity.findViewById(R.id.et_year_date_of_expiration) }
    private val teNationalId: TextInputEditText by lazy { activity.findViewById(R.id.te_id_number) }
    private val teLaserId: TextInputEditText by lazy { activity.findViewById(R.id.te_laser_id) }
    private val cbExpirationDate: CheckBox by lazy { activity.findViewById(R.id.cb_expiration) }
    private val tvConfirm: TextView by lazy { activity.findViewById(R.id.tv_confirm) }
    private val btConfirm: Button by lazy { activity.findViewById(R.id.bt_confirm) }
    private val ivLogo: ImageView by lazy { activity.findViewById(R.id.iv_ndid_bank_logo_tile) }

    fun setTheme() {
        try {
            setThemeLogicThatCanBeEmpty(
                ReviewTheme.mainHeaderTextColor,
                ReviewTheme.sectionHeaderTextColor,
                ReviewTheme.fieldLabelTextColor,
                ReviewTheme.borderColor
            )

            ReviewTheme.fontName?.let {
                setFontName(it)
            }

            ReviewTheme.cancelLogo?.let {
                ivCancel.setImageResource(it)
            }

            ReviewTheme.logo?.let {
                ivLogo.setImageResource(it)
            }

            setThemeForButton()
        } catch (e: IllegalArgumentException) {
            HandleCallback.ocrResultsCallback?.onSuccess(
                false,
                "Please put color as #______",
                null,
                null
            )
        }
    }

    fun setThemeForMainHeaderColor(color: String) {
        tvMainHeader.setTextColor(Color.parseColor(color))
    }

    fun setThemeForSectionHeaderColor(color: String) {
        tvSectionHeader.setTextColor(Color.parseColor(color))
        tvConfirm.setTextColor(Color.parseColor(color))
    }

    fun setThemeForFieldLabelHeaderColor(color: String) {
        val labelTextColor = Color.parseColor(color)
        tvFirstNameEn.setTextColor(labelTextColor)
        tvLastNameEn.setTextColor(labelTextColor)
        tvFirstNameTh.setTextColor(labelTextColor)
        tvLastNameTh.setTextColor(labelTextColor)
        tvMiddleNameEn.setTextColor(labelTextColor)
        tvMiddleNameTh.setTextColor(labelTextColor)
        tvTitleEn.setTextColor(labelTextColor)
        tvTitleTh.setTextColor(labelTextColor)
        tvNationalId.setTextColor(labelTextColor)
        tvDateOfBirth.setTextColor(labelTextColor)
        tvIssuedDate.setTextColor(labelTextColor)
        tvExpirationDate.setTextColor(labelTextColor)
        tvLaserId.setTextColor(labelTextColor)
        cbExpirationDate.setTextColor(labelTextColor)
    }

    fun setThemeForBorderColor(color: String) {
        val borderColor = Color.parseColor(ReviewTheme.borderColor)
        val backgroundStyle = GradientDrawable()
        backgroundStyle.cornerRadius = activity.resources.getDimension(R.dimen.radius16)
        backgroundStyle.setStroke(
            activity.resources.getDimension(R.dimen.width2).toInt(),
            borderColor
        )
        rlReview.background = backgroundStyle
        vSectionHeader.setBackgroundColor(borderColor)
    }

    fun setThemeForButton() {
        val textColors = themeHelper.makeColorArray(
            highlightColor = ReviewTheme.highlightButtonTextColor,
            normalColor = ReviewTheme.normalButtonTextColor,
            disableColor = ReviewTheme.disableButtonTextColor
        )

        val buttonColors = themeHelper.makeColorArray(
            highlightColor = ReviewTheme.highlightButtonBackgroundColor,
            normalColor = ReviewTheme.normalButtonBackgroundColor,
            disableColor = ReviewTheme.disableButtonBackgroundColor
        )

        val textColorState = themeHelper.makeColorStateArray(
            highlightColor = ReviewTheme.highlightButtonTextColor,
            normalColor = ReviewTheme.normalButtonTextColor,
            disableColor = ReviewTheme.disableButtonTextColor
        )

        val buttonColorState = themeHelper.makeColorStateArray(
            highlightColor = ReviewTheme.highlightButtonBackgroundColor,
            normalColor = ReviewTheme.normalButtonBackgroundColor,
            disableColor = ReviewTheme.disableButtonBackgroundColor
        )

        val textColorList = themeHelper.textColorStateListMaker(textColors, textColorState)
        val buttonColorList = themeHelper.buttonColorStateListMaker(buttonColors, buttonColorState)

        btConfirm.backgroundTintList = buttonColorList
        btConfirm.setTextColor(textColorList)
    }

    fun setFontName(font: Typeface) {
        tvMainHeader.typeface = font
        tvSectionHeader.typeface = font
        tvTitleTh.typeface = font
        tvFirstNameTh.typeface = font
        tvMiddleNameTh.typeface = font
        tvLastNameTh.typeface = font
        tvTitleEn.typeface = font
        tvFirstNameEn.typeface = font
        tvMiddleNameEn.typeface = font
        tvLastNameEn.typeface = font
        tvNationalId.typeface = font
        tvDateOfBirth.typeface = font
        tvIssuedDate.typeface = font
        tvExpirationDate.typeface = font
        tvLaserId.typeface = font
        tvConfirm.typeface = font
        teTitleTh.typeface = font
        teTitleEn.typeface = font
        teFirstNameEn.typeface = font
        teMiddleNameEn.typeface = font
        teFirstNameEn.typeface = font
        teMiddleNameEn.typeface = font
        teLastNameEn.typeface = font
        teFirstNameTh.typeface = font
        teMiddleNameTh.typeface = font
        teLastNameTh.typeface = font
        teNationalId.typeface = font
        etDayDateOfBirth.typeface = font
        etMonthDateOfBirth.typeface = font
        etYearDateOfBirth.typeface = font
        etDayDateOfIssued.typeface = font
        etMonthDateOfIssued.typeface = font
        etYearDateOfIssued.typeface = font
        etDayDateOfExpire.typeface = font
        etMonthDateOfExpire.typeface = font
        etYearDateOfExpire.typeface = font
        teLaserId.typeface = font
        btConfirm.typeface = font
        cbExpirationDate.typeface = font
    }

    fun setThemeLogicThatCanBeEmpty(
        mainHeaderColor: String?,
        sectionHeaderColor: String?,
        fieldLabelTextColor: String?,
        borderColor: String?
    ) {
        if (!mainHeaderColor.isNullOrEmpty()) {
            setThemeForMainHeaderColor(mainHeaderColor)
        }

        if (!sectionHeaderColor.isNullOrEmpty()) {
            setThemeForSectionHeaderColor(sectionHeaderColor)
        }

        if (!fieldLabelTextColor.isNullOrEmpty()) {
            setThemeForFieldLabelHeaderColor(fieldLabelTextColor)
        }

        if (!borderColor.isNullOrEmpty()) {
            setThemeForBorderColor(borderColor)
        }
    }
}