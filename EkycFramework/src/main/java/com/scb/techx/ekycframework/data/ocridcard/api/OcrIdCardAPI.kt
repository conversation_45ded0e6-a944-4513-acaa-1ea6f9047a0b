package com.scb.techx.ekycframework.data.ocridcard.api

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.data.ocridcard.model.InitFlowRequestEntity
import com.scb.techx.ekycframework.data.ocridcard.model.InitFlowResponseEntity
import com.scb.techx.ekycframework.Constants
import io.reactivex.rxjava3.core.Single
import retrofit2.http.Body
import retrofit2.http.HeaderMap
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Url

interface OcrIdCardAPI {
    @Headers(Constants.ContentType)
    @POST
    fun getInitFlow(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: InitFlowRequestEntity
    ): Single<InitFlowResponseEntity>
}