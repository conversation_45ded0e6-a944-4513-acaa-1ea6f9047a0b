package com.scb.techx.ekycframework.domain.ndid.model.response

import androidx.annotation.Keep

@Keep
data class NdidRequestResponse (
    val code: String,
    val description: String,
    val data: NdidRequestData?
    )

@Keep
data class NdidRequestData(
    val sessionId: String,
    val referenceId: String,
    val status: String,
    val expireTime: Int,
    val idp: Idp,
    val ndidData: NdidData
    )

@Keep
data class NdidData(
    val requestId: String
    )

@Keep
data class Idp(
    val nodeId: String,
    val industryCode: String,
    val companyCode: String,
    val shortName: String,
    val marketingNameTh: String,
    val marketingNameEn: String,
    val smallIconPath: String,
    val mediumIconPath: String,
    val largeIconPath: String,
    val deepLinkIos: String,
    val deepLinkAndroid: String,
    val deepLinkHuawei: String
    )