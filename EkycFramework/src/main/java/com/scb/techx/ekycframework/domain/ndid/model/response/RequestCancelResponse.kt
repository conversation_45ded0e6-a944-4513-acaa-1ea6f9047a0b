package com.scb.techx.ekycframework.domain.ndid.model.response

import androidx.annotation.Keep

@Keep
data class RequestCancel(
    val code: String,
    val description: String,
    val data: DataRequestCancel?,
    val ndidData: NdidDataRequestCancel?
)

@Keep
data class NdidDataRequestCancel(
    val requestId: String?
)

@Keep
data class DataRequestCancel(
    val sessionId: String?,
    val referenceId: String?,
    val status: String?
)
