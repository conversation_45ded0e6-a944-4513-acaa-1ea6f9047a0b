package com.scb.techx.ekycframework.data.getsession.mapper

import com.scb.techx.ekycframework.data.getsession.model.response.SessionTokenDataEntity
import com.scb.techx.ekycframework.data.getsession.model.response.SessionTokenResponseEntity
import com.scb.techx.ekycframework.domain.getsession.model.SessionTokenData
import com.scb.techx.ekycframework.domain.getsession.model.SessionTokenResponse

class GetSessionResponseMapperEntity {
    fun mapFromEntity(entity: SessionTokenResponseEntity): SessionTokenResponse {
        return SessionTokenResponse(
            code = entity.code,
            description = entity.description,
            data = mapSessionTokenData(entity.data)
        )
    }

    private fun mapSessionTokenData(
        entity: SessionTokenDataEntity?
    ): SessionTokenData? {
        return if (entity == null) {
            null
        }
        else {
            SessionTokenData(
                ekycToken = entity.ekycToken
            )
        }
    }
}