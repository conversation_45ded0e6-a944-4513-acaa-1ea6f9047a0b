package com.scb.techx.ekycframework.domain.ndid.model.response

import androidx.annotation.Keep

@Keep
data class NdidStatus(
    val code: String,
    val description: String,
    val data: NdidStatusData
)

@Keep
data class NdidStatusData(
    val sessionId: String,
    val referenceId: String,
    val status: String,
    val expireTime: Int,
    val idpStatus: IdpStatus,
    val ndidError: NdidErrorStatus,
    val ndidStatusNdidData: NdidStatusNdidData
)

@Keep
data class NdidStatusNdidData(
    val requestId: String
)

@Keep
data class IdpStatus(
    val nodeId: String,
    val industryCode: String,
    val companyCode: String,
    val shortName: String,
    val marketingNameTh: String,
    val marketingNameEn: String,
    val smallIconPath: String,
    val mediumIconPath: String,
    val largeIconPath: String,
    val deepLinkIos: String,
    val deepLinkAndroid: String,
    val deepLinkHuawei: String
)

@Keep
data class NdidErrorStatus(
    val code: String,
    val description: String,
    val messageTh: String,
    val messageEn: String
)