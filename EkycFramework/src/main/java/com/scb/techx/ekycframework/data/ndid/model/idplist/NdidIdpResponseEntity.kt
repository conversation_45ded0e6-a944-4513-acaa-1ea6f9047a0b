package com.scb.techx.ekycframework.data.ndid.model.idplist

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class NdidIdpResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: IdpDataEntity?
)

@Keep
data class IdpDataEntity(
    @SerializedName("registeredIdpList")
    val registeredIdpList: MutableList<IdpListEntity>?,
    @SerializedName("idpList")
    val idpList: MutableList<IdpListEntity>?
)

@Keep
data class IdpListEntity(
    @SerializedName("nodeId")
    val nodeId: String,
    @SerializedName("industryCode")
    val industryCode: String,
    @SerializedName("companyCode")
    val companyCode: String,
    @SerializedName("marketingNameTh")
    val marketingNameTh: String,
    @SerializedName("marketingNameEn")
    val marketingNameEn: String,
    @SerializedName("smallIconPath")
    val smallIconPath: String,
    @SerializedName("mediumIconPath")
    val mediumIconPath: String,
    @SerializedName("largeIconPath")
    val largeIconPath: String,
    @SerializedName("deepLinkIos")
    val deepLinkIos: String,
    @SerializedName("deepLinkAndroid")
    val deepLinkAndroid: String,
    @SerializedName("deepLinkHuawei")
    val deepLinkHuawei: String,
)