package com.scb.techx.ekycframework.ui.ndidverification.idplist.viewholder

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.jakewharton.rxbinding4.view.clicks
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.Constants.EN_LOCALE
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.THEME_SETTING_ERROR
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ndidverification.themehelper.NdidThemeHelper
import com.scb.techx.ekycframework.ui.ndidverification.idplist.model.IdpListModel
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase

class IdpListViewHolder(val view: View, val idpRegistered: Boolean) : RecyclerView.ViewHolder(view) {
    private val tvBankName: TextView by lazy { view.findViewById(R.id.tv_bank_name) }
    private val clBgSelection: ConstraintLayout by lazy { view.findViewById(R.id.cl_bg_selection) }
    private val imvBankLogo: ImageView by lazy { view.findViewById(R.id.imv_bank_logo) }
    private val ndidVerificationActivity by lazy { view.context as NdidVerificationActivity }
    fun bind(data: IdpListModel, selectionBankCallback: (String, String, String, Boolean) -> Unit) {
        with(itemView) {

            if (HandleCallback.language.isNullOrEmpty()) {
                if (GetDeviceSettingUseCase.getCurrentLanguage() == EN_LOCALE) {
                    tvBankName.text = data.marketingNameEn
                } else {
                    tvBankName.text = data.marketingNameTh
                }
            } else if (HandleCallback.language == EN_LOCALE) {
                tvBankName.text = data.marketingNameEn
            } else {
                tvBankName.text = data.marketingNameTh
            }

            clBgSelection.isSelected = data.isSelected
            Glide.with(view.context)
                .load(data.mediumIconPath)
                .placeholder(R.drawable.ic_default_image_md)
                .error(R.drawable.ic_default_image_md)
                .into(imvBankLogo)
            clBgSelection.clicks().subscribe {
                selectionBankCallback.invoke(data.nodeId, data.companyCode, data.industryCode, idpRegistered)
            }
            setTheme()
        }
    }

    private fun setTheme() {
        try {
            NdidThemeHelper.setSecondaryColorTextTheme(tvBankName, true)
            NdidThemeHelper.setSelectableBorderTileTheme(clBgSelection)
        } catch (e: IllegalArgumentException) {
            ndidVerificationActivity.handleCallback(
                false,
                THEME_SETTING_ERROR,
                null,
                null,
                null
            )
        }
    }
}