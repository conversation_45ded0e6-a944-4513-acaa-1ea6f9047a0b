package com.scb.techx.ekycframework.data.ndid.model.idplist

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class NdidRequestResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: NdidRequestDataEntity?
)

@Keep
data class NdidRequestDataEntity(
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("referenceId")
    val referenceId: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("expireTime")
    val expireTime: Int,
    @SerializedName("idp")
    val idp: IdpEntity,
    @SerializedName("ndidData")
    val ndidData: NdidDataEntity
)

@Keep
data class NdidDataEntity(
    @SerializedName("requestId")
    val requestId: String
)

@Keep
data class IdpEntity(
    @SerializedName("nodeId")
    val nodeId: String,
    @SerializedName("industryCode")
    val industryCode: String,
    @SerializedName("companyCode")
    val companyCode: String,
    @SerializedName("shortName")
    val shortName: String,
    @SerializedName("marketingNameTh")
    val marketingNameTh: String,
    @SerializedName("marketingNameEn")
    val marketingNameEn: String,
    @SerializedName("smallIconPath")
    val smallIconPath: String,
    @SerializedName("mediumIconPath")
    val mediumIconPath: String,
    @SerializedName("largeIconPath")
    val largeIconPath: String,
    @SerializedName("deepLinkIos")
    val deepLinkIos: String,
    @SerializedName("deepLinkAndroid")
    val deepLinkAndroid: String,
    @SerializedName("deepLinkHuawei")
    val deepLinkHuawei: String
)