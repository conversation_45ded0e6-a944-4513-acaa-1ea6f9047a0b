package com.scb.techx.ekycframework.domain.ocridcard.repository

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowRequest
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowResponse
import io.reactivex.rxjava3.core.Single

interface OcrIdCardRepository {
    fun getInitFlow(
        authenticatedHeaders: AuthenticatedHeaders,
        request: InitFlowRequest
    ): Single<InitFlowResponse>
}