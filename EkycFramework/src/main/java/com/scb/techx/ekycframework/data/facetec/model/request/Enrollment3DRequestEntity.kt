package com.scb.techx.ekycframework.data.facetec.model.request

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class Enrollment3DRequestEntity(
    @SerializedName("faceScan")
    val faceScan: String,
    @SerializedName("auditTrailImage")
    val auditTrailImage: String,
    @SerializedName("lowQualityAuditTrailImage")
    val lowQualityAuditTrailImage: String,
    @SerializedName("function")
    val function: String
)