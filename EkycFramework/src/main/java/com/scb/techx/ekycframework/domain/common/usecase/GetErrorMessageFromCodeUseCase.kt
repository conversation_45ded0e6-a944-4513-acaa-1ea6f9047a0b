package com.scb.techx.ekycframework.domain.common.usecase

import com.scb.techx.ekycframework.Constants

object GetErrorMessageFromCodeUseCase {
    fun execute(code: String): String {
        return when (code) {
            Constants.EkycStatusCode.CUS_EKYC_1001 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_1001
            }
            Constants.EkycStatusCode.CUS_EKYC_1002 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_1002
            }
            Constants.EkycStatusCode.CUS_EKYC_1003 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_1003
            }
            Constants.EkycStatusCode.CUS_EKYC_1004 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_1004
            }
            Constants.EkycStatusCode.CUS_EKYC_1899 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_1899
            }
            Constants.EkycStatusCode.CUS_EKYC_1999 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_1999
            }
            Constants.EkycStatusCode.CUS_EKYC_2001 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_2001
            }
            Constants.EkycStatusCode.CUS_EKYC_2002 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_2002
            }
            Constants.EkycStatusCode.CUS_EKYC_2003 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_2003
            }
            Constants.EkycStatusCode.CUS_EKYC_2004 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_2004
            }
            Constants.EkycStatusCode.CUS_EKYC_3001 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_3001
            }
            Constants.EkycStatusCode.CUS_EKYC_3003 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_3003
            }
            Constants.EkycStatusCode.CUS_EKYC_4001 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_4001
            }
            Constants.EkycStatusCode.CUS_EKYC_7101 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_7101
            }
            Constants.EkycStatusCode.CUS_EKYC_7102 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_7102
            }
            Constants.EkycStatusCode.CUS_EKYC_7103 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_7103
            }
            Constants.EkycStatusCode.CUS_EKYC_7201 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_7201
            }
            Constants.EkycStatusCode.CUS_EKYC_7301 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_7301
            }
            Constants.EkycStatusCode.CUS_EKYC_9001 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_9001
            }
            Constants.EkycStatusCode.CUS_EKYC_9002 -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_9002
            }
            else -> Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE
        }
    }
}