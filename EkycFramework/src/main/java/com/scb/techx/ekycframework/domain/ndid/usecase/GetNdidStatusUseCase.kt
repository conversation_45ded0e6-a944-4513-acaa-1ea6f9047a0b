package com.scb.techx.ekycframework.domain.ndid.usecase

import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.ui.processor.Config
import io.reactivex.rxjava3.core.Single

class GetNdidStatusUseCase {
    companion object {
        fun execute(
            pref: EkycPreferenceUtil,
            repository: NdidRepository
        ): Single<NdidStatus> {
            return repository.getNdidStatus(
                ApiMainHeadersProvider().getAuthenticatedHeaders(
                    authorization = Config.token,
                    sessionId = Config.x_session_id,
                    tid = GetDeviceSettingUseCase.getUUID(),
                    ekycToken = pref.ekycToken,
                    correlationId = GetDeviceSettingUseCase.getUUID()
                )
            )
        }
    }
}