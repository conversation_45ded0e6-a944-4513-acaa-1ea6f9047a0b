package com.scb.techx.ekycframework.domain.ocrliveness.usecase

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanResponse
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import io.reactivex.rxjava3.core.Single

class GetMatch3D2DUseCase {
    companion object {
        fun executeFront(
            repository: FaceTecRepository,
            authedHeaders: AuthenticatedHeaders,
            request: Match3D2DIdScanFrontRequest
        ): Single<Match3D2DIdScanResponse> {
            return repository.getMatch3D2DIdScanFront(
                authedHeaders = authedHeaders,
                request = request,
            )
        }

        fun executeBack(
            repository: FaceTecRepository,
            authedHeaders: AuthenticatedHeaders,
            request: Match3D2DIdScanBackRequest
        ): Single<Match3D2DIdScanResponse> {
            return repository.getMatch3D2DIdScanBack(
                authedHeaders = authedHeaders,
                request = request
            )
        }
    }
}