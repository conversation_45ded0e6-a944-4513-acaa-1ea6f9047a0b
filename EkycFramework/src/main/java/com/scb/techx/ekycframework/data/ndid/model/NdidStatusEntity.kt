package com.scb.techx.ekycframework.data.ndid.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class NdidStatusEntity(
    @SerializedName("code")
    val code: String,

    @SerializedName("description")
    val description: String,

    @SerializedName("data")
    val dataEntity: DataStatusEntity?,
)

@Keep
data class DataStatusEntity(
    @SerializedName("sessionId")
    val sessionId: String?,

    @SerializedName("referenceId")
    val referenceId: String?,

    @SerializedName("status")
    val status: String?,

    @SerializedName("expireTime")
    val expireTime: Int?,

    @SerializedName("idp")
    val idpEntity: IdpStatusEntity?,

    @SerializedName("ndidError")
    val ndidErrorEntity: NdidErrorStatusEntity?,

    @SerializedName("ndidData")
    val ndidData: NdidDataStatusEntity?
)

@Keep
data class NdidDataStatusEntity(
    @SerializedName("requestId")
    val requestId: String?
)

@Keep
data class IdpStatusEntity(
    @SerializedName("nodeId")
    val nodeId: String?,

    @SerializedName("industryCode")
    val industryCode: String?,

    @SerializedName("companyCode")
    val companyCode: String?,

    @SerializedName("shortName")
    val shortName: String?,

    @SerializedName("marketingNameTh")
    val marketingNameTh: String?,

    @SerializedName("marketingNameEn")
    val marketingNameEn: String?,

    @SerializedName("smallIconPath")
    val smallIconPath: String?,

    @SerializedName("mediumIconPath")
    val mediumIconPath: String?,

    @SerializedName("largeIconPath")
    val largeIconPath: String?,

    @SerializedName("deepLinkIos")
    val deepLinkIos: String?,

    @SerializedName("deepLinkAndroid")
    val deepLinkAndroid: String?,

    @SerializedName("deepLinkHuawei")
    val deepLinkHuawei: String?
)

@Keep
data class NdidErrorStatusEntity(
    @SerializedName("code")
    val code: String?,

    @SerializedName("description")
    val description: String?,

    @SerializedName("messageTh")
    val messageTh: String?,

    @SerializedName("messageEn")
    val messageEn: String?
)
