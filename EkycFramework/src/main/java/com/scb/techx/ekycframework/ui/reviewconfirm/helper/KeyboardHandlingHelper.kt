package com.scb.techx.ekycframework.ui.reviewconfirm.helper

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.CheckBox
import android.widget.EditText
import android.widget.ImageButton
import androidx.appcompat.app.AppCompatActivity
import com.scb.techx.ekycframework.R


class KeyboardHandlingHelper (
    val activity: Activity
    ) {
    var keyboardIsShown = false

    fun setUpCloseButton(
        doOnKeyboardOn: () -> Unit,
        doOnKeyboardOff: () -> Unit,
    ) {
        val activityRootView: View = activity.findViewById(R.id.rl_root)
        activityRootView.viewTreeObserver
            .addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    val heightDiff: Int =
                        activityRootView.getRootView().getHeight() - activityRootView.getHeight()
                    if (heightDiff > dpToPx(activity, 180f)
                    ) {
                        if (!keyboardIsShown) {
                            doOnKeyboardOn()
                            keyboardIsShown = true
                        }
                    }
                    else {
                        if (keyboardIsShown) {
                            doOnKeyboardOff()
                            keyboardIsShown = false
                        }
                    }
                }
            })
    }

    fun hideSoftKeyboard(activity: Activity) {
        val inputMethodManager = activity.getSystemService(
            AppCompatActivity.INPUT_METHOD_SERVICE
        ) as InputMethodManager
        if (inputMethodManager.isAcceptingText) {
            inputMethodManager.hideSoftInputFromWindow(
                activity.currentFocus?.windowToken,
                0
            )
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    fun setUpCloseKeyboardWhenPressOutsideEditText(
        activity: Activity,
        view: View
    ) {
        var isAllowToCloseKeyboard = true
        view.setOnTouchListener { _, motionEvent ->
            if (!view.isFocusable ||
                (view !is EditText && view !is CheckBox && view !is Button && view !is ImageButton)
            ) {
                when (motionEvent.action) {
                    MotionEvent.ACTION_UP -> {
                        if (isAllowToCloseKeyboard) {
                            hideSoftKeyboard(activity)
                        }
                        false
                    }
                    MotionEvent.ACTION_DOWN -> {
                        isAllowToCloseKeyboard = true
                        true
                    }
                    else -> {
                        false
                    }
                }
            }
            else {
                false
            }
        }

        view.setOnScrollChangeListener {
                _,_,_,_,_ ->
            isAllowToCloseKeyboard = false
        }

        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val innerView = view.getChildAt(i)
                setUpCloseKeyboardWhenPressOutsideEditText(activity, innerView)
            }
        }
    }

    private fun dpToPx(context: Context, valueInDp: Float): Float {
        val metrics: DisplayMetrics = context.resources.displayMetrics
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, valueInDp, metrics)
    }
}