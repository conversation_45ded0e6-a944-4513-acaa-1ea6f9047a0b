package com.scb.techx.ekycframework.ui.reviewconfirm.presenter

import android.content.DialogInterface
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay

interface ReviewInformationEkycContract {

    interface View {
        fun showEkycErrorDialog(description: String, dopaData: ConfirmationInfoResponse)
        fun confirmationInfoCallback(data: ConfirmationInfoResponse)
        fun setErrorDateOfExpire(error: String)
        fun callBackToClient(description: String)
        fun showLoadingDialog()
        fun hideLoadingDialog()
        fun disableExpiration()
        fun enableExpiration()
        fun callbackWithDopa(data: ConfirmationInfoResponse)
        fun callbackWithoutDopa(data: ConfirmationInfoResponse)
        fun dismissDialog(dialogInterface: DialogInterface)
        fun callBackAfterFailDopa(
            dopaData: ConfirmationInfoResponse,
            dialogInterface: DialogInterface
        )
        fun checkEnabledButton()
    }

    interface Presenter {
        fun determineValidateTypeTextEnglish(text: String?): ValidateType
        fun determineValidateTypeTitleEnglish(text: String?): ValidateType
        fun determineValidateTypeTitleThai(text: String?): ValidateType
        fun determineValidateTypeTextThai(text: String?): ValidateType
        fun determineValidateTypeNationalId(number: String?): ValidateType
        fun determineValidateTypeLaserId(laserId: String?): ValidateType
        fun getConfirmationInfo(request: ConfirmationInfoRequest, authenticatedHeaders: AuthenticatedHeaders)
        fun setUpNoExpirationDateCheckBoxOnCheck(isChecked: Boolean)
        fun decideTypeOfSuccessCallBack(
            data: ConfirmationInfoResponse,
            isDopaEnable: Boolean
        )
        fun decideLogicForPopupPositiveButton(
            dopaData: ConfirmationInfoResponse,
            dialogInterface: DialogInterface,
        )
        fun handleOcrPrefill(userConfirmedValueDisplay: UserConfirmedValueDisplay): UserConfirmedValueDisplay
    }
}