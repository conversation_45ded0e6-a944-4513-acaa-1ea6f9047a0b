package com.scb.techx.ekycframework.data.ndid.datarepository

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.data.ndid.api.NdidApi
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.data.ndid.mapper.NdidStatusMapperEntity
import com.scb.techx.ekycframework.data.ndid.mapper.RequestCancelMapperEntity
import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidIdpRequestMapperToEntity
import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidIdpResponseMapperEntity
import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidRequestRequestMapperToEntity
import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidRequestResponseMapperEntity
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.RequestCancel
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequest
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidIdpResponse
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequestRequest
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestResponse
import com.scb.techx.ekycframework.ui.processor.Config.Companion.NdidEndpoint.NDID_IDP_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.NdidEndpoint.NDID_REQUEST_CANCEL_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.NdidEndpoint.NDID_REQUEST_ENDPOINT
import com.scb.techx.ekycframework.ui.processor.Config.Companion.NdidEndpoint.NDID_STATUS_ENDPOINT
import io.reactivex.rxjava3.core.Single

class NdidDataRepository(private val service: NdidApi) : NdidRepository {

    private val ndidStatusMapper = NdidStatusMapperEntity()
    private val requestCancelMapper = RequestCancelMapperEntity()
    private val ndidIdpRequestMapper = NdidIdpRequestMapperToEntity()
    private val ndidIdpResponseMapper = NdidIdpResponseMapperEntity()
    private val ndidRequestRequestMapper = NdidRequestRequestMapperToEntity()
    private val ndidRequestResponseMapper = NdidRequestResponseMapperEntity()

    override fun getNdidStatus(authenticatedHeaders: AuthenticatedHeaders): Single<NdidStatus> {
        return service.getNdidStatus(
            NDID_STATUS_ENDPOINT,
            authenticatedHeaders
        ).map {
            ndidStatusMapper.mapFromEntity(it)
        }
    }

    override fun postRequestCancel(authenticatedHeaders: AuthenticatedHeaders): Single<RequestCancel> {
        return service.postRequestCancel(
            NDID_REQUEST_CANCEL_ENDPOINT,
            authenticatedHeaders
        ).map {
            requestCancelMapper.mapFromEntity(it)
        }
    }

    override fun getIdpList(
        authenticatedHeaders: AuthenticatedHeaders,
        request: NdidIdpRequest
    ): Single<NdidIdpResponse> {
        return service.getIdpList(
            NDID_IDP_ENDPOINT,
            authenticatedHeaders,
            ndidIdpRequestMapper.mapToEntity(request)
        ).map {
            ndidIdpResponseMapper.mapFromEntity(it)
        }
    }

    override fun getNdidRequest(
        authenticatedHeaders: AuthenticatedHeaders,
        request: NdidIdpRequestRequest
    ): Single<NdidRequestResponse> {
        return service.getNdidRequest(
            NDID_REQUEST_ENDPOINT,
            authenticatedHeaders,
            ndidRequestRequestMapper.mapToEntity(request)
        ).map {
            ndidRequestResponseMapper.mapFromEntity(it)
        }
    }

}