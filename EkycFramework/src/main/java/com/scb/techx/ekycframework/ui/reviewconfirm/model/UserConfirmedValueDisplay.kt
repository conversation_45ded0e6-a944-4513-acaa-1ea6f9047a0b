package com.scb.techx.ekycframework.ui.reviewconfirm.model

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class UserConfirmedValueDisplay(
    var nationalId: String? = null,
    var titleTh: String? = null,
    var firstNameTh: String? = null,
    var middleNameTh: String? = null,
    var lastNameTh: String? = null,
    var titleEn: String? = null,
    var firstNameEn: String? = null,
    var middleNameEn: String? = null,
    var lastNameEn: String? = null,
    var dateOfBirth: String? = null,
    var dateOfIssue: String? = null,
    var dateOfExpiry: String? = null,
    var laserId: String? = null
) : Parcelable
