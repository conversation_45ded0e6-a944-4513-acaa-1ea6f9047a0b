package com.scb.techx.ekycframework.data.facetec.mapper.response

import com.scb.techx.ekycframework.domain.ocrliveness.model.response.SessionFaceTecData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.SessionFaceTecResponse
import com.scb.techx.ekycframework.data.facetec.model.response.SessionFaceTecDataEntity
import com.scb.techx.ekycframework.data.facetec.model.response.SessionFaceTecResponseEntity

class GetSessionFaceTecResponseMapperEntity {
    fun mapFromEntity(entity: SessionFaceTecResponseEntity): SessionFaceTecResponse {
        return SessionFaceTecResponse(
            code = entity.code,
            description = entity.description,
            data = entity.data?.let {
                mapMatch3D2DIdScanData(it)
            }
        )
    }

    private fun mapMatch3D2DIdScanData(
        entity: SessionFaceTecDataEntity
    ): SessionFaceTecData {
        return SessionFaceTecData(
            sessionFaceTec = entity.sessionFaceTec,
            productionKey = entity.productionKey,
            deviceKey = entity.deviceKey,
            encryptionKey = entity.encryptionKey
        )
    }
}