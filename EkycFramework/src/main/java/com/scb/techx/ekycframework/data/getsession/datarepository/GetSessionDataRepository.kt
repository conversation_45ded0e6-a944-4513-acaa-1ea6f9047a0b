package com.scb.techx.ekycframework.data.getsession.datarepository

import com.scb.techx.ekycframework.data.getsession.api.GetSessionAPI
import com.scb.techx.ekycframework.data.getsession.mapper.GetSessionResponseMapperEntity
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.getsession.model.SessionTokenResponse
import com.scb.techx.ekycframework.domain.getsession.repository.GetSessionRepository
import com.scb.techx.ekycframework.ui.processor.Config.Companion.GetSessionEndpoint.GET_SESSION_ENDPOINT
import io.reactivex.rxjava3.core.Single

class GetSessionDataRepository(private val service: GetSessionAPI): GetSessionRepository {
    private val getSessionResponseMapperEntity = GetSessionResponseMapperEntity()

    override fun getSessionToken(authedHeaders: AuthenticatedHeaders): Single<SessionTokenResponse> {
        return service.getSessionToken(
            GET_SESSION_ENDPOINT,
            authedHeaders
        ).map {
            getSessionResponseMapperEntity.mapFromEntity(it)
        }
    }
}