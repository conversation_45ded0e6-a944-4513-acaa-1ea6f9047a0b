package com.scb.techx.ekycframework.data.facetec.model.response

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class ConfirmationInfoResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: DopaDataEntity?
) : Parcelable

@Keep
@Parcelize
data class DopaDataEntity(
    @SerializedName("dopaCode")
    val dopaCode: String?,
    @SerializedName("dopaDesc")
    val dopaDesc: String?,
    @SerializedName("userConfirmedValue")
    val userConfirmedValue: UserConfirmedValueEntity?,
) : Parcelable

@Keep
@Parcelize
data class UserConfirmedValueEntity(
    @SerializedName("nationalId")
    val nationalId: String?,
    @SerializedName("titleTh")
    val titleTh: String?,
    @SerializedName("firstNameTh")
    val firstNameTh: String?,
    @SerializedName("middleNameTh")
    val middleNameTh: String?,
    @SerializedName("lastNameTh")
    val lastNameTh: String?,
    @SerializedName("titleEn")
    val titleEn: String?,
    @SerializedName("firstNameEn")
    val firstNameEn: String?,
    @SerializedName("middleNameEn")
    val middleNameEn: String?,
    @SerializedName("lastNameEn")
    val lastNameEn: String?,
    @SerializedName("dateOfBirth")
    val dateOfBirth: String?,
    @SerializedName("dateOfIssue")
    val dateOfIssue: String?,
    @SerializedName("dateOfExpiry")
    val dateOfExpiry: String?,
    @SerializedName("laserId")
    val laserId: String?,
) : Parcelable