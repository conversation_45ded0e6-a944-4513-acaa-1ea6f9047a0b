package com.scb.techx.ekycframework.ui.theme

import android.graphics.Typeface

object NdidTheme {
    var headerTextColor: String? = null
    var subHeaderTextColor: String? = null
    var linkTextColor: String? = null
    var buttonBackgroundColor: String? = null
    var buttonTextColor: String? = null
    var timerBackgroundColor: String? = null
    var timerTextColor: String? = null
    var fontName: Typeface? = null
    var successIcon: Int? = null
    var errorIcon: Int? = null
    var unselectedBorderColor: String? = null
    var selectedBorderColor: String? = null
    var borderButtonColor: String? = null
    var borderButtonBackgroundColor: String? = null
    var borderButtonTextColor: String? = null
}