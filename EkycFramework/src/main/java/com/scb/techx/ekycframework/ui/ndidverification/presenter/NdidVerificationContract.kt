package com.scb.techx.ekycframework.ui.ndidverification.presenter

import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.ui.base.BasePresenter
import com.scb.techx.ekycframework.ui.base.BaseView
import com.scb.techx.ekycframework.ui.ndidverification.model.NdidVerificationEnrollmentDisplay
import com.scb.techx.ekycframework.util.EkycUtilities

interface NdidVerificationContract {
    interface View : BaseView {
        fun navigateToEnrollment(ndidVerificationEnrollmentDisplay: NdidVerificationEnrollmentDisplay?)
        fun navigateToIdpList()
        fun hideLoadingDialog()
        fun showLoadingDialog()
        fun onFinish()
        fun navigateToSuccess(description: String, status: String, referenceId: String, requestId: String)
        fun isPackageInstalled(packageName: String): Boolean
        fun handleCallback(
            success: <PERSON><PERSON>an,
            description: String,
            ndidStatus: String?,
            ndidError: EkycUtilities.NdidError?,
            ndidData: EkycUtilities.NdidData?
        )
        fun handleNotPendingNdidStatus(response: NdidStatus)
        fun handleErrorEkyc(code: String)
    }

    interface Presenter : BasePresenter {
        fun handleNdidNotPendingStatus(response: NdidStatus)
        fun getNdidStatus()
    }
}