package com.scb.techx.ekycframework.data.ocridcard.mapper

import com.scb.techx.ekycframework.data.ocridcard.model.InitFlowDataEntity
import com.scb.techx.ekycframework.data.ocridcard.model.InitFlowResponseEntity
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowData
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowResponse

class InitFlowResponseMapperEntity {
    fun mapFromEntity(entity: InitFlowResponseEntity): InitFlowResponse {
        return InitFlowResponse(
            entity.code,
            entity.description,
            transformData(entity.data)
        )
    }

    private fun transformData(entity: InitFlowDataEntity?): InitFlowData {
        return InitFlowData(
            sdkEncryptionIv = entity?.sdkEncryptionIv?: "",
            sdkEncryptionKeyOcr = entity?.sdkEncryptionKeyOcr?: ""
        )
    }
}