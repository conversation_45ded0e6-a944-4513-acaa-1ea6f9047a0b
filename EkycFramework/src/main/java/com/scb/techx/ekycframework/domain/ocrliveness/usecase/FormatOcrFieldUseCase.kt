package com.scb.techx.ekycframework.domain.ocrliveness.usecase

import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DOcrData
import com.scb.techx.ekycframework.Constants.DateOfBirthFlag
import com.scb.techx.ekycframework.Constants.DATE_OCR_FORMAT
import com.scb.techx.ekycframework.Constants.DATE_FORMAT
import com.scb.techx.ekycframework.Constants.NATIONAL_ID_FORMAT
import com.scb.techx.ekycframework.Constants.LASER_ID_FORMAT
import com.scb.techx.ekycframework.Constants.EMPTY
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Locale

class FormatOcrFieldUseCase {
    companion object {
        const val LIFELONG = "LIFELONG"
    }

    private fun convertDateOfBirthFromTag(dateOfBirth: String?, flag: String?): String {
        try {
            if (dateOfBirth != null) {
                return when (flag) {
                    DateOfBirthFlag.DAY_MONTH_YEAR -> {
                        dateOfBirth
                    }
                    DateOfBirthFlag.MONTH_YEAR -> {
                        dateOfBirth.substring(startIndex = 2)
                    }
                    DateOfBirthFlag.YEAR_ONLY -> {
                        dateOfBirth.substring(startIndex = 5)
                    }
                    else -> {
                        EMPTY
                    }
                }
            }
        }
        catch (e: StringIndexOutOfBoundsException) {
            return EMPTY
        }

        return EMPTY
    }

    fun userConfirmValueMapFromOcrData(
        data: Match3D2DOcrData?
    ): UserConfirmedValue {
        var dateOfBirthFormattedString = convertDateOfBirthFromTag(
            data?.dateOfBirth,
            data?.dateOfBirthFlag
        )
        var issuedDateFormattedString = EMPTY

        if (!data?.dateOfBirth.isNullOrEmpty()) {
            dateOfBirthFormattedString = formatDate(dateOfBirthFormattedString, false)
        }

        if (!data?.dateOfIssue.isNullOrEmpty()) {
            issuedDateFormattedString = formatDate(data?.dateOfIssue, false)
        }

        return UserConfirmedValue(
            nationalId = data?.nationalId.orEmpty(),
            titleTh = data?.titleTh.orEmpty(),
            firstNameTh = data?.firstNameTh.orEmpty(),
            middleNameTh = data?.middleNameTh.orEmpty(),
            lastNameTh = data?.lastNameTh.orEmpty(),
            titleEn = data?.titleEn.orEmpty(),
            firstNameEn = data?.firstNameEn.orEmpty(),
            middleNameEn = data?.middleNameEn.orEmpty(),
            lastNameEn = data?.lastNameEn.orEmpty(),
            dateOfBirth = dateOfBirthFormattedString,
            dateOfIssue = issuedDateFormattedString,
            dateOfExpiry = validateDateField(
                date = data?.dateOfExpiry.orEmpty(),
                isForSettingEditText = false
            ),
            laserId = data?.laserId.orEmpty()
        )
    }

    fun removeSpaceAndSpecialCharacter(string: String?): String {
        val re = Regex("[^A-Za-z0-9]")
        return if (string == null) {
            EMPTY
        } else {
            re.replace(string, EMPTY)
        }
    }

    private fun formatWithDateLength4(date: String, isForSettingEditText: Boolean): String {
        return try {
            val dateType = SimpleDateFormat("yyyy", Locale.US).parse(date)
            val year = SimpleDateFormat("yyyy", Locale.US).format(dateType)
            "-/-/$year"
        } catch (e: ParseException) {
            if (isForSettingEditText) {
                "-/-/"
            }
            else {
                EMPTY
            }
        }
    }

    private fun formatWithDateLength7(date: String, isForSettingEditText: Boolean): String {
        return try {
            val dateType = SimpleDateFormat("MMMy", Locale.US).parse(date)
            val monthYear = SimpleDateFormat("MM/y", Locale.US).format(dateType)
            "-/$monthYear"
        } catch (e: ParseException) {
            if (isForSettingEditText) {
                "-/"
            }
            else {
                EMPTY
            }
        }
    }

    private fun validateDateField(date: String, isForSettingEditText: Boolean): String {
        try {
            var day = date.substring(0, date.length - 7)
            var month = date.substring(date.length - 7, date.length - 4)
            var year = date.substring(date.length - 4, date.length)
            day = try {
                val format = SimpleDateFormat("dd/MMM", Locale.US)
                format.isLenient = false
                val dateType = format.parse("$day/$month")
                SimpleDateFormat("dd", Locale.US).format(dateType)
            } catch (e: ParseException) {
                if (isForSettingEditText) {
                    EMPTY
                }
                else {
                    return EMPTY
                }
            }
            month = try {
                val dateType = SimpleDateFormat("MMM", Locale.US).parse(month)
                SimpleDateFormat("MM", Locale.US).format(dateType)
            } catch (e: ParseException) {
                if (isForSettingEditText) {
                    EMPTY
                }
                else {
                    return EMPTY
                }
            }
            year = try {
                val dateType = SimpleDateFormat("yyyy", Locale.US).parse(year)
                SimpleDateFormat("yyyy", Locale.US).format(dateType)
            } catch (e: ParseException) {
                if (isForSettingEditText) {
                    EMPTY
                }
                else {
                    return EMPTY
                }
            }
            return "$day/$month/$year"
        } catch (e: IndexOutOfBoundsException) {
            return EMPTY
        }
    }

    fun formatDate(date: String?, isForSettingEditText: Boolean): String {
        date?.let {
            var newDate = it
            newDate = newDate.replace(".", EMPTY)
            newDate = newDate.replace(" ", EMPTY)
            newDate = newDate.replace("-", EMPTY)
            try {
                val format = SimpleDateFormat(DATE_OCR_FORMAT, Locale.US)
                format.isLenient = false
                val dateType = format.parse(newDate)
                return SimpleDateFormat(DATE_FORMAT, Locale.US).format(dateType)
            } catch (e: ParseException) {
                return when (newDate.length) {
                    4 -> {
                        formatWithDateLength4(newDate, isForSettingEditText)
                    }
                    7 -> {
                        formatWithDateLength7(newDate, isForSettingEditText)
                    }
                    else -> {
                        validateDateField(newDate, isForSettingEditText)
                    }
                }
            }
        }
        return EMPTY
    }

    fun formatExpirationDate(date: String?): String {
        date?.let {
            return try {
                var expiryDate = it
                expiryDate = expiryDate.replace(".", EMPTY)
                expiryDate = expiryDate.replace(" ", EMPTY)
                expiryDate = expiryDate.replace("-", EMPTY)
                val dateType = SimpleDateFormat(DATE_OCR_FORMAT, Locale.US).parse(expiryDate)
                SimpleDateFormat(DATE_FORMAT, Locale.US).format(dateType)
            } catch (e: ParseException) {
                if (LIFELONG == date || date.matches("^[0-9]+$".toRegex())) {
                    LIFELONG
                } else {
                    EMPTY
                }
            }
        }
        return EMPTY
    }

    fun formatNationalId(number: String?): String {
        val format = NATIONAL_ID_FORMAT
        val replaceChar = 'X'
        val formatChars = format.toCharArray()
        val inputChars = number?.toCharArray()
        val result = StringBuilder()
        var replaceIndex = 0
        for (i in formatChars.indices) {
            if (inputChars != null && replaceIndex >= inputChars.size) {
                break
            }
            if (formatChars[i] != replaceChar) {
                result.append(formatChars[i])
                continue
            }
            result.append(inputChars?.get(replaceIndex))
            replaceIndex++
        }
        return result.toString().replace(replaceChar.toString().toRegex(), EMPTY)
    }

    fun formatLaserId(laserId: String?): String {
        val format = LASER_ID_FORMAT
        val replaceChar = '#'
        val formatChars = format.toCharArray()
        val inputChars = laserId?.toCharArray()
        val result = StringBuilder()
        var replaceIndex = 0
        for (i in formatChars.indices) {
            if (inputChars != null && replaceIndex >= inputChars.size) {
                break
            }
            if (formatChars[i] != replaceChar) {
                result.append(formatChars[i])
                continue
            }
            result.append(inputChars?.get(replaceIndex))
            replaceIndex++
        }
        return result.toString().replace(replaceChar.toString().toRegex(), EMPTY)
    }
}