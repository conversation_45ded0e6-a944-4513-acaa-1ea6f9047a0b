package com.scb.techx.ekycframework.domain.ocrliveness.usecase

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import io.reactivex.rxjava3.core.Single

class GetConfirmInfoUseCase {
    companion object {
        fun execute(
            authenticatedHeaders: AuthenticatedHeaders,
            request: ConfirmationInfoRequest,
            repository: FaceTecRepository
        ): Single<ConfirmationInfoResponse> {
            return repository.getConfirmationInfo(
                authenticatedHeaders,
                request
            )
        }
    }
}