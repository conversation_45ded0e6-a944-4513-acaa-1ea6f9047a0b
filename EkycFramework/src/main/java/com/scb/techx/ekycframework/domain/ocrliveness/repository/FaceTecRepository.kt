package com.scb.techx.ekycframework.domain.ocrliveness.repository

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.SessionFaceTecResponse
import io.reactivex.rxjava3.core.Single

interface FaceTecRepository {
    fun getConfirmationInfo(
        authedHeaders: AuthenticatedHeaders,
        request: ConfirmationInfoRequest
    ): Single<ConfirmationInfoResponse>

    fun getEnrollment3D(
        authedHeaders: AuthenticatedHeaders,
        request: Enrollment3DRequest
    ): Single<Enrollment3DResponse>

    fun getMatch3D2DIdScanFront(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanFrontRequest
    ): Single<Match3D2DIdScanResponse>

    fun getMatch3D2DIdScanBack(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanBackRequest
    ): Single<Match3D2DIdScanResponse>

    fun getIdScanOnly(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanFrontRequest
    ): Single<Match3D2DIdScanResponse>

    fun getIdScanOnly(
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanBackRequest
    ): Single<Match3D2DIdScanResponse>

    fun getSessionFaceTec(
        authedHeaders: AuthenticatedHeaders
    ): Single<SessionFaceTecResponse>
}