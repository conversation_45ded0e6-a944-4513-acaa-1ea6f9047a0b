package com.scb.techx.ekycframework.domain.getsession.repository

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.getsession.model.SessionTokenResponse
import io.reactivex.rxjava3.core.Single

interface GetSessionRepository {
    fun getSessionToken(
        authedHeaders: AuthenticatedHeaders
    ): Single<SessionTokenResponse>
}