package com.scb.techx.ekycframework.ui.processor.photoidscan.processor

import android.content.Context
import com.facetec.sdk.FaceTecIDScanProcessor
import com.facetec.sdk.FaceTecIDScanResult
import com.facetec.sdk.FaceTecIDScanResultCallback
import com.facetec.sdk.FaceTecIDScanStatus
import com.facetec.sdk.FaceTecSDK
import com.facetec.sdk.FaceTecSessionActivity
import com.scb.techx.ekycframework.Constants
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.data.facetec.api.FaceTecAPI
import com.scb.techx.ekycframework.data.facetec.datarepository.FaceTecDataRepository
import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.ui.processor.photoidscan.presenter.PhotoIDScanProcessorContract
import com.scb.techx.ekycframework.ui.processor.photoidscan.presenter.PhotoIDScanProcessorPresenter
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

class PhotoIDScanProcessor(
    private val context: Context,
    private val checkExpiredIdCard: Boolean = true
) : PhotoIDScanProcessorContract.Processor, FaceTecIDScanProcessor {
    private val repository: FaceTecRepository by lazy {
        FaceTecDataRepository(GetApiClientUseCase.getApiClient().create(FaceTecAPI::class.java))
    }
    private var pref = EkycPreferenceUtil(context)
    val presenter: PhotoIDScanProcessorPresenter

    var success: Boolean = false

    private var headersProvider = ApiMainHeadersProvider()

    init {
        FaceTecSessionActivity.createAndLaunchSession(
            context,
            this@PhotoIDScanProcessor,
            pref.sessionFaceTec
        )
        presenter = PhotoIDScanProcessorPresenter(
            this,
            pref,
            Schedulers.io(),
            AndroidSchedulers.mainThread(),
            repository
        )
    }

    override fun processIDScanWhileFaceTecSDKWaits(
        faceTecIDScanResult: FaceTecIDScanResult,
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback
    ) {
        if (faceTecIDScanResult.status != FaceTecIDScanStatus.SUCCESS) {
            ClearTokenUseCase.execute(pref)
            HandleCallback.ocrResultsCallback?.onSuccess(
                success = false,
                description = Constants.EkycCallbackMessage.USER_CANCELLED,
                userOcrValue = null,
                userConfirmedValue = null
            )
            faceTecIDScanResultCallback.cancel()
            return
        }

        if (faceTecIDScanResult.frontImagesCompressedBase64.size > 0 && faceTecIDScanResult.backImagesCompressedBase64.size <= 0) {
            scanFrontImage(
                faceTecIDScanResult,
                faceTecIDScanResultCallback
            )
        } else if (faceTecIDScanResult.frontImagesCompressedBase64.size > 0 && faceTecIDScanResult.backImagesCompressedBase64.size > 0) {
            scanBackImage(
                faceTecIDScanResult,
                faceTecIDScanResultCallback
            )
        }
    }

    private fun scanFrontImage(
        faceTecIDScanResult: FaceTecIDScanResult,
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback
    ) {
        val frontImagesCompressedBase64 = faceTecIDScanResult.frontImagesCompressedBase64[0]

        val request = Match3D2DIdScanFrontRequest(
            idScan = faceTecIDScanResult.idScanBase64,
            idScanFrontImage = frontImagesCompressedBase64,
            enableConfirmInfo = pref.enableConfirmInfo
        )

        val authedHeaders = headersProvider.getAuthenticatedHeaders(
            authorization = Config.token,
            deviceKey = pref.deviceKey,
            userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId),
            sessionId = Config.x_session_id,
            tid = GetDeviceSettingUseCase.getUUID(),
            ekycToken = pref.ekycToken,
            correlationId = GetDeviceSettingUseCase.getUUID(),
            sdkVersion = GetDeviceSettingUseCase.getVersionName(),
        )

        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback,
            authedHeaders,
            request
        )
    }

    private fun scanBackImage(
        faceTecIDScanResult: FaceTecIDScanResult,
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback
    ) {
        val backImagesCompressedBase64 = faceTecIDScanResult.backImagesCompressedBase64[0]

        val request = Match3D2DIdScanBackRequest(
            idScan = faceTecIDScanResult.idScanBase64,
            idScanBackImage = backImagesCompressedBase64,
            enableConfirmInfo = pref.enableConfirmInfo
        )

        val authedHeaders = headersProvider.getAuthenticatedHeaders(
            authorization = Config.token,
            deviceKey = pref.deviceKey,
            userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId),
            sessionId = Config.x_session_id,
            tid = GetDeviceSettingUseCase.getUUID(),
            ekycToken = pref.ekycToken,
            correlationId = GetDeviceSettingUseCase.getUUID(),
            sdkVersion = GetDeviceSettingUseCase.getVersionName()
        )

        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback,
            authedHeaders,
            request
        )
    }

    override fun proceedToNextStepIDScan(
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
        scanResultBlob: String?
    ) {
        success = faceTecIDScanResultCallback?.proceedToNextStep(
                scanResultBlob ?: ""
            )?: false
    }

    override fun failCallbackWithMessage(description: String) {
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = false,
            description = description,
            userOcrValue = null,
            userConfirmedValue = null
        )
    }

    override fun navigateToReviewInformation(userConfirmedValueDisplay: UserConfirmedValueDisplay) {
        ReviewInformationEkycActivity.startActivity(
            context,
            userConfirmedValueDisplay,
            checkExpiredIdCard
        )
    }

    override fun successCallbackWithMessage(description: String, userOcrValue: UserConfirmedValue) {
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = true,
            description = description,
            userOcrValue = userOcrValue,
            userConfirmedValue = null
        )
    }

    override fun faceTecCancelIDScan(faceTecIDScanResultCallback: FaceTecIDScanResultCallback?) {
        faceTecIDScanResultCallback?.cancel()
    }
}