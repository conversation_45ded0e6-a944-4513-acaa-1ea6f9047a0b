package com.scb.techx.ekycframework.data.facetec.model.request

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class ConfirmationInfoRequestEntity(
    @SerializedName("checkExpiredIdCard")
    val checkExpiredIdCard: <PERSON><PERSON><PERSON>,
    @SerializedName("checkDopa")
    val checkDopa: Boolean,
    @SerializedName("data")
    val data: Data
) {
    @Keep
    data class Data(
        @SerializedName("nationalId")
        val nationalId: String,
        @SerializedName("titleTh")
        val titleTh: String,
        @SerializedName("titleEn")
        val titleEn: String,
        @SerializedName("firstNameTh")
        val firstNameTh: String,
        @SerializedName("firstNameEn")
        val firstNameEn: String,
        @SerializedName("middleNameTh")
        val middleNameTh: String,
        @SerializedName("middleNameEn")
        val middleNameEn: String,
        @SerializedName("lastNameTh")
        val lastNameTh: String,
        @SerializedName("lastNameEn")
        val lastNameEn: String,
        @SerializedName("dateOfBirth")
        val dateOfBirth: String,
        @SerializedName("dateOfIssue")
        val dateOfIssue: String,
        @SerializedName("dateOfExpiry")
        val dateOfExpiry: String,
        @SerializedName("laserId")
        val laserId: String
    )
}
