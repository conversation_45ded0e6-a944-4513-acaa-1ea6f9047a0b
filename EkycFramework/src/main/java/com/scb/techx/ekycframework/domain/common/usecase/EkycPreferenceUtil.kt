package com.scb.techx.ekycframework.domain.common.usecase

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences

@SuppressLint("CommitPrefEdits")
class EkycPreferenceUtil(context: Context) {

    private val prefsKey = "EkycSharedPreferences"

    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(prefsKey, Context.MODE_PRIVATE)
    }

    var ekycToken: String
        get() = prefs.getString("ekycToken", "") ?: ""
        set(ekycToken) {
            prefs.edit().putString("ekycToken", ekycToken).apply()
        }

    var sdkEncryptionKeyOcr: String
        get() = prefs.getString("sdkEncryptionKeyOcr", "") ?: ""
        set(sdkEncryptionKeyOcr) {
            prefs.edit().putString("sdkEncryptionKeyOcr", sdkEncryptionKeyOcr).apply()
        }

    var sdkEncryptionIv: String
        get() = prefs.getString("sdkEncryptionIv", "") ?: ""
        set(sdkEncryptionIv) {
            prefs.edit().putString("sdkEncryptionIv", sdkEncryptionIv).apply()
        }

    var installationId: String
        get() = prefs.getString("installationId", "") ?: ""
        set(installationId) {
            prefs.edit().putString("installationId", installationId).apply()
        }

    var sessionFaceTec: String
        get() = prefs.getString("sessionFaceTec", "") ?: ""
        set(sessionFaceTec) {
            prefs.edit().putString("sessionFaceTec", sessionFaceTec).apply()
        }

    var productionKey: String
        get() = prefs.getString("productionKey", "") ?: ""
        set(productionKey) {
            prefs.edit().putString("productionKey", productionKey).apply()
        }

    var deviceKey: String
        get() = prefs.getString("deviceKey", "") ?: ""
        set(deviceKey) {
            prefs.edit().putString("deviceKey", deviceKey).apply()
        }

    var encryptionKey: String
        get() = prefs.getString("encryptionKey", "") ?: ""
        set(encryptionKey) {
            prefs.edit().putString("encryptionKey", encryptionKey).apply()
        }

    var enableConfirmInfo: Boolean
        get() = prefs.getBoolean("enableConfirmInfo", true) ?: true
        set(deviceKey) {
            prefs.edit().putBoolean("enableConfirmInfo", deviceKey).apply()
        }

}