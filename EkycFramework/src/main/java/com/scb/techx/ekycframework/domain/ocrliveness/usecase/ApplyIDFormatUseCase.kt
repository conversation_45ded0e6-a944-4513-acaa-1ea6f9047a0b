package com.scb.techx.ekycframework.domain.ocrliveness.usecase

import com.scb.techx.ekycframework.Constants

class ApplyIDFormatUseCase {
    companion object {
        fun getIdCardFormat(afterChar: CharArray, format: String): String {
            val currentFormat = StringBuilder()
            var dashCount = 0
            for (i in afterChar.indices) {
                if (i + dashCount < format.length) {
                    if (format.toCharArray()[i + dashCount] == Constants.ID_SEPARATE_CHAR[0]) {
                        currentFormat.append(format.toCharArray()[i + dashCount])
                        currentFormat.append("#")
                        dashCount += 1
                    } else {
                        currentFormat.append(format.toCharArray()[i + dashCount])
                    }
                }
            }
            return currentFormat.toString()
        }

        fun applyIdPattern(text: String, format: String, isNationalId: Boolean): String {
            val pattern = StringBuilder()
            val replacement = StringBuilder()
            val formats: Array<String> = format.split("-").toTypedArray()
            var first3CharForPattern = "(\\d{"
            if (!isNationalId) {
                first3CharForPattern = "(\\w{"
            }
            for (i in formats.indices) {
                pattern.append(first3CharForPattern).append(formats[i].length).append("})")
                if (i == 0) {
                    replacement.append("$").append(i + 1)
                } else {
                    replacement.append("-" + "$").append(i + 1)
                }
            }
            return text.replaceFirst(pattern.toString().toRegex(), replacement.toString())
        }
    }
}