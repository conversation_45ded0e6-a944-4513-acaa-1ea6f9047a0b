package com.scb.techx.ekycframework.ui.assets

import android.app.Activity
import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.Window
import com.scb.techx.ekycframework.R

class EkycLoadingAlert(private val activity: Activity) {

    private lateinit var alertDialog: AlertDialog

    fun startLoading() {
        val builder: AlertDialog.Builder = AlertDialog.Builder(activity)

        val inflater: LayoutInflater = activity.layoutInflater
        builder.setView(inflater.inflate(R.layout.loading_indicator, null))
        builder.setCancelable(false)

        alertDialog = builder.create()
        alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        alertDialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        alertDialog.show()
    }

    fun dismissLoading() {
        if (alertDialog.isShowing) {
            alertDialog.dismiss()
        }
    }
}