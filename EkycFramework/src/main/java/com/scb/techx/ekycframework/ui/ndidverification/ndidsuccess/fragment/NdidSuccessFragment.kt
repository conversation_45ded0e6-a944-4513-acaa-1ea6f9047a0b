package com.scb.techx.ekycframework.ui.ndidverification.ndidsuccess.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.Constants.EMPTY
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.THEME_SETTING_ERROR
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ndidverification.themehelper.NdidThemeHelper
import com.scb.techx.ekycframework.util.EkycUtilities

private const val ARG_NDID_STATUS = "status"
private const val ARG_NDID_DESCRIPTION = "description"
private const val ARG_NDID_REFERENCE_ID = "refcode"
private const val ARG_NDID_REQUEST_ID = "request id"


class NdidSuccessFragment : Fragment() {
    lateinit var tvRefCode: TextView
    lateinit var btNext: Button
    lateinit var ivSuccess: ImageView
    lateinit var tvSuccess: TextView
    lateinit var tvDescription: TextView

    private var description: String = ""
    private var status: String? = null
    private var referenceId: String? = null
    private var requestId: String? = null
    private val ndidVerificationActivity by lazy { activity as NdidVerificationActivity }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            description = it.getString(ARG_NDID_DESCRIPTION) ?: EMPTY
            status = it.getString(ARG_NDID_STATUS)
            referenceId = it.getString(ARG_NDID_REFERENCE_ID)
            requestId = it.getString(ARG_NDID_REQUEST_ID)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initFindById(view)
        setupRefCode()
        setOnClickNext()
        setTheme()
    }

    private fun initFindById(view: View) {
        tvRefCode = view.findViewById(R.id.tv_ndid_refcode)
        tvDescription = view.findViewById(R.id.tv_ndid_success_subtext)
        tvSuccess = view.findViewById(R.id.tv_ndid_success_text)
        ivSuccess = view.findViewById(R.id.iv_ndid_success_icon)
        btNext = view.findViewById(R.id.bt_ndid_next)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.fragment_ndid_success, container, false)
    }

    private fun setupRefCode() {
        tvRefCode.text = resources.getString(R.string.Ekyc_ndid_holding_referralCode, referenceId)
    }

    private fun setOnClickNext() {
        btNext.setOnClickListener {
            ndidVerificationActivity.handleCallback(
                success = true,
                description = description,
                ndidStatus = status,
                ndidError = null,
                ndidData = EkycUtilities.NdidData(
                    referenceId = referenceId,
                    requestId = requestId
                )
            )
        }
    }

    private fun setTheme() {
        try {
            NdidThemeHelper.setButtonColor(btNext)
            NdidThemeHelper.setSecondaryColorTextTheme(tvSuccess, true)
            NdidThemeHelper.setSecondaryColorTextTheme(tvDescription)
            NdidThemeHelper.setSecondaryColorTextTheme(tvRefCode)
            NdidThemeHelper.setSuccessIcon(ivSuccess)
        } catch (e: IllegalArgumentException) {
            ndidVerificationActivity.handleCallback(
                false,
                THEME_SETTING_ERROR,
                null,
                null,
                null
            )
        }
    }

    companion object {
        @JvmStatic
        fun newInstance(description: String, status: String, referenceId: String, requestId: String) =
            NdidSuccessFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_NDID_STATUS, status)
                    putString(ARG_NDID_DESCRIPTION, description)
                    putString(ARG_NDID_REFERENCE_ID, referenceId)
                    putString(ARG_NDID_REQUEST_ID, requestId)
                }
            }
    }
}