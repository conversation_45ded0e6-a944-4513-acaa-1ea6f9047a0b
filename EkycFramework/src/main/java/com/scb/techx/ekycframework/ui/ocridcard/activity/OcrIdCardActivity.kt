package com.scb.techx.ekycframework.ui.ocridcard.activity

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.provider.Settings
import android.widget.ImageView
import androidx.core.app.ActivityCompat
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.data.ocridcard.datarepository.OcrIdCardDataRepository
import com.scb.techx.ekycframework.data.ocridcard.api.OcrIdCardAPI
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.USER_CANCELLED
import com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.fragment.EnableCameraFragment
import com.scb.techx.ekycframework.ui.ocridcard.frontidcardfragment.fragment.FrontIdCardFragment
import com.scb.techx.ekycframework.ui.ocridcard.presenter.OcrIdCardContract
import com.scb.techx.ekycframework.ui.ocridcard.presenter.OcrIdCardPresenter
import com.scb.techx.ekycframework.ui.assets.EkycLoadingAlert
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.ui.assets.EkycDialogAlert
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.Locale

class OcrIdCardActivity : AppCompatActivity(), OcrIdCardContract.View {
    private val ivCancel: ImageView by lazy { findViewById(R.id.iv_ocr_cancel) }
    private val ivLogo: ImageView by lazy { findViewById(R.id.iv_ocr_logo) }

    private val loadingAlert = EkycLoadingAlert(this)
    private val pref: EkycPreferenceUtil by lazy { EkycPreferenceUtil(this) }
    private var isCheckExpired = true
    private lateinit var presenter: OcrIdCardPresenter

    companion object {
        private const val CHECK_EXPIRED_DATE = "CheckExpiredDate"
        private const val CHECK_DOPA = "CheckDopa"

        @JvmStatic
        fun startActivity(
            context: Context?,
            checkExpiredDate: Boolean,
            checkDopa: Boolean
        ) {
            context?.let {
                val intent = Intent(context, OcrIdCardActivity::class.java)
                intent.putExtra(CHECK_EXPIRED_DATE, checkExpiredDate)
                intent.putExtra(CHECK_DOPA, checkDopa)
                it.startActivity(intent)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_ocr_id_card)

        isCheckExpired = intent.getBooleanExtra(CHECK_EXPIRED_DATE, true)

        presenter = OcrIdCardPresenter(
            this,
            pref,
            Schedulers.io(),
            AndroidSchedulers.mainThread(),
            OcrIdCardDataRepository(
                GetApiClientUseCase.getApiClient().create(OcrIdCardAPI::class.java)
            )
        )

        supportActionBar?.hide()
        presenter.start()

        HandleCallback.language?.let {
            val locale = Locale(it)
            val config = this.resources.configuration
            config.setLocale(locale)
            this.resources.updateConfiguration(config, this.resources.displayMetrics)
        }

        ivCancel.setOnClickListener {
            handleCallback(
                success = false,
                description = USER_CANCELLED,
                userOcrValue = null,
                userConfirmedValue = null,
                dopaResult = null
            )
        }
    }

    override fun hideLoadingDialog() {
        loadingAlert.dismissLoading()
    }

    override fun showLoadingDialog() {
        loadingAlert.startLoading()
    }

    override fun onFinish() {
        // Not do anything yet
    }

    override fun isCameraEnable(): Boolean {
        return ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun shouldShowRequestPermissionRationale(): Boolean {
        return shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)
    }

    override fun setShouldShowStatus() {
        val genPrefs = this.getSharedPreferences("GENERIC_PREFERENCES", Context.MODE_PRIVATE)
        val editor = genPrefs.edit()
        editor.putBoolean(Manifest.permission.CAMERA, true)
        editor.apply()
    }

    private fun getRationaleStatus(): Boolean {
        val genPrefs = this.getSharedPreferences("GENERIC_PREFERENCES", Context.MODE_PRIVATE)
        return genPrefs.getBoolean(Manifest.permission.CAMERA, false)
    }

    override fun neverAskAgainSelected(): Boolean {
        return shouldShowRequestPermissionRationale() != getRationaleStatus()
    }

    override fun launchSetting() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri = Uri.fromParts("package", packageName, null)
        intent.data = uri
        startActivity(intent)
    }

    override fun navigateToEnableCamera() {
        val cameraFragment = EnableCameraFragment.newInstance()
        supportFragmentManager.beginTransaction().apply {
            replace(R.id.fl_ocr_fragment, cameraFragment)
            commit()
        }
    }

    override fun navigateToFrontIdScan() {
        val frontIdCardFragment = FrontIdCardFragment.newInstance()
        supportFragmentManager.beginTransaction().apply {
            replace(R.id.fl_ocr_fragment, frontIdCardFragment)
            commit()
        }
    }

    override fun onBackPressed() {
        //do nothing
    }

    override fun handleCallback(
        success: Boolean,
        description: String,
        userOcrValue: UserConfirmedValue?,
        userConfirmedValue: UserConfirmedValue?,
        dopaResult: DopaResult?
    ) {
        ClearTokenUseCase.execute(pref)
        HandleCallback.ocrResultsCallback?.onSuccess(
            success,
            description,
            null,
            userConfirmedValue,
            dopaResult
        )
        this.finish()
    }

    override fun showDialog(
        message: String,
        isPositiveOnly: Boolean,
        positiveCallback: NdidVerificationActivity.PositiveCallback,
        negativeCallback: NdidVerificationActivity.NegativeCallback?,
        positiveButtonText: String?,
        negativeButtonText: String?
    ) {
        EkycDialogAlert.show(
            message = message,
            activity = this,
            isPositiveOnly = isPositiveOnly,
            positiveCallback = positiveCallback,
            negativeCallback = negativeCallback,
            positiveButtonText = positiveButtonText,
            negativeButtonText = negativeButtonText
        )
    }

    override fun handleHttpException(throwable: Throwable) {
        presenter.handleHttpException(throwable, this)
    }
}