package com.scb.techx.ekycframework.domain.common.usecase

import android.os.Build
import android.util.Base64
import com.scb.techx.ekycframework.Constants.UserAgentHandling.USER_AGENT_SPLITTER
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.ui.processor.Config.Companion.PROJECT_VERSION
import org.json.JSONObject
import retrofit2.HttpException
import java.util.UUID
import java.util.Locale

object GetDeviceSettingUseCase {

    fun getUUID(): String {
        return UUID.randomUUID().toString()
    }

    fun getVersionName(): String {
        return PROJECT_VERSION
    }

    fun getSSID(jwt: String): String {
        return if (jwt.isNotEmpty()) {
            try {
                val parts = jwt.split(".")
                val charset = charset("UTF-8")
                val payload = String(Base64.decode(parts[1].toByteArray(charset), Base64.URL_SAFE))
                JSONObject(payload).getString("ssid")
            } catch (e: Exception) {
                ""
            }
        } else {
            ""
        }

    }

    fun getCurrentLanguage(): String {
        return Locale.getDefault().language.lowercase()
    }

    private fun getRegion(): String {
        val cCode = Locale.getDefault().country
        val lCode = Locale.getDefault().language
        return lCode + "_" + cCode.uppercase()
    }

    private fun getInstallationId(pref: EkycPreferenceUtil): String {
        if (pref.installationId.isEmpty()) {
            pref.installationId = getUUID()
        }
        return pref.installationId
    }

    fun getDeviceInfo(pref: EkycPreferenceUtil): String {
        return "android|" +
                getInstallationId(pref) + USER_AGENT_SPLITTER +
                getDeviceName() + USER_AGENT_SPLITTER+
                getVersionName() + USER_AGENT_SPLITTER +
                getRegion() + USER_AGENT_SPLITTER +
                HandleCallback.language
    }

    fun getStatusCode(throwable: Throwable): String {
        val error: HttpException = throwable as HttpException
        return error.response()?.code().toString()
    }

    private fun capitalize(string: String?): String? {
        if (string == null || string.length == 0) {
            return ""
        }
        val first = string[0]
        return if (Character.isUpperCase(first)) {
            string
        } else {
            first.uppercaseChar().toString() + string.substring(1)
        }
    }

    private fun getDeviceName(): String? {
        val model = Build.MODEL

        return if (model == null) {
            "No model name"
        } else {
            capitalize(model)
        }
    }
}