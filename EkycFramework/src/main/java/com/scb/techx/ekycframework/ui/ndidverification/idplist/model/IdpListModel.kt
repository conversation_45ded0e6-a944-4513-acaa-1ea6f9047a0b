package com.scb.techx.ekycframework.ui.ndidverification.idplist.model

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
class IdpListModel(
    val nodeId: String = "",
    val industryCode: String = "",
    val companyCode: String = "",
    val marketingNameTh: String = "",
    val marketingNameEn: String = "",
    val smallIconPath: String = "",
    val mediumIconPath: String = "",
    val largeIconPath: String = "",
    var isSelected: Boolean = false
) : Parcelable