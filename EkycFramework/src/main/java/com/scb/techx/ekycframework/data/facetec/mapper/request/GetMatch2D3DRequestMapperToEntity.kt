package com.scb.techx.ekycframework.data.facetec.mapper.request

import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.data.facetec.model.request.Match3D2DIdScanBackRequestEntity
import com.scb.techx.ekycframework.data.facetec.model.request.Match3D2DIdScanFrontRequestEntity

class GetMatch2D3DRequestMapperToEntity {
    fun mapToEntity(entity: Match3D2DIdScanFrontRequest): Match3D2DIdScanFrontRequestEntity {
        return Match3D2DIdScanFrontRequestEntity(
            idScan = entity.idScan,
            idScanFrontImage = entity.idScanFrontImage,
            enableConfirmInfo = entity.enableConfirmInfo
        )
    }

    fun mapToEntity(entity: Match3D2DIdScanBackRequest): Match3D2DIdScanBackRequestEntity {
        return Match3D2DIdScanBackRequestEntity(
            idScan = entity.idScan,
            idScanBackImage = entity.idScanBackImage,
            enableConfirmInfo = entity.enableConfirmInfo
        )
    }
}