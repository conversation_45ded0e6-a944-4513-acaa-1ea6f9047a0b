package com.scb.techx.ekycframework.domain.getsession.usecase

import android.content.Context
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.domain.getsession.repository.GetSessionRepository
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1000
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.SUCCESS_MESSAGE
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.common.usecase.GetErrorMessageFromExceptionUseCase
import com.scb.techx.ekycframework.Constants.EMPTY
import io.reactivex.rxjava3.core.Scheduler

class EkycGetSessionUseCase {
    companion object {
        private val headersProvider = ApiMainHeadersProvider()

        fun execute(
            context: Context,
            repository: GetSessionRepository?,
            pref: EkycPreferenceUtil,
            sessionId: String,
            processScheduler: Scheduler,
            androidScheduler: Scheduler,
        ) {
            if (pref.ekycToken.isEmpty()) {
                handleGetSessionToken(
                    context,
                    repository,
                    pref,
                    sessionId,
                    processScheduler,
                    androidScheduler
                )
            } else {
                Config.x_session_id = GetDeviceSettingUseCase.getSSID(pref.ekycToken)
                if (Config.sessionId == Config.x_session_id) {
                    Config.isNewEkycToken = false
                    HandleCallback.initCallback?.onSuccess(true, SUCCESS_MESSAGE, pref.ekycToken)
                } else {
                    ClearTokenUseCase.execute(pref)
                    handleGetSessionToken(
                        context,
                        repository,
                        pref,
                        sessionId,
                        processScheduler,
                        androidScheduler
                    )
                }

            }
        }

        private fun handleGetSessionToken(
            context: Context,
            repository: GetSessionRepository?,
            preferenceUtil: EkycPreferenceUtil,
            sessionId: String,
            processScheduler: Scheduler,
            androidScheduler: Scheduler,
        ) {
            repository?.getSessionToken(
                headersProvider.getAuthenticatedHeaders(
                    authorization = Config.token,
                    sdkVersion = GetDeviceSettingUseCase.getVersionName(),
                    deviceInfo = GetDeviceSettingUseCase.getDeviceInfo(preferenceUtil),
                    sessionId = sessionId,
                    tid = GetDeviceSettingUseCase.getUUID(),
                    correlationId = GetDeviceSettingUseCase.getUUID()
                )
            )?.subscribeOn(processScheduler)
                ?.observeOn(androidScheduler)
                ?.subscribe({ response ->
                    if (response.code == CUS_EKYC_1000) {
                        response.data?.let {
                            preferenceUtil.ekycToken = it.ekycToken
                                ?: EMPTY
                            Config.x_session_id = GetDeviceSettingUseCase.getSSID(
                                it.ekycToken
                                    ?: EMPTY
                            )
                            Config.isNewEkycToken = true
                            HandleCallback.initCallback?.onSuccess(
                                true,
                                SUCCESS_MESSAGE,
                                it.ekycToken
                            )
                        }
                    } else {
                        HandleCallback.initCallback?.onSuccess(
                            false,
                            response.description,
                            null
                        )
                    }
                }, {
                    it.printStackTrace()
                    HandleCallback.initCallback?.onSuccess(
                        false,
                        GetErrorMessageFromExceptionUseCase.execute(it),
                        null
                    )
                })
        }
    }
}