package com.scb.techx.ekycframework.ui.reviewconfirm.assets

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.ApplyIDFormatUseCase
import com.scb.techx.ekycframework.Constants.EMPTY
import com.scb.techx.ekycframework.Constants.ID_SEPARATE_CHAR
import com.scb.techx.ekycframework.Constants.NATIONAL_ID_FORMAT
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.ui.reviewconfirm.helper.ReviewTextFieldErrorSettingHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycContract

class IDTextWatcher(
    private val editText: TextInputEditText,
    private val format: String,
    private val textInputLayout: TextInputLayout,
    private val activity: ReviewInformationEkycActivity,
    private val presenter: ReviewInformationEkycContract.Presenter
) : TextWatcher {
    private val errorSettingHelper by lazy { ReviewTextFieldErrorSettingHelper(activity, presenter) }

    private fun determineSelectionPointerAndSetFormat(
        editable: Editable,
        editText: EditText,
        format: String
    ) {
        val originalString = editable.toString()
        if (originalString.isNotEmpty()) {
            var selectionIndex = editText.selectionStart
            var strAfter = originalString
            val result: String
            val strAfterLength = strAfter.length
            strAfter = strAfter.replace(ID_SEPARATE_CHAR, EMPTY)

            if (NATIONAL_ID_FORMAT == format) {
                strAfter = strAfter.replace("[^0-9-]".toRegex(), EMPTY)
                val currentFormat = ApplyIDFormatUseCase.getIdCardFormat(strAfter.toCharArray(), format)
                result = ApplyIDFormatUseCase.applyIdPattern(strAfter, currentFormat, true)
            } else {
                strAfter = strAfter.replace("[^a-zA-Z0-9-]".toRegex(), EMPTY)
                val currentFormat = ApplyIDFormatUseCase.getIdCardFormat(strAfter.toCharArray(), format)
                result = ApplyIDFormatUseCase.applyIdPattern(strAfter, currentFormat, false)
            }
            val resultLength = result.length

            selectionIndex = if (resultLength > strAfterLength) selectionIndex +
                    (resultLength - strAfterLength) else selectionIndex - (
                    strAfterLength - resultLength)

            editable.clear()
            editText.append(result)

            try {
                editText.setSelection(selectionIndex)
            } catch (e: IndexOutOfBoundsException) {
                editText.setSelection(0)
            }
        }
    }

    override fun beforeTextChanged(text: CharSequence?, p1: Int, p2: Int, p3: Int) {
        // Do nothing
    }

    override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
        editText.setOnFocusChangeListener { _, focused ->
            if (!focused) {
                if (NATIONAL_ID_FORMAT == format) {
                    errorSettingHelper.setErrorNationalId(
                        presenter.determineValidateTypeNationalId(editText.text.toString()),
                        editText,
                        textInputLayout
                    )
                } else {
                    errorSettingHelper.setErrorLaserId(
                        presenter.determineValidateTypeLaserId(editText.text.toString()),
                        editText,
                        textInputLayout
                    )
                }
                textInputLayout.endIconMode = TextInputLayout.END_ICON_NONE
                activity.checkEnabledButton()
            }
            if (focused) {
                if (editText.text.toString().isNotEmpty()) {
                    textInputLayout.endIconMode = TextInputLayout.END_ICON_CLEAR_TEXT
                }
            }
        }
    }

    override fun afterTextChanged(editable: Editable) {
        editText.removeTextChangedListener(this)

        determineSelectionPointerAndSetFormat(
            editable,
            editText,
            format
        )

        editText.addTextChangedListener(this)
    }
}