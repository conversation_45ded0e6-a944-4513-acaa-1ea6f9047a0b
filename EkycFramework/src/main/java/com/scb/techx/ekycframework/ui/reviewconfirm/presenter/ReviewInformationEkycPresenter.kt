package com.scb.techx.ekycframework.ui.reviewconfirm.presenter

import android.content.Context
import android.content.DialogInterface
import com.scb.techx.ekycframework.Constants.EMPTY
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1000
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_3004
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7201
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7202
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType.INVALID_ID_SUM
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType.NULL_OR_EMPTY
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType.VALID
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType.WRONG_LETTER
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType.LENGTH
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType.VALIDATE_INVALID
import com.scb.techx.ekycframework.ui.base.presenter.BasePresenter
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.OcrValidateUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.CheckSumErrorUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.common.usecase.GetErrorMessageFromCodeUseCase
import com.scb.techx.ekycframework.domain.common.usecase.GetErrorMessageFromExceptionUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.GetConfirmInfoUseCase
import com.scb.techx.ekycframework.ui.reviewconfirm.model.PrefillDisplayedToggle
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay
import io.reactivex.rxjava3.core.Scheduler

class ReviewInformationEkycPresenter(
    val view: ReviewInformationEkycContract.View,
    val pref: EkycPreferenceUtil,
    val context: Context,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val repository: FaceTecRepository
) : ReviewInformationEkycContract.Presenter, BasePresenter(pref) {
    override fun getConfirmationInfo(
        request: ConfirmationInfoRequest,
        authenticatedHeaders: AuthenticatedHeaders
    ) {
        GetConfirmInfoUseCase.execute(authenticatedHeaders, request, repository)
            .subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .doOnSubscribe { view.showLoadingDialog() }
            .doOnSuccess { view.hideLoadingDialog() }
            .doOnError { view.hideLoadingDialog() }
            .subscribe({
                when (it.code) {
                    CUS_EKYC_1000 -> {
                        view.confirmationInfoCallback(it)
                    }

                    CUS_EKYC_3004 -> {
                        view.setErrorDateOfExpire(
                            context.resources.getString(R.string.Ekyc_review_expire_within_seven_days)
                        )
                    }

                    CUS_EKYC_7201 -> {
                        view.showEkycErrorDialog(
                            context.resources.getString(R.string.Ekyc_dopa_fail),
                            it
                        )
                    }
                    CUS_EKYC_7202 -> {
                        view.showEkycErrorDialog(
                            context.resources.getString(R.string.Ekyc_dopa_limit_reached),
                            it
                        )
                    }
                    else -> {
                        view.callBackToClient(GetErrorMessageFromCodeUseCase.execute(it.code))
                    }
                }
            }, {
                view.callBackToClient(GetErrorMessageFromExceptionUseCase.execute(it))
            })
    }

    override fun decideTypeOfSuccessCallBack(
        data: ConfirmationInfoResponse,
        isDopaEnable: Boolean
    ) {
        if(isDopaEnable) {
            view.callbackWithDopa(data)
        }
        else {
            view.callbackWithoutDopa(data)
        }
    }

    override fun determineValidateTypeTextEnglish(text: String?): ValidateType {
        if (text.isNullOrEmpty()) {
            return NULL_OR_EMPTY
        }
        if (!OcrValidateUseCase.isTextEnglishValid(text)) {
            return VALIDATE_INVALID
        }
        return VALID
    }

    override fun determineValidateTypeTitleEnglish(text: String?): ValidateType {
        if (text.isNullOrEmpty()) {
            return NULL_OR_EMPTY
        }
        if (!OcrValidateUseCase.isTextTitleEnglishValid(text)) {
            return VALIDATE_INVALID
        }
        return VALID
    }

    override fun determineValidateTypeTitleThai(text: String?): ValidateType {
        if (text.isNullOrEmpty()) {
            return NULL_OR_EMPTY
        }
        if (!OcrValidateUseCase.isTextTitleThaiValid(text)) {
            return VALIDATE_INVALID
        }
        return VALID
    }

    override fun determineValidateTypeTextThai(text: String?): ValidateType {
        if (text.isNullOrEmpty()) {
            return NULL_OR_EMPTY
        }
        if (!OcrValidateUseCase.isTextThaiValid(text)) {
            return VALIDATE_INVALID
        }
        return VALID
    }

    override fun determineValidateTypeNationalId(number: String?): ValidateType {
        val idNumber = number?.replace("-", "")
        if (number.isNullOrEmpty()) {
            return NULL_OR_EMPTY
        }
        idNumber?.let {
            if (it.length < 13) {
                return LENGTH
            }
        }
        if (!OcrValidateUseCase.isIdCardNumberValid(idNumber)) {
            return VALIDATE_INVALID
        }
        if (!CheckSumErrorUseCase.isValidIdNumber(idNumber?: "")) {
            return INVALID_ID_SUM
        }
        return VALID
    }

    override fun determineValidateTypeLaserId(laserId: String?): ValidateType {
        val laserIdNumber = laserId?.replace("-", "")
        if (laserIdNumber.isNullOrEmpty()) {
            return NULL_OR_EMPTY
        }

        if (!OcrValidateUseCase.isLaserIdValidLetter(laserIdNumber)) {
            return WRONG_LETTER
        }

        if (!OcrValidateUseCase.isLaserIdValid(laserIdNumber)) {
            return VALIDATE_INVALID
        }

        laserIdNumber.let {
            if (it.length < 12) {
                return LENGTH
            }
        }

        return VALID
    }

    override fun setUpNoExpirationDateCheckBoxOnCheck(isChecked: Boolean) {
        if (isChecked) {
            view.disableExpiration()
        } else {
            view.enableExpiration()
        }
    }

    override fun decideLogicForPopupPositiveButton(
        dopaData: ConfirmationInfoResponse,
        dialogInterface: DialogInterface
    ) {
        when (dopaData.code) {
            CUS_EKYC_7201, CUS_EKYC_3004 -> {
                view.dismissDialog(dialogInterface)
            }
            CUS_EKYC_7202 -> {
                view.callBackAfterFailDopa(
                    dopaData,
                    dialogInterface,
                )
            }
        }
    }

    override fun handleOcrPrefill(userConfirmedValueDisplay: UserConfirmedValueDisplay): UserConfirmedValueDisplay {
        return UserConfirmedValueDisplay(
            nationalId = userConfirmedValueDisplay.nationalId,
            titleTh = if(PrefillDisplayedToggle.titleThFlag) userConfirmedValueDisplay.titleTh else EMPTY,
            titleEn = if(PrefillDisplayedToggle.titleEnFlag) userConfirmedValueDisplay.titleEn else EMPTY,
            firstNameTh = if(PrefillDisplayedToggle.firstNameThFlag) userConfirmedValueDisplay.firstNameTh else EMPTY,
            firstNameEn = if(PrefillDisplayedToggle.firstNameEnFlag) userConfirmedValueDisplay.firstNameEn else EMPTY,
            middleNameTh = userConfirmedValueDisplay.middleNameTh,
            middleNameEn = userConfirmedValueDisplay.middleNameEn,
            lastNameTh = if(PrefillDisplayedToggle.lastNameThFlag) userConfirmedValueDisplay.lastNameTh else EMPTY,
            lastNameEn = if(PrefillDisplayedToggle.lastNameEnFlag) userConfirmedValueDisplay.lastNameEn else EMPTY,
            dateOfBirth = if(PrefillDisplayedToggle.dateOfBirthFlag) userConfirmedValueDisplay.dateOfBirth else EMPTY,
            dateOfIssue = if(PrefillDisplayedToggle.dateOfIssueFlag) userConfirmedValueDisplay.dateOfIssue else EMPTY,
            dateOfExpiry = if(PrefillDisplayedToggle.dateOfExpiryFlag) userConfirmedValueDisplay.dateOfExpiry else EMPTY,
            laserId = if(PrefillDisplayedToggle.laserIdFlag) userConfirmedValueDisplay.laserId else EMPTY
        )
    }
}