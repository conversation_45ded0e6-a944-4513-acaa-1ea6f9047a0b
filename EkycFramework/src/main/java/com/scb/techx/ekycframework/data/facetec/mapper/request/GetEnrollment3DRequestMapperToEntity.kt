package com.scb.techx.ekycframework.data.facetec.mapper.request

import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.data.facetec.model.request.Enrollment3DRequestEntity

class GetEnrollment3DRequestMapperToEntity {
    fun mapToEntity(entity: Enrollment3DRequest): Enrollment3DRequestEntity {
        return Enrollment3DRequestEntity(
            faceScan = entity.faceScan,
            auditTrailImage = entity.auditTrailImage,
            lowQualityAuditTrailImage = entity.lowQualityAuditTrailImage,
            function = entity.function
        )
    }
}