package com.scb.techx.ekycframework.domain.getsession.usecase

import android.content.Context
import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.SessionFaceTecResponse
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.ui.processor.Config
import io.reactivex.rxjava3.core.Single

class FaceTecGetSessionUseCase {
    companion object {
        fun execute(
            pref: EkycPreferenceUtil,
            context: Context,
            repository: FaceTecRepository
        ): Single<SessionFaceTecResponse> {
            return repository.getSessionFaceTec(
                ApiMainHeadersProvider().getAuthenticatedHeaders(
                    authorization = Config.token,
                    userAgent = "",
                    sessionId = Config.x_session_id,
                    tid = GetDeviceSettingUseCase.getUUID(),
                    ekycToken = pref.ekycToken,
                    correlationId = GetDeviceSettingUseCase.getUUID(),
                    sdkVersion = GetDeviceSettingUseCase.getVersionName(),
                )
            )
        }
    }
}