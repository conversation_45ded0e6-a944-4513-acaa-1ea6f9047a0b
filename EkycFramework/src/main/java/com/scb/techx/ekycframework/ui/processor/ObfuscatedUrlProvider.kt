package com.scb.techx.ekycframework.ui.processor

import android.util.Base64
import android.util.Log
import androidx.annotation.Keep

@Keep
internal object ObfuscatedUrlProvider {

    // Obfuscation keys derived from app context
    private val primaryKey = byteArrayOf(0x45, 0x6B, 0x79, 0x43, 0x32, 0x30, 0x32, 0x34)
    private val secondaryKey = "SecureEkyc".toByteArray()
    private val rotationFactor = 13

    // Decoy URLs to confuse reverse engineers
    private val decoyUrls = arrayOf(
        "aHR0cHM6Ly9hcGkuZXhhbXBsZS5jb20=",
        "aHR0cHM6Ly90ZXN0LmRvbWFpbi5jb20=",
        "aHR0cHM6Ly9kZXYuc2VydmljZS5jb20="
    )

    private fun decodeUrl(encodedParts: Array<String>, method: Int, salt: String = ""): String {
        return when (method) {
            1 -> xorBase64Decode(encodedParts, salt)
            2 -> hexCaesarDecode(encodedParts, salt)
            3 -> substitutionDecode(encodedParts, salt)
            4 -> dynamicAssemblyDecode(encodedParts)
            else -> reverseCombineDecode(encodedParts, salt)
        }
    }

    private fun xorBase64Decode(parts: Array<String>, salt: String): String {
        val combined = parts.joinToString("")
        val step1 = String(Base64.decode(combined, Base64.DEFAULT))
        val step2 = xorDecrypt(step1.toByteArray(), primaryKey)
        return String(step2).replace(salt, "")
    }

    private fun hexCaesarDecode(parts: Array<String>, salt: String): String {
        val combined = parts.joinToString("")
        val step1 = hexToBytes(combined)
        val step2 = xorDecrypt(step1, secondaryKey)
        val step3 = caesarDecrypt(String(step2), rotationFactor)
        return step3.substring(salt.length)
    }

    private fun substitutionDecode(parts: Array<String>, salt: String): String {
        val combined = parts.joinToString("")
        val step1 = String(Base64.decode(combined, Base64.DEFAULT))
        val step2 = applySubstitution(step1, createUrlSubstitutionMap())
        return step2.replace(salt, "")
    }

    // Real one - Fixed to properly decode hex-encoded URL parts
    private fun dynamicAssemblyDecode(parts: Array<String>): String {
        return try {
            parts.mapNotNull { part ->
                try {
                    // The hex strings are plain ASCII-encoded, not XOR encrypted
                    val bytes = hexToBytes(part)
                    val decoded = String(bytes, Charsets.UTF_8)
                    // Validate that the decoded part contains valid URL characters
                    if (decoded.matches(Regex("[a-zA-Z0-9.:/\\-]+"))) {
                        decoded
                    } else {
                        Log.w("ObfuscatedUrlProvider", "Invalid decoded part: $decoded")
                        null
                    }
                } catch (e: Exception) {
                    Log.e("ObfuscatedUrlProvider", "Failed to decode hex part: $part", e)
                    null
                }
            }.joinToString("")
        } catch (e: Exception) {
            Log.e("ObfuscatedUrlProvider", "Failed to decode URL parts", e)
            ""
        }
    }

    private fun reverseCombineDecode(parts: Array<String>, salt: String): String {
        val decoded = parts.map { part ->
            String(Base64.decode(part, Base64.DEFAULT))
        }.joinToString("").reversed()
        return decoded.replace(salt, "")
    }

    private fun xorDecrypt(data: ByteArray, key: ByteArray): ByteArray {
        return data.mapIndexed { index, byte ->
            (byte.toInt() xor key[index % key.size].toInt()).toByte()
        }.toByteArray()
    }

    private fun caesarDecrypt(text: String, shift: Int): String {
        return text.map { char ->
            when {
                char.isLetter() -> {
                    val base = if (char.isUpperCase()) 'A' else 'a'
                    ((char - base - shift + 26) % 26 + base.code).toChar()
                }
                else -> char
            }
        }.joinToString("")
    }

    private fun hexToBytes(hex: String): ByteArray {
        return hex.chunked(2).map { it.toInt(16).toByte() }.toByteArray()
    }

    private fun applySubstitution(text: String, substitutionMap: Map<Char, Char>): String {
        return text.map { substitutionMap[it] ?: it }.joinToString("")
    }

    private fun createUrlSubstitutionMap(): Map<Char, Char> {
        val original = "abcdefghijklmnopqrstuvwxyz.-:"
        val substituted = "nopqrstuvwxyzabcdefghijklm-:."
        return original.zip(substituted).toMap()
    }

    // Real one
    private fun validateUrl(url: String): String {
        return if (url.startsWith("https://") && url.contains(".")) {
            url
        } else {
            "https://invalid.domain.com"
        }
    }

    fun getBaseUrl(environment: String): String {
        Log.d("Heelo", validateUrl(dynamicAssemblyDecode(
            arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d6465762e6e702e", "73636274656368782e696f")
        )))
        Log.d("Heelo", validateUrl(dynamicAssemblyDecode(
            arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d616c7068612e6e702e", "73636274656368782e696f")
        )))
        Log.d("Heelo", validateUrl(dynamicAssemblyDecode(
            arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d73746167696e672e6e702e", "73636274656368782e696f")
        )))
        return when (environment.uppercase()) {
            "DEV" -> validateUrl(dynamicAssemblyDecode(
                arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d6465762e6e702e", "73636274656368782e696f")
            ))

            "SIT" -> validateUrl(dynamicAssemblyDecode(
                arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d616c7068612e6e702e", "73636274656368782e696f")
            ))

            "UAT" -> validateUrl(dynamicAssemblyDecode(
                arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d73746167696e672e6e702e", "73636274656368782e696f")
            ))

            "PT" -> validateUrl(dynamicAssemblyDecode(
                arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d70742e6e702e", "73636274656368782e696f")
            ))

            "PREPROD" -> validateUrl(dynamicAssemblyDecode(
                arrayOf("68747470733a2f2f", "656b79632d656b7963", "2d70726570726f642e6e702e", "73636274656368782e696f")
            ))

            "PROD" -> validateUrl(dynamicAssemblyDecode(
                arrayOf("68747470733a2f2f", "656b79632d656b7963", "2e73636274656368782e696f")
            ))

            else -> "https://invalid.environment.com"
        }
    }

    fun performIntegrityCheck(): Boolean {
        val testUrl = getBaseUrl("DEV")
        val expectedPattern = Regex("^https://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
        return expectedPattern.matches(testUrl)
    }

    private fun getDecoyUrl(index: Int): String {
        return if (index < decoyUrls.size) {
            String(Base64.decode(decoyUrls[index], Base64.DEFAULT))
        } else {
            "https://decoy.example.com"
        }
    }
}
