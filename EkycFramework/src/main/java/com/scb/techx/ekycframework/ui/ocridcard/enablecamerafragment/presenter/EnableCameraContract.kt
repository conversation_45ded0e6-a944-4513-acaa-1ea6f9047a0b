package com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.presenter

interface EnableCameraContract {

    interface View {
        fun navigateToScanFrontIdCard()
        fun isShowDoNotAskAgain(): Boolean
        fun navigateToSettings()
        fun requestCameraPermissionDialog()
        fun setEnableButton()
        fun setShouldShowStatus()
        fun activityFinish()
    }

    interface Presenter {
        fun setActionButton(isNeverAskAgainSelected: Boolean)
        fun setActionPermissionDialog(isCameraGranted: Boolean, isNeverAskAgainSelected: Boolean)
        fun onResume(isGranted: Boolean)
    }
}