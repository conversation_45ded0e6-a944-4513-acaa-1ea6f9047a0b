package com.scb.techx.ekycframework.data.facetec.mapper.response

import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DResponse
import com.scb.techx.ekycframework.data.facetec.model.response.Enrollment3DDataEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Enrollment3DResponseEntity

class GetEnrollment3DResponseMapperEntity {
    fun mapFromEntity(entity: Enrollment3DResponseEntity): Enrollment3DResponse {
        return Enrollment3DResponse(
            code = entity.code,
            description = entity.description,
            data = entity.data?.let {
                mapEnrollment3DData(it)
            }
        )
    }

    private fun mapEnrollment3DData(
        entity: Enrollment3DDataEntity
    ): Enrollment3DData {
        return Enrollment3DData(
            scanResultBlob = entity.scanResultBlob,
            wasProcessed = entity.wasProcessed
        )
    }
}