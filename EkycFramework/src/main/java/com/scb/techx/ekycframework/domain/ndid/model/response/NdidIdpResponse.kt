package com.scb.techx.ekycframework.domain.ndid.model.response

import androidx.annotation.Keep

@Keep
data class NdidIdpResponse(
    val code: String,
    val description: String,
    val data: IdpData?
)

@Keep
data class IdpData(
    val registeredIdpList: MutableList<IdpList>?,
    val idpList: MutableList<IdpList>?
)

@Keep
data class IdpList(
    val nodeId: String,
    val industryCode: String,
    val companyCode: String,
    val marketingNameTh: String,
    val marketingNameEn: String,
    val smallIconPath: String,
    val mediumIconPath: String,
    val largeIconPath: String,
    val deepLinkIos: String,
    val deepLinkAndroid: String,
    val deepLinkHuawei: String
)