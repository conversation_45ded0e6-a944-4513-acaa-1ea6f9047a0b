package com.scb.techx.ekycframework.ui.ndidverification.activity

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.appcompat.app.ActionBar
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.data.ndid.datarepository.NdidDataRepository
import com.scb.techx.ekycframework.data.ndid.api.NdidApi
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.THEME_SETTING_ERROR
import com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.fragment.NdidVerificationEnrollmentFragment
import com.scb.techx.ekycframework.ui.ndidverification.model.NdidVerificationEnrollmentDisplay
import com.scb.techx.ekycframework.ui.ndidverification.themehelper.NdidThemeHelper
import com.scb.techx.ekycframework.ui.ndidverification.idplist.fragment.IdpListFragment
import com.scb.techx.ekycframework.ui.ndidverification.ndidsuccess.fragment.NdidSuccessFragment
import com.scb.techx.ekycframework.ui.ndidverification.presenter.NdidVerificationContract
import com.scb.techx.ekycframework.ui.ndidverification.presenter.NdidVerificationPresenter
import com.scb.techx.ekycframework.ui.assets.EkycLoadingAlert
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.ui.assets.EkycDialogAlert
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.util.EkycUtilities
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.Locale

class NdidVerificationActivity : AppCompatActivity(), NdidVerificationContract.View {
    private val loadingAlert = EkycLoadingAlert(this)
    private val pref: EkycPreferenceUtil by lazy { EkycPreferenceUtil(this) }
    private val repository = NdidDataRepository(GetApiClientUseCase.getApiClient().create(NdidApi::class.java))
    private val presenter = NdidVerificationPresenter(
        this,
        pref,
        this,
        Schedulers.io(),
        AndroidSchedulers.mainThread(),
        repository
    )
    private val tvTitle: AppCompatTextView by lazy { findViewById(R.id.tv_ndid_title) }

    companion object {
        @JvmStatic
        fun startActivity(context: Context?) {
            context?.let {
                val intent = Intent(context, NdidVerificationActivity::class.java)
                it.startActivity(intent)
            }
        }
    }

    override fun onBackPressed() {
        //Do nothing
    }

    override fun onDestroy() {
        loadingAlert.dismissLoading()
        super.onDestroy()
    }

    private fun setTheme() {
        try {
            NdidThemeHelper.setPrimaryColorTextTheme(tvTitle, true)
        } catch (e: IllegalArgumentException) {
            handleCallback(
                false,
                THEME_SETTING_ERROR,
                null,
                null,
                null
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_ndid_verification)
        supportActionBar?.title = getString(R.string.Ekyc_ndid_navigation_title)
        supportActionBar?.displayOptions = ActionBar.DISPLAY_SHOW_CUSTOM
        supportActionBar?.setCustomView(R.layout.app_bar)
        setTheme()
        supportActionBar?.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this, R.color.colorWhite
                )
            )
        )

        presenter.start()

        HandleCallback.language?.let {
            val locale = Locale(it)
            val config = this.resources.configuration
            config.setLocale(locale)
            this.resources.updateConfiguration(config, this.resources.displayMetrics)
        }
    }

    override fun showLoadingDialog() {
        if (!isFinishing) {
            loadingAlert.startLoading()
        }
    }

    override fun hideLoadingDialog() {
        if (!isFinishing) {
            loadingAlert.dismissLoading()
        }
    }

    override fun navigateToEnrollment(
        ndidVerificationEnrollmentDisplay: NdidVerificationEnrollmentDisplay?
    ) {
        val enrollmentFragment =
            NdidVerificationEnrollmentFragment.newInstance(ndidVerificationEnrollmentDisplay)
        supportFragmentManager.beginTransaction().apply {
            replace(R.id.fl_ndid_fragment, enrollmentFragment)
            setBackButtonVisibility(true)
            commit()
        }
    }

    override fun navigateToIdpList() {
        val fragment = IdpListFragment()
        supportFragmentManager.beginTransaction().apply {
            replace(R.id.fl_ndid_fragment, fragment)
            setBackButtonVisibility(true)
            commit()
        }
    }

    override fun navigateToSuccess(description: String, status: String, referenceId: String, requestId: String) {
        val successFragment = NdidSuccessFragment.newInstance(
            description = description,
            status = status,
            referenceId = referenceId,
            requestId = requestId
        )
        supportFragmentManager.beginTransaction().apply {
            replace(R.id.fl_ndid_fragment, successFragment)
            setBackButtonVisibility(false)
            commit()
        }
    }

    override fun onFinish() {
        this.finish()
    }

    override fun handleCallback(
        success: Boolean,
        description: String,
        ndidStatus: String?,
        ndidError: EkycUtilities.NdidError?,
        ndidData: EkycUtilities.NdidData?
    ) {
        ClearTokenUseCase.execute(pref)
        HandleCallback.ndidVerificationCallback?.onResult(
            success,
            description,
            ndidStatus,
            ndidError,
            ndidData
        )
        this.finish()
    }

    interface PositiveCallback {
        fun onPositive()
    }

    interface NegativeCallback {
        fun onNegative()
    }

    override fun showDialog(
        message: String,
        isPositiveOnly: Boolean,
        positiveCallback: PositiveCallback,
        negativeCallback: NegativeCallback?,
        positiveButtonText: String?,
        negativeButtonText: String?
    ) {
        EkycDialogAlert.show(
            message = message,
            activity = this,
            isPositiveOnly = isPositiveOnly,
            positiveCallback = positiveCallback,
            negativeCallback = negativeCallback,
            positiveButtonText = positiveButtonText,
            negativeButtonText = negativeButtonText
        )
    }

    private fun setBackButtonVisibility(isShown: Boolean) {
        if (isShown) {
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            supportActionBar?.setDisplayShowHomeEnabled(true)
            val backArrow = ContextCompat.getDrawable(this, R.drawable.ic_baseline_arrow_back_24)
            backArrow?.let {
                val wrapBackArrow = DrawableCompat.wrap(backArrow)
                DrawableCompat.setTint(wrapBackArrow, Color.BLACK)
                supportActionBar?.setHomeAsUpIndicator(wrapBackArrow)
            }
        } else {
            supportActionBar?.setDisplayHomeAsUpEnabled(false)
            supportActionBar?.setDisplayShowHomeEnabled(false)
        }
    }

    override fun handleHttpException(throwable: Throwable) {
        hideLoadingDialog()
        presenter.handleHttpException(throwable, this)
    }

    override fun handleErrorEkyc(code: String) {
        presenter.handleErrorEkyc(code, this)
    }

    override fun handleNotPendingNdidStatus(response: NdidStatus) {
        presenter.handleNdidNotPendingStatus(response)
    }

    override fun isPackageInstalled(packageName: String): Boolean {
        return try {
            packageManager.getApplicationInfo(packageName, 0).enabled
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

}