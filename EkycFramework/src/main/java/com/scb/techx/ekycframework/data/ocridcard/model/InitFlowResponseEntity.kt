package com.scb.techx.ekycframework.data.ocridcard.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class InitFlowResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: InitFlowDataEntity?
)

data class InitFlowDataEntity(
    @SerializedName("sdkEncryptionKeyOcr")
    val sdkEncryptionKeyOcr: String?,
    @SerializedName("sdkEncryptionIv")
    val sdkEncryptionIv: String?,
)
