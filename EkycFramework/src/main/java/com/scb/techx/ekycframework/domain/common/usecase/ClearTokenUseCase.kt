package com.scb.techx.ekycframework.domain.common.usecase

import com.scb.techx.ekycframework.Constants

object ClearTokenUseCase {
    fun execute(pref: EkycPreferenceUtil) {
        pref.ekycToken = Constants.EMPTY
        pref.sessionFaceTec = Constants.EMPTY
        pref.productionKey = Constants.EMPTY
        pref.deviceKey = Constants.EMPTY
        pref.encryptionKey = Constants.EMPTY
        pref.sdkEncryptionKeyOcr = Constants.EMPTY
        pref.sdkEncryptionIv = Constants.EMPTY
        pref.enableConfirmInfo = true
    }
}