package com.scb.techx.ekycframework.ui.reviewconfirm.assets

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import com.google.android.material.textfield.TextInputLayout
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.ui.reviewconfirm.helper.ReviewTextFieldErrorSettingHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycContract

class MandatoryFieldTextWatcher(
    private val editText: EditText,
    private val textInputLayout: TextInputLayout,
    private val isTitle: Boolean,
    private val isEnglish: Boolean,
    private val activity: ReviewInformationEkycActivity,
    private val presenter: ReviewInformationEkycContract.Presenter
) : TextWatcher {
    private val errorSettingHelper by lazy { ReviewTextFieldErrorSettingHelper(activity, presenter) }

    private fun validate(text: String): ValidateType {
        return if (isTitle && isEnglish) {
            presenter.determineValidateTypeTitleEnglish(
                text.trim()
            )
        }
        else if (isTitle && !isEnglish) {
            presenter.determineValidateTypeTitleThai(
                text.trim()
            )
        }
        else if (!isTitle && isEnglish) {
            presenter.determineValidateTypeTextEnglish(
                text.trim()
            )
        }
        else {
            presenter.determineValidateTypeTextThai(
                text.trim()
            )
        }
    }

    override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
        // Do nothing
    }

    override fun onTextChanged(text: CharSequence?, p1: Int, p2: Int, p3: Int) {
        editText.setOnFocusChangeListener { _, focused ->
            if (!focused) {
                editText.setText(text.toString().trim())
                errorSettingHelper.setErrorText(
                    validate(text.toString()),
                    editText,
                    textInputLayout,
                    isEnglish
                )
                textInputLayout.endIconMode = TextInputLayout.END_ICON_NONE
                activity.checkEnabledButton()
            }
            if (focused) {
                if (text.toString().isNotEmpty()) {
                    textInputLayout.endIconMode = TextInputLayout.END_ICON_CLEAR_TEXT
                }
                activity.checkEnabledButton()
            }
        }
    }

    override fun afterTextChanged(editable: Editable?) {
        // Do nothing
    }
}