package com.scb.techx.ekycframework.data.ndid.mapper.idplist

import com.scb.techx.ekycframework.data.ndid.model.idplist.IdpDataEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.IdpListEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidIdpResponseEntity
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpData
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpList
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidIdpResponse

class NdidIdpResponseMapperEntity {
    fun mapFromEntity(entity: NdidIdpResponseEntity): NdidIdpResponse {
        return NdidIdpResponse(
            entity.code,
            entity.description,
            transformIdpData(entity.data)
        )
    }

    private fun transformIdpData(data: IdpDataEntity?): IdpData? {
        return if (data == null) {
            null
        } else {
            IdpData(
                transformIdpList(data.registeredIdpList),
                transformIdpList(data.idpList)
            )
        }
    }

    private fun transformIdpList(data: MutableList<IdpListEntity>?): MutableList<IdpList>? {
        return data?.map { IdpList(
            nodeId = it.nodeId,
            industryCode = it.industryCode,
            companyCode = it.companyCode,
            marketingNameTh = it.marketingNameTh,
            marketingNameEn = it.marketingNameEn,
            smallIconPath = it.smallIconPath,
            mediumIconPath = it.mediumIconPath,
            largeIconPath = it.largeIconPath,
            deepLinkIos = it.deepLinkIos,
            deepLinkAndroid = it.deepLinkAndroid,
            deepLinkHuawei = it.deepLinkHuawei
        ) }?.toMutableList()
    }
}