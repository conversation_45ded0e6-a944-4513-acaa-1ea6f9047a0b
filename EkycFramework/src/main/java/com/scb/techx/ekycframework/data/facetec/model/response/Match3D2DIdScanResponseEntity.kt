package com.scb.techx.ekycframework.data.facetec.model.response

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class Match3D2DIdScanResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: Match3D2DIdScanDataEntity?
)

@Keep
data class Match3D2DIdScanDataEntity(
    @SerializedName("documentData")
    val documentData: String?,
    @SerializedName("scanResultBlob")
    val scanResultBlob: String?,
    @SerializedName("wasProcessed")
    val wasProcessed: Boolean?,
    @SerializedName("ocrData")
    val ocrData: Match3D2DOcrDataEntity?
)

@Keep
data class Match3D2DOcrDataEntity(
    @SerializedName("nationalId")
    val nationalId: String?,
    @SerializedName("titleTh")
    val titleTh: String?,
    @SerializedName("titleEn")
    val titleEn: String?,
    @SerializedName("firstNameTh")
    val firstNameTh: String?,
    @SerializedName("middleNameTh")
    val middleNameTh: String?,
    @SerializedName("lastNameTh")
    val lastNameTh: String?,
    @SerializedName("firstNameEn")
    val firstNameEn: String?,
    @SerializedName("middleNameEn")
    val middleNameEn: String?,
    @SerializedName("lastNameEn")
    val lastNameEn: String?,
    @SerializedName("dateOfBirth")
    val dateOfBirth: String?,
    @SerializedName("dateOfBirthFlag")
    val dateOfBirthFlag: String?,
    @SerializedName("dateOfIssue")
    val dateOfIssue: String?,
    @SerializedName("dateOfExpiry")
    val dateOfExpiry: String?,
    @SerializedName("laserId")
    val laserId: String?
)