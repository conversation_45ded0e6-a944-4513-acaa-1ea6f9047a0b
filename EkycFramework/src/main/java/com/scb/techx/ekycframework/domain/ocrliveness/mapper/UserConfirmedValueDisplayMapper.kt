package com.scb.techx.ekycframework.domain.ocrliveness.mapper

import com.google.gson.Gson
import com.scb.techx.ekycframework.domain.ocrliveness.model.ocr.DocumentData
import com.scb.techx.ekycframework.domain.ocrliveness.model.ocr.Fields
import com.scb.techx.ekycframework.domain.ocrliveness.model.ocr.Groups
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay

class UserConfirmedValueDisplayMapper {
    fun transform(
        documentData: String?
    ): UserConfirmedValueDisplay? {
        val gson = Gson()
        val data = gson.fromJson(documentData, DocumentData::class.java)

        data.scannedValues?.let {
            return findInformationUserInfoGroup(it.groups)
        }
        return null
    }

    private fun updateUserInfoInUserConfirmedValue(
        fields: List<Fields>,
        userConfirmedValueDisplay: UserConfirmedValueDisplay
    ): UserConfirmedValueDisplay {
        for (field in fields) {
            when {
                field.fieldKey.equals("firstName") -> {
                    val firstnameWithPrefix = field.value.toString()
                    val nameList = firstnameWithPrefix.split(" ", limit = 2)
                    if (nameList.size > 1) {
                        userConfirmedValueDisplay.titleEn = nameList[0]
                        userConfirmedValueDisplay.firstNameEn = nameList[1]
                    } else {
                        userConfirmedValueDisplay.firstNameEn = firstnameWithPrefix
                    }
                }
                field.fieldKey.equals("lastName") -> {
                    userConfirmedValueDisplay.lastNameEn = field.value.toString()
                }
                field.fieldKey.equals("dateOfBirth") -> {
                    userConfirmedValueDisplay.dateOfBirth = field.value.toString()
                }
                field.fieldKey.equals("fullName") -> {
                    val fullNameTH = field.value.toString()
                    val nameThList = fullNameTH.trim().split(" ", limit = 3)
                    if (nameThList.size > 2) {
                        userConfirmedValueDisplay.titleTh = nameThList[0]
                        userConfirmedValueDisplay.firstNameTh = nameThList[1]
                        userConfirmedValueDisplay.lastNameTh = nameThList[2]
                    }
                }
            }
        }
        return userConfirmedValueDisplay;
    }

    private fun updateIdInfoInUserConfirmedValue(
        fields: List<Fields>,
        userConfirmedValueDisplay: UserConfirmedValueDisplay
    ): UserConfirmedValueDisplay {
        for (field in fields) {
            when {
                field.fieldKey.equals("idNumber") -> {
                    var nationalId = field.value.toString()
                    nationalId = nationalId.replace("[^0-9]".toRegex(), "")
                    userConfirmedValueDisplay.nationalId = nationalId
                }
                field.fieldKey.equals("dateOfIssue") -> {
                    userConfirmedValueDisplay.dateOfIssue = field.value.toString()
                }
                field.fieldKey.equals("dateOfExpiration") -> {
                    userConfirmedValueDisplay.dateOfExpiry = field.value.toString()
                }
            }
        }

        return userConfirmedValueDisplay;
    }

    private fun updateLaserIdInfoInUserConfirmedValue(
        fields: List<Fields>,
        userConfirmedValueDisplay: UserConfirmedValueDisplay
    ): UserConfirmedValueDisplay {
        for (field in fields) {
            when {
                field.fieldKey.equals("customField1") -> {
                    var laserId = field.value.toString()
                    laserId = laserId.replace("[^A-Z0-9]".toRegex(), "")
                    userConfirmedValueDisplay.laserId = laserId
                }
            }
        }

        return userConfirmedValueDisplay;
    }

    private fun findInformationUserInfoGroup(groups: List<Groups>): UserConfirmedValueDisplay {
        var userConfirmedValueDisplay = UserConfirmedValueDisplay()
        for (group in groups) {
            when {
                group.groupKey.equals("userInfo") -> {
                    val fields = group.fields
                    userConfirmedValueDisplay = updateUserInfoInUserConfirmedValue(
                        fields,
                        userConfirmedValueDisplay
                    )
                }
                group.groupKey.equals("idInfo") -> {
                    val fields = group.fields
                    userConfirmedValueDisplay = updateIdInfoInUserConfirmedValue(
                        fields,
                        userConfirmedValueDisplay
                    )
                }
                group.groupKey.equals("customFields") -> {
                    val fields = group.fields
                    userConfirmedValueDisplay = updateLaserIdInfoInUserConfirmedValue(
                        fields,
                        userConfirmedValueDisplay
                    )
                }
            }
        }

        return userConfirmedValueDisplay
    }
}