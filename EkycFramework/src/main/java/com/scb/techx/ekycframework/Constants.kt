package com.scb.techx.ekycframework

import androidx.annotation.Keep

@Keep
object Constants {

    const val TH_LOCALE = "th"
    const val EN_LOCALE = "en"
    const val ContentType = "Content-Type: application/json"
    const val DATE_FORMAT = "dd/MM/y"
    const val DATE_OCR_FORMAT = "ddMMMyyyy"
    const val DATE_PARSE_FORMAT = "dd/MM/yyyy"
    const val NATIONAL_ID_FORMAT = "X-XXXX-XXXXX-XX-X"
    const val LASER_ID_FORMAT = "###-#######-##"
    const val ID_SEPARATE_CHAR = "-"
    const val DATE_STRING_FORMAT = "%s/%s/%s"
    const val NO_VALUE_FIELD_CHAR = "-"
    const val DOUBLE_DASH = "--"
    const val DATE_SPLITTER = "/"
    const val EMPTY_DATE_SEND_TO_BACKEND = "01"
    const val LIVENESS = "liveness"
    const val OCR = "ocr"
    const val PACKAGE_NAME_GOOGLE_PLAY = "com.android.vending"
    const val EMPTY = ""
    const val TIME_SEPARATOR = ":"
    const val ZERO = "0"
    const val HEX_CODE_SHARP = "#"

    const val EXPIRE_DATE = "31/12/2099"

    const val ONE_HOUR: Long = 3600000
    const val ONE_MINUTE: Long = 60000
    const val ONE_SECOND: Long = 1000

    object UserAgentHandling {
        const val USER_AGENT_SPLITTER = "|"
        const val LOCATION_OF_INSTALLATION_ID = 5
    }

    object EkycStatusCode {
        const val CUS_EKYC_1000 = "CUS-KYC-1000"
        const val CUS_EKYC_1001 = "CUS-KYC-1001"
        const val CUS_EKYC_1002 = "CUS-KYC-1002"
        const val CUS_EKYC_1003 = "CUS-KYC-1003"
        const val CUS_EKYC_1004 = "CUS-KYC-1004"
        const val CUS_EKYC_1899 = "CUS-KYC-1899"
        const val CUS_EKYC_1999 = "CUS-KYC-1999"
        const val CUS_EKYC_2001 = "CUS-KYC-2001"
        const val CUS_EKYC_2002 = "CUS-KYC-2002"
        const val CUS_EKYC_2003 = "CUS-KYC-2003"
        const val CUS_EKYC_2004 = "CUS-KYC-2004"
        const val CUS_EKYC_3001 = "CUS-KYC-3001"
        const val CUS_EKYC_3002 = "CUS-KYC-3002"
        const val CUS_EKYC_3003 = "CUS-KYC-3003"
        const val CUS_EKYC_3004 = "CUS-KYC-3004"
        const val CUS_EKYC_4001 = "CUS-KYC-4001"
        const val CUS_EKYC_7101 = "CUS-KYC-7101"
        const val CUS_EKYC_7102 = "CUS-KYC-7102"
        const val CUS_EKYC_7103 = "CUS-KYC-7103"
        const val CUS_EKYC_7105 = "CUS-KYC-7105"
        const val CUS_EKYC_7201 = "CUS-KYC-7201"
        const val CUS_EKYC_7202 = "CUS-KYC-7202"
        const val CUS_EKYC_7301 = "CUS-KYC-7301"
        const val CUS_EKYC_9001 = "CUS-KYC-9001"
        const val CUS_EKYC_9002 = "CUS-KYC-9002"
        const val SUCCESS = "200"
        const val TIMEOUT = "504"
        const val INVALID_TOKEN = "401"
    }

    object DateOfBirthFlag {
        const val DAY_MONTH_YEAR = "0"
        const val MONTH_YEAR = "1"
        const val YEAR_ONLY = "2"
    }

    object AllowedSpecialCharacter {
        const val DASH = '-'
        const val DOT = '.'
        const val SPACE = ' '
    }

    object NdidStatus {
        const val NDID_ERROR_CODE_40200 = "40200"
        const val USER_SELECT_IDP = "user-select-idp"
        const val IDP_PENDING = "idp-pending"
        const val AS_DATA_COMPLETED = "as-data-completed"
        const val IDP_ERRORED = "idp-errored"
        const val AS_ERRORED = "as-errored"
        const val IDP_PENDING_TIMEOUT = "idp-pending-timeout"
        const val IDP_CONFIRMED_ACCEPT_TIMEOUT = "idp-confirmed-accept-timeout"
        const val NDID_CONNECTION_FAIL = "ndid-connection-fail"
        const val IDP_CONFIRMED_REJECT = "idp-confirmed-reject"
        const val IDP_CONFIRMED_ACCEPT = "idp-confirmed-accept"
        const val USER_CANCELLED_IDP = "user-cancelled-idp"
    }

    object EkycCallbackMessage {
        const val SUCCESS_MESSAGE = "Success"
        const val TIMEOUT_MESSAGE = "Timeout"
        const val COMMON_ERROR_MESSAGE = "Unable to process"
        const val INVALID_TOKEN_MESSAGE = "Invalid token or token expired"
        const val USER_CANCELLED = "User cancelled"
        const val SESSION_TIMEOUT = "Session Timeout"

        const val THEME_SETTING_ERROR = "Please put color as #______"

        const val DES_CUS_EKYC_1001 = "Invalid ekycToken"
        const val DES_CUS_EKYC_1002 = "ekycToken expired"
        const val DES_CUS_EKYC_1003 = "Access denied"
        const val DES_CUS_EKYC_1004 = "ekycToken not enough time"
        const val DES_CUS_EKYC_1899 = COMMON_ERROR_MESSAGE
        const val DES_CUS_EKYC_1999 = COMMON_ERROR_MESSAGE
        const val DES_CUS_EKYC_2001 = COMMON_ERROR_MESSAGE
        const val DES_CUS_EKYC_2002 = COMMON_ERROR_MESSAGE
        const val DES_CUS_EKYC_2003 = "Data not found"
        const val DES_CUS_EKYC_2004 = "Unable to decrypt image"
        const val DES_CUS_EKYC_3001 = "NDID on processing."
        const val DES_CUS_EKYC_3003 = "NDID is not available time"
        const val DES_CUS_EKYC_3004 = "OCR ID card nearly expired"
        const val DES_CUS_EKYC_4001 = "Duplicate session Id"
        const val DES_CUS_EKYC_7101 = "OCR Check Liveness Fail (Enrollment-3d)"
        const val DES_CUS_EKYC_7102 = "OCR Match Photo ID and Selfie Fail (Match-3d-2d-idscan)"
        const val DES_CUS_EKYC_7103 =
            "Compare Reference Image and Selfie Fail (Match-3d-2d-face-portrait)"
        const val DES_CUS_EKYC_7201 = "DOPA Fail"
        const val DES_CUS_EKYC_7202 = "DOPA retry limit exceed"
        const val DES_CUS_EKYC_7301 = "NDID Fail"
        const val DES_CUS_EKYC_9001 = "Timeout"
        const val DES_CUS_EKYC_9002 = "Connection error"
        const val INVALID_SESSION_ID = "Invalid sessionId"
        const val TOKEN_EXP = "tokenExp"
    }
}