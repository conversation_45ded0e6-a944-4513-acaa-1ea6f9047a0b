package com.scb.techx.ekycframework.ui.ndidverification.idplist.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.ui.ndidverification.idplist.model.IdpListModel
import com.scb.techx.ekycframework.ui.ndidverification.idplist.viewholder.IdpHeaderViewHolder
import com.scb.techx.ekycframework.ui.ndidverification.idplist.viewholder.IdpListViewHolder

class IdpListAdapter(
    private val context: Context,
    private val selectionBankCallback: (String, String, String, Boolean) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var registeredIdpList: MutableList<IdpListModel> = mutableListOf()
    var idpList: MutableList<IdpListModel> = mutableListOf()
    val REGISTER = 0
    val IDP = 1
    val REGISTER_ITEM_VIEW = 2
    val IDP_ITEM_VIEW = 3

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            REGISTER, IDP -> IdpHeaderViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.idp_header_item, parent, false)
            )
            REGISTER_ITEM_VIEW -> {
                IdpListViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.idp_item_list, parent, false),
                    idpRegistered = true
                )
            }
            else ->
                IdpListViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.idp_item_list, parent, false),
                    idpRegistered = false
                )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            REGISTER -> {
                val viewHolder = holder as IdpHeaderViewHolder
                viewHolder.bind(context.resources.getString(R.string.Ekyc_ndid_idp_registered))
            }
            IDP -> {
                val viewHolder = holder as IdpHeaderViewHolder
                viewHolder.bind(context.resources.getString(R.string.Ekyc_ndid_idp_unregistered))
            }
            REGISTER_ITEM_VIEW -> {
                val viewHolder = holder as IdpListViewHolder
                viewHolder.bind(registeredIdpList[position - 1], selectionBankCallback)
            }
            else -> {
                val pos = if (registeredIdpList.size == 0) position - 1 else position - registeredIdpList.size - 2
                val viewHolder = holder as IdpListViewHolder
                viewHolder.bind(idpList[pos], selectionBankCallback)
            }
        }
    }

    override fun getItemCount(): Int {
        return if (registeredIdpList.size > 0 && idpList.size > 0) {
            1 + registeredIdpList.size + 1 + idpList.size
        } else if (registeredIdpList.size > 0 && idpList.size == 0) {
            1 + registeredIdpList.size
        } else if (registeredIdpList.size == 0 && idpList.size > 0) {
            1 + idpList.size
        } else {
            0
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (registeredIdpList.size > 0 && idpList.size > 0) {
            when {
                position == 0 -> {
                    REGISTER
                }
                position == registeredIdpList.size + 1 -> {
                    IDP
                }
                position > registeredIdpList.size + 1 -> {
                    IDP_ITEM_VIEW
                }
                else -> REGISTER_ITEM_VIEW
            }
        } else if (registeredIdpList.size > 0 && idpList.size == 0) {
            if (position == 0) {
                REGISTER
            } else {
                REGISTER_ITEM_VIEW
            }
        } else if (registeredIdpList.size == 0 && idpList.size > 0) {
            if (position == 0) {
                IDP
            } else {
                IDP_ITEM_VIEW
            }
        } else {
            IDP_ITEM_VIEW
        }
    }

    fun setData(registeredIdpList: MutableList<IdpListModel>, idpList: MutableList<IdpListModel>) {
        this.registeredIdpList = registeredIdpList
        this.idpList = idpList
    }

    fun setSelected(idpNodeId: String) {
        registeredIdpList.forEach {
            it.isSelected = it.nodeId == idpNodeId
        }
        idpList.forEach {
            it.isSelected = it.nodeId == idpNodeId
        }
        notifyDataSetChanged()
    }


}
