package com.scb.techx.ekycframework

import androidx.annotation.Keep
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.util.EkycUtilities.InitCallback
import com.scb.techx.ekycframework.util.EkycUtilities.LivenessCheckCallback
import com.scb.techx.ekycframework.util.EkycUtilities.NdidVerificationCallback
import com.scb.techx.ekycframework.util.EkycUtilities.OCRResultsCallback

@Keep
object HandleCallback {
    var language: String? = GetDeviceSettingUseCase.getCurrentLanguage()
    var initCallback: InitCallback? = null
    var ocrResultsCallback: OCRResultsCallback? = null
    var livenessCheckCallback: LivenessCheckCallback? = null
    var ndidVerificationCallback: NdidVerificationCallback? = null
}