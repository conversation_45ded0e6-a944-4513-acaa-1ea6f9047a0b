package com.scb.techx.ekycframework.domain.ocrliveness.model.response

import androidx.annotation.Keep

@Keep
data class Match3D2DIdScanResponse(
    val code: String,
    val description: String,
    val data: Match3D2DIdScanData?
)

@Keep
data class Match3D2DIdScanData(
    val documentData: String?,
    val scanResultBlob: String?,
    val wasProcessed: Boolean?,
    val ocrData: Match3D2DOcrData?
)

@Keep
data class Match3D2DOcrData(
    val nationalId: String?,
    val titleTh: String?,
    val titleEn: String?,
    val firstNameTh: String?,
    val middleNameTh: String?,
    val lastNameTh: String?,
    val firstNameEn: String?,
    val middleNameEn: String?,
    val lastNameEn: String?,
    val dateOfBirth: String?,
    val dateOfBirthFlag: String?,
    val dateOfIssue: String?,
    val dateOfExpiry: String?,
    val laserId: String?
)