package com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.fragment

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.ui.ocridcard.activity.OcrIdCardActivity
import com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.presenter.EnableCameraContract
import com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.presenter.EnableCameraPresenter

class EnableCameraFragment : Fragment(), EnableCameraContract.View {
    lateinit var ivCameraIcon: ImageView
    lateinit var tvEnableCameraHeader: TextView
    lateinit var tvEnableCameraDescription: TextView
    lateinit var btEnableCamera: Button

    private val ocrIdCardActivity by lazy { activity as OcrIdCardActivity }
    lateinit var presenter: EnableCameraPresenter

    private val requestCameraPermission = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        presenter.setActionPermissionDialog(isGranted, isShowDoNotAskAgain())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        presenter = EnableCameraPresenter(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_enable_camera, container, false)
    }

    private fun initFindByID(view: View) {
        ivCameraIcon = view.findViewById(R.id.iv_enable_camera_icon)
        tvEnableCameraHeader = view.findViewById(R.id.tv_enable_camera_header)
        tvEnableCameraDescription = view.findViewById(R.id.tv_enable_camera_description)
        btEnableCamera = view.findViewById(R.id.bt_enable_camera)
    }

    override fun setEnableButton() {
        btEnableCamera.setOnClickListener {
            presenter.setActionButton(isShowDoNotAskAgain())
        }
    }

    override fun activityFinish() {
        activity?.finish()
    }

    override fun requestCameraPermissionDialog() {
        requestCameraPermission.launch(Manifest.permission.CAMERA)
    }

    override fun navigateToScanFrontIdCard() {
        ocrIdCardActivity.navigateToFrontIdScan()
    }

    override fun isShowDoNotAskAgain(): Boolean {
        return ocrIdCardActivity.neverAskAgainSelected()
    }

    override fun setShouldShowStatus() {
        ocrIdCardActivity.setShouldShowStatus()
    }

    override fun navigateToSettings() {
        ocrIdCardActivity.launchSetting()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initFindByID(view)
        setEnableButton()
    }

    override fun onResume() {
        super.onResume()
        presenter.onResume(isAllowCameraPermission())
    }

    private fun isAllowCameraPermission(): Boolean {
        return activity?.let {
            ActivityCompat.checkSelfPermission(
                it,
                Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
        } ?: false
    }

    companion object {
        @JvmStatic
        fun newInstance() = EnableCameraFragment()
    }
}