package com.scb.techx.ekycframework.domain.ndid.usecase

import com.scb.techx.ekycframework.Constants.ONE_MINUTE
import com.scb.techx.ekycframework.Constants.ONE_SECOND
import com.scb.techx.ekycframework.Constants.EMPTY
import com.scb.techx.ekycframework.Constants.ZERO
import com.scb.techx.ekycframework.Constants.TIME_SEPARATOR
class GetTimerTextUseCase {
    companion object {
        fun getTimerText(time: Long): String {
            val minutes = time / ONE_MINUTE
            val seconds = time % ONE_MINUTE / ONE_SECOND

            var timeLeftText = EMPTY + minutes
            timeLeftText += TIME_SEPARATOR
            if (seconds < 10) timeLeftText += ZERO
            timeLeftText += seconds

            return timeLeftText
        }
    }
}