package com.scb.techx.ekycframework.ui.processor.photoidscan.presenter

import com.facetec.sdk.FaceTecIDScanResultCallback
import com.scb.techx.ekycframework.Constants
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.common.usecase.GetErrorMessageFromExceptionUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.FormatOcrFieldUseCase
import com.scb.techx.ekycframework.domain.ocrliveness.mapper.UserConfirmedValueDisplayMapper
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.GetOcrIdCardOlnlyUsaCase
import io.reactivex.rxjava3.core.Scheduler

class PhotoIDScanProcessorPresenter(
    private val processor: PhotoIDScanProcessorContract.Processor,
    private val pref: EkycPreferenceUtil,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val faceTecRepository: FaceTecRepository
) : PhotoIDScanProcessorContract.Presenter {
    private fun setThrownCallback(throwable: Throwable) {
        processor.failCallbackWithMessage(
            GetErrorMessageFromExceptionUseCase.execute(throwable)
        )
    }

    override fun sendAPIForFrontIdCardScan(
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanFrontRequest
    ) {
        GetOcrIdCardOlnlyUsaCase.executeFront(
            faceTecRepository,
            authedHeaders,
            request
        ).subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .subscribe({
                if (it.code == Constants.EkycStatusCode.CUS_EKYC_1000
                    || it.code == Constants.EkycStatusCode.CUS_EKYC_7102
                    || it.code == Constants.EkycStatusCode.CUS_EKYC_7105
                ) {
                    if (it.data?.wasProcessed == true) {
                        processor.proceedToNextStepIDScan(
                            faceTecIDScanResultCallback,
                            it.data.scanResultBlob
                        )
                    } else {
                        ClearTokenUseCase.execute(pref)
                        processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        processor.failCallbackWithMessage(it.description)
                    }
                } else {
                    ClearTokenUseCase.execute(pref)
                    processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                    processor.failCallbackWithMessage(it.description)
                }
            }, {
                it.printStackTrace()
                ClearTokenUseCase.execute(pref)
                processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                setThrownCallback(it)
            })
    }

    override fun sendAPIForBackIdCardScan(
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
        authedHeaders: AuthenticatedHeaders,
        request: Match3D2DIdScanBackRequest
    ) {
        GetOcrIdCardOlnlyUsaCase.executeBack(
            faceTecRepository,
            authedHeaders,
            request
        )
            .subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .subscribe({ response ->
                if (response.code == Constants.EkycStatusCode.CUS_EKYC_1000) {
                    if (pref.enableConfirmInfo) {
                        UserConfirmedValueDisplayMapper().transform(response.data?.documentData)?.let {
                            processor.navigateToReviewInformation(it)
                            processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        }
                    }
                    else {
                        val ocrFormatterUtil = FormatOcrFieldUseCase()
                        ClearTokenUseCase.execute(pref)
                        processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        processor.successCallbackWithMessage(
                            response.description,
                            ocrFormatterUtil.userConfirmValueMapFromOcrData(response.data?.ocrData)
                        )
                    }

                    if (response.data?.wasProcessed == true) {
                        processor.proceedToNextStepIDScan(
                            faceTecIDScanResultCallback,
                            response.data.scanResultBlob
                        )
                    } else {
                        ClearTokenUseCase.execute(pref)
                        processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                        processor.failCallbackWithMessage(response.description)
                    }
                }
                else {
                    ClearTokenUseCase.execute(pref)
                    processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
                    processor.failCallbackWithMessage(response.description)
                }

            }, {
                it.printStackTrace()
                ClearTokenUseCase.execute(pref)
                setThrownCallback(it)
                processor.faceTecCancelIDScan(faceTecIDScanResultCallback)
            })
    }
}