package com.scb.techx.ekycframework.ui.processor.photomatchid.presenter

import com.facetec.sdk.FaceTecFaceScanResultCallback
import com.facetec.sdk.FaceTecIDScanResultCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay

interface PhotoMatchIDProcessorContract {
    interface Processor {
        fun proceedToNextStepFaceScan(
            faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
            scanResultBlob: String?
        )
        fun proceedToNextStepIDScan(
            faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
            scanResultBlob: String?
        )
        fun failCallbackWithMessage(description: String)
        fun navigateToReviewInformation(
            userConfirmedValueDisplay: UserConfirmedValueDisplay
        )
        fun successCallbackWithMessage(
            description: String,
            userOcrValue: com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
        )
        fun faceTecCancelFaceScan(faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?)
        fun faceTecCancelIDScan(faceTecIDScanResultCallback: FaceTecIDScanResultCallback?)
    }

    interface Presenter {
        fun sendAPIForFaceScan(
            faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
            authedHeaders: AuthenticatedHeaders,
            request: Enrollment3DRequest
        )
        fun sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
            authedHeaders: AuthenticatedHeaders,
            request: Match3D2DIdScanFrontRequest
        )
        fun sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
            authedHeaders: AuthenticatedHeaders,
            request: Match3D2DIdScanBackRequest
        )
    }
}