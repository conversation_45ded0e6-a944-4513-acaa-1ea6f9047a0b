package com.scb.techx.ekycframework.domain.common.usecase

import android.graphics.Color
import com.scb.techx.ekycframework.Constants.HEX_CODE_SHARP

object IgnoreOpacityUseCase {
    fun execute(color: String?): String? {
        return try {
            Color.parseColor(color)
            if (color?.length == 9) {
                HEX_CODE_SHARP + color.substring(3)
            } else {
                color
            }
        } catch (e: Exception) {
            color
        }
    }
}