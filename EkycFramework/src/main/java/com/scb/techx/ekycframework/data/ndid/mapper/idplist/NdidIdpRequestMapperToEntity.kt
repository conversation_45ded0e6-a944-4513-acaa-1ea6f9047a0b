package com.scb.techx.ekycframework.data.ndid.mapper.idplist

import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidIdpRequestEntity
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequest

class NdidIdpRequestMapperToEntity {
    fun mapToEntity(data: NdidIdpRequest): NdidIdpRequestEntity {
        return NdidIdpRequestEntity(
            data.identifierType,
            data.identifierValue,
            data.serviceId
        )
    }
}