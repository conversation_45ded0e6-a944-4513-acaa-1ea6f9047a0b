package com.scb.techx.ekycframework.data.facetec.model.response

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class Enrollment3DResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: Enrollment3DDataEntity?
)

@Keep
data class Enrollment3DDataEntity(
    @SerializedName("scanResultBlob")
    val scanResultBlob: String?,
    @SerializedName("wasProcessed")
    val wasProcessed: Boolean?,
)