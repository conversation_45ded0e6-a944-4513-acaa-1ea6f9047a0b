package com.scb.techx.ekycframework.util.facetechelper.presenter

import android.content.Context
import com.facetec.sdk.FaceTecSDK
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.util.facetechelper.model.FaceTecFeatureType
import io.reactivex.rxjava3.core.Scheduler

interface FaceTecInitializeHelperContract {
    interface Helper {
        fun initializeFaceTecSDK(
            context: Context,
            callback: FaceTecSDK.InitializeCallback?,
            pref: EkycPreferenceUtil
        )

        fun openEnrollmentProcessor(
            context: Context
        )

        fun openPhotoIDScanProcessor(
            context: Context
        )

        fun openOCRLivenessProcessor(
            context: Context,
            isExpiredIdCard: Boolean
        )

        fun handleCallbackFalseOCRLiveness(
            description: String
        )

        fun handleCallbackFalseEnrollment(
            description: String
        )
    }

    interface Presenter {
        fun getSessionFaceTec(
            context: Context,
            faceTecFeature: FaceTecFeatureType,
            isExpiredIdCard: Boolean,
            pref: Ekyc<PERSON>referenceUtil,
            processScheduler: Scheduler,
            androidScheduler: Scheduler
        )

        fun openProcessor(
            success: Boolean,
            faceTecFeature: FaceTecFeatureType,
            context: Context,
            isExpiredIdCard: Boolean,
            pref: EkycPreferenceUtil
        )
    }
}