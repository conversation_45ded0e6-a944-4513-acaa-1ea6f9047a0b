package com.scb.techx.ekycframework.data.getsession.api

import com.scb.techx.ekycframework.data.getsession.model.response.SessionTokenResponseEntity
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import io.reactivex.rxjava3.core.Single
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.Url

interface GetSessionAPI {
    @GET
    fun getSessionToken(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders
    ): Single<SessionTokenResponseEntity>
}