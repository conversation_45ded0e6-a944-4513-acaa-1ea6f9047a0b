package com.scb.techx.ekycframework.domain.ndid.mapper

import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatusData
import com.scb.techx.ekycframework.ui.ndidverification.model.NdidVerificationEnrollmentDisplay
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestData

class NdidVerificationEnrollmentDisplayMapper {

    fun transform(data: NdidRequestData?):
            NdidVerificationEnrollmentDisplay {
        return NdidVerificationEnrollmentDisplay(
            data?.referenceId ?: "",
            data?.expireTime ?: 0,
            data?.idp?.marketingNameTh ?: "",
            data?.idp?.marketingNameEn ?: "",
            data?.idp?.smallIconPath ?: "",
            data?.idp?.largeIconPath ?: "",
            data?.idp?.deepLinkAndroid ?: "",
            data?.idp?.deepLinkHuawei ?: ""
        )
    }

    fun transform(data: NdidStatusData?):
            NdidVerificationEnrollmentDisplay {
        return NdidVerificationEnrollmentDisplay(
            data?.referenceId ?: "",
            data?.expireTime ?: 0,
            data?.idpStatus?.marketingNameTh ?: "",
            data?.idpStatus?.marketingNameEn ?: "",
            data?.idpStatus?.smallIconPath ?: "",
            data?.idpStatus?.largeIconPath ?: "",
            data?.idpStatus?.deepLinkAndroid ?: "",
            data?.idpStatus?.deepLinkHuawei ?: ""
        )
    }
}