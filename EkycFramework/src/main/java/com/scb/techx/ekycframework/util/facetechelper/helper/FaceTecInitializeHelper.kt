package com.scb.techx.ekycframework.util.facetechelper.helper

import com.scb.techx.ekycframework.ui.processor.photoidscan.processor.PhotoIDScanProcessor
import android.content.Context
import com.facetec.sdk.FaceTecSDK
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.data.facetec.api.FaceTecAPI
import com.scb.techx.ekycframework.data.facetec.datarepository.FaceTecDataRepository
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.ui.processor.enrollment.processor.EnrollmentProcessor
import com.scb.techx.ekycframework.ui.processor.photomatchid.processor.PhotoMatchIDProcessor
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.util.facetechelper.model.FaceTecFeatureType
import com.scb.techx.ekycframework.util.facetechelper.presenter.FaceTecInitializeHelperContract
import com.scb.techx.ekycframework.util.facetechelper.presenter.FaceTecInitializeHelperPresenter
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

class FaceTecInitializeHelper(
    context: Context,
    faceTecFeature: FaceTecFeatureType,
    isExpiredIdCard: Boolean,
    pref: EkycPreferenceUtil
): FaceTecInitializeHelperContract.Helper {
    val repository = FaceTecDataRepository(GetApiClientUseCase.getApiClient().create(FaceTecAPI::class.java))

    init {
        val presenter = FaceTecInitializeHelperPresenter(this, repository)
        presenter.getSessionFaceTec(
            context,
            faceTecFeature,
            isExpiredIdCard,
            pref,
            Schedulers.io(),
            AndroidSchedulers.mainThread()
        )
    }

    override fun openEnrollmentProcessor(
        context: Context
    ) {
        EnrollmentProcessor(
            context
        )
    }

    override fun openPhotoIDScanProcessor(context: Context) {
        PhotoIDScanProcessor(context)
    }

    override fun openOCRLivenessProcessor(
        context: Context,
        isExpiredIdCard: Boolean
    ) {
        PhotoMatchIDProcessor(
            context,
            isExpiredIdCard,
        )
    }

    override fun handleCallbackFalseOCRLiveness(description: String) {
        HandleCallback.ocrResultsCallback?.onSuccess(
            false,
            description,
            null,
            null
        )
    }

    override fun handleCallbackFalseEnrollment(description: String) {
        HandleCallback.livenessCheckCallback?.onResult(
            false,
            description
        )
    }

    override fun initializeFaceTecSDK(
        context: Context,
        callback: FaceTecSDK.InitializeCallback?,
        pref: EkycPreferenceUtil
    ) {
        FaceTecSDK.initializeInProductionMode(
            context,
            pref.productionKey,
            pref.deviceKey,
            pref.encryptionKey,
            callback
        )
    }
}