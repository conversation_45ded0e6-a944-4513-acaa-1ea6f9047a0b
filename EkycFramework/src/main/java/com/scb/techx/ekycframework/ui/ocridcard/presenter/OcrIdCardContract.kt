package com.scb.techx.ekycframework.ui.ocridcard.presenter

import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.ui.base.BasePresenter
import com.scb.techx.ekycframework.ui.base.BaseView
import com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult

interface OcrIdCardContract {
    interface View : BaseView {
        fun hideLoadingDialog()
        fun showLoadingDialog()
        fun onFinish()
        fun isCameraEnable(): Boolean
        fun launchSetting()
        fun handleCallback(
            success: <PERSON><PERSON><PERSON>,
            description: String,
            userOcrValue: UserConfirmedValue?,
            userConfirmedValue: UserConfirmedValue?,
            dopaResult: DopaResult?
        )
        fun navigateToEnableCamera()
        fun navigateToFrontIdScan()
        fun setShouldShowStatus()
        fun neverAskAgainSelected(): Boolean
    }

    interface Presenter : BasePresenter {
        fun initOcr(ocr: Boolean)
        fun handleHttpException(throwable: Throwable, view: View)
    }
}