package com.scb.techx.ekycframework.domain.ocrliveness.usecase

import com.scb.techx.ekycframework.Constants.AllowedSpecialCharacter
import java.util.regex.Pattern

class OcrValidateUseCase {
    companion object {
        private const val TITLE_EN_REGEX = "^[a-zA-Z. ]+$"
        private const val TITLE_TH_REGEX = "^[\\u0E01-\\u0E39\\u0E40-\\u0E4C. ]+\$"
        private const val NAME_EN_REGEX = "^[a-zA-Z-. ]+$"
        private const val NAME_TH_REGEX = "^[\\u0E01-\\u0E39\\u0E40-\\u0E4C-. ]+$"
        private const val THAI_NATIONAL_ID_REGEX = "^[0-9]+$"
        private const val LASER_ID_REGEX = "^([A-Z]{2}[0-9]{0,10}|[A-Z]{1,2})$"
        private const val LASER_ID_WORD_REGEX = "^[A-Z0-9]+\$"

        private fun isTwoCharactersTogether(name: String?, firstWord: Char, secondWord: Char): Boolean {
            return name != null && (name.contains("$firstWord$secondWord") ||
                    name.contains("$secondWord$firstWord") ||
                    name.contains("$firstWord$firstWord") ||
                    name.contains("$secondWord$secondWord")
                    )
        }

        @JvmStatic
        fun isTextEnglishValid(name: String?): Boolean {
            val pattern = Pattern.compile(NAME_EN_REGEX)
            if (name == null) {
                return false
            }
            return (pattern.matcher(name.trim()).matches() && !isTwoCharactersTogether(
                name,
                AllowedSpecialCharacter.DASH,
                AllowedSpecialCharacter.DOT
            )) && !isTwoCharactersTogether(
                name,
                AllowedSpecialCharacter.SPACE,
                AllowedSpecialCharacter.SPACE
            )
        }

        @JvmStatic
        fun isTextTitleEnglishValid(name: String?): Boolean {
            val pattern = Pattern.compile(TITLE_EN_REGEX)
            if (name == null) {
                return false
            }
            return pattern.matcher(name.trim()).matches()
        }

        @JvmStatic
        fun isTextTitleThaiValid(name: String?): Boolean {
            val pattern = Pattern.compile(TITLE_TH_REGEX)
            if (name == null) {
                return false
            }
            return pattern.matcher(name.trim()).matches()
        }

        @JvmStatic
        fun isTextThaiValid(name: String?): Boolean {
            val pattern = Pattern.compile(NAME_TH_REGEX)
            if (name == null) {
                return false
            }
            return pattern.matcher(name.trim()).matches() && !isTwoCharactersTogether(
                name,
                AllowedSpecialCharacter.DASH,
                AllowedSpecialCharacter.DOT
            ) && !isTwoCharactersTogether(
                name,
                AllowedSpecialCharacter.SPACE,
                AllowedSpecialCharacter.SPACE
            )
        }

        @JvmStatic
        fun isIdCardNumberValid(number: String?): Boolean {
            val pattern = Pattern.compile(THAI_NATIONAL_ID_REGEX)
            if (number === null) {
                return false
            }
            return pattern.matcher(number).matches()
        }

        @JvmStatic
        fun isLaserIdValid(laserId: String?): Boolean {
            val pattern = Pattern.compile(LASER_ID_REGEX)
            if (laserId == null) {
                return false
            }
            return pattern.matcher(laserId).matches()
        }

        @JvmStatic
        fun isLaserIdValidLetter(laserId: String?): Boolean {
            val pattern = Pattern.compile(LASER_ID_WORD_REGEX)
            if (laserId == null) {
                return false
            }
            return pattern.matcher(laserId).matches()
        }
    }
}