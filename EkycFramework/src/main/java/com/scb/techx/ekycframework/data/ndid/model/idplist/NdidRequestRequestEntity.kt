package com.scb.techx.ekycframework.data.ndid.model.idplist

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class NdidRequestRequestEntity(
    @SerializedName("idpNodeId")
    val idpNodeId: String,
    @SerializedName("idpIndustryCode")
    val idpIndustryCode: String,
    @SerializedName("idpCompanyCode")
    val idpCompanyCode: String,
    @SerializedName("idpRegistered")
    val idpRegistered: Boolean?,
    @SerializedName("identifierType")
    val identifierType: String,
    @SerializedName("identifierValue")
    val identifierValue: String,
    @SerializedName("serviceId")
    val serviceId: String
)