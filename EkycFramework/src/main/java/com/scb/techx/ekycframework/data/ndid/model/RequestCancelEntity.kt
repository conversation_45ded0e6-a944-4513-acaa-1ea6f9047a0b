package com.scb.techx.ekycframework.data.ndid.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class RequestCancelEntity(
    @SerializedName("code")
    val code: String,

    @SerializedName("description")
    val description: String,

    @SerializedName("data")
    val dataEntity: DataRequestCancelEntity?,

    @SerializedName("ndidDataEntity")
    val ndidDataEntity: NdidDataEntity?,
)

@Keep
data class NdidDataEntity(
    @SerializedName("requestId")
    val requestId: String?,
)

@Keep
data class DataRequestCancelEntity(
    @SerializedName("sessionId")
    val sessionId: String?,

    @SerializedName("referenceId")
    val referenceId: String?,

    @SerializedName("status")
    val status: String?
)
