package com.scb.techx.ekycframework.data.ndid.mapper.idplist

import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidRequestRequestEntity
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequestRequest

class NdidRequestRequestMapperToEntity {
    fun mapToEntity(data: NdidIdpRequestRequest): NdidRequestRequestEntity {
        return NdidRequestRequestEntity(
            data.idpNodeId,
            data.idpIndustryCode,
            data.idpCompanyCode,
            data.idpRegistered,
            data.identifierType,
            data.identifierValue,
            data.serviceId
        )
    }
}