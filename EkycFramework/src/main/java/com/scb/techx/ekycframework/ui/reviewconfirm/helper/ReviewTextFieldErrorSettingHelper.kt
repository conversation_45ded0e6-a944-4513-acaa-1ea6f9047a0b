package com.scb.techx.ekycframework.ui.reviewconfirm.helper

import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.textfield.TextInputLayout
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.Constants
import com.scb.techx.ekycframework.Constants.DATE_STRING_FORMAT
import com.scb.techx.ekycframework.Constants.EMPTY_DATE_SEND_TO_BACKEND
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycContract
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ReviewTextFieldErrorSettingHelper(
    val activity: ReviewInformationEkycActivity,
    val presenter: ReviewInformationEkycContract.Presenter
) {
    fun setFieldAsError(
        editText: EditText,
        textInputLayout: TextInputLayout,
        message: String
    ) {
        editText.setBackgroundResource(R.drawable.shape_review_edit_text_error)
        textInputLayout.helperText = message
    }

    fun setFieldAsNormal(
        editText: EditText,
        textInputLayout: TextInputLayout
    ) {
        editText.setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        textInputLayout.helperText = null
    }

    fun setDateFieldAsError(
        dayEditText: EditText,
        monthEditText: EditText,
        yearEditText: EditText,
        errorTextView: TextView,
        errorLayout: LinearLayout,
        message: String
    ) {
        dayEditText.setBackgroundResource(R.drawable.shape_review_edit_text_error)
        monthEditText.setBackgroundResource(R.drawable.shape_review_edit_text_error)
        yearEditText.setBackgroundResource(R.drawable.shape_review_edit_text_error)
        errorLayout.visibility = View.VISIBLE
        errorTextView.visibility = View.VISIBLE
        errorTextView.text = message
    }

    fun setDateFieldAsNormal(
        dayEditText: EditText,
        monthEditText: EditText,
        yearEditText: EditText,
        errorTextView: TextView,
        errorLayout: LinearLayout
    ) {
        dayEditText.setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        monthEditText.setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        yearEditText.setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        errorLayout.visibility = View.GONE
        errorTextView.visibility = View.GONE
    }

    fun setFieldAsDisable(
        editText: EditText
    ) {
        editText.isFocusable = false
        editText.isCursorVisible = false
        editText.isFocusableInTouchMode = false
        editText.setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        editText.setTextColor(editText.textColors.withAlpha(128))
    }

    fun setDateFieldAsDisable(
        dayEditText: EditText,
        monthEditText: EditText,
        yearEditText: EditText,
        errorTextView: TextView,
        errorLayout: LinearLayout
    ) {
        dayEditText.setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        monthEditText.setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        yearEditText.setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        errorLayout.visibility = View.GONE
        errorTextView.visibility = View.GONE
    }

    fun setErrorOptionalField(
        result: ValidateType,
        editText: EditText,
        textInputLayout: TextInputLayout,
        isEnglish: Boolean
    ) {
        val message = if (isEnglish) {
            activity.resources.getString(R.string.Ekyc_review_allow_en)
        }
        else {
            activity.resources.getString(R.string.Ekyc_review_allow_th)
        }
        if (ValidateType.VALIDATE_INVALID == result) {
            setFieldAsError(
                editText,
                textInputLayout,
                message
            )
        } else {
            setFieldAsNormal(
                editText,
                textInputLayout
            )
        }
    }

    fun setErrorLaserId(
        result: ValidateType,
        editText: EditText,
        textInputLayout: TextInputLayout
    ) {
        when (result) {
            ValidateType.NULL_OR_EMPTY -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_require)
                )
            }
            ValidateType.LENGTH -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_allow_laser)
                )
            }
            ValidateType.VALIDATE_INVALID -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_correct_error)
                )
            }
            ValidateType.WRONG_LETTER -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_allow_en_and_number)
                )
            }
            else -> {
                setFieldAsNormal(
                    editText,
                    textInputLayout
                )
            }
        }
    }

    fun setErrorNationalId(
        result: ValidateType,
        editText: EditText,
        textInputLayout: TextInputLayout,
        isEnableWhenValid: Boolean = true
    ) {
        when (result) {
            ValidateType.NULL_OR_EMPTY -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_require)
                )
            }
            ValidateType.LENGTH -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_allow_card_id)
                )
            }
            ValidateType.VALIDATE_INVALID -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_allow_number)
                )
            }
            ValidateType.INVALID_ID_SUM -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_check_sum_card_id)
                )
            }
            else -> {
                setFieldAsNormal(
                    editText,
                    textInputLayout
                )
                if (!isEnableWhenValid) {
                    editText.isClickable = false
                    setFieldAsDisable(
                        editText
                    )
                }
            }
        }
    }

    fun setErrorText(
        result: ValidateType,
        editText: EditText,
        textInputLayout: TextInputLayout,
        isEnglish: Boolean
    ) {
        val message = if (isEnglish) {
            activity.resources.getString(R.string.Ekyc_review_allow_en)
        }
        else {
            activity.resources.getString(R.string.Ekyc_review_allow_th)
        }
        when (result) {
            ValidateType.NULL_OR_EMPTY -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    activity.resources.getString(R.string.Ekyc_review_require)
                )
            }
            ValidateType.VALIDATE_INVALID -> {
                setFieldAsError(
                    editText,
                    textInputLayout,
                    message
                )
            }
            else -> {
                setFieldAsNormal(
                    editText,
                    textInputLayout
                )
            }
        }
    }

    private fun checkValidDate(date: String): Boolean {
        return try {
            var dateString = date
            dateString = dateString.replace(Constants.NO_VALUE_FIELD_CHAR, EMPTY_DATE_SEND_TO_BACKEND)
            val dateStringList = dateString.split(Constants.DATE_SPLITTER, limit = 3)
            if (dateStringList[2].length != 4) {
                false
            } else {
                if (dateStringList[0].length > 2 || dateStringList[1].length > 2) {
                    return false
                }
                val format = SimpleDateFormat(Constants.DATE_PARSE_FORMAT, Locale.US)
                format.isLenient = false
                val checkDate = format.parse(dateString)
                checkDate.before(Date())
            }
        } catch (e: ParseException) {
            false
        }
    }

    private fun checkExpireDate(date: String, isCheckExpired: Boolean): Boolean {
        return try {
            if (date.length < 9) {
                return false
            }
            val endDate = SimpleDateFormat(Constants.DATE_FORMAT, Locale.US).parse(Constants.EXPIRE_DATE)
            val format = SimpleDateFormat(Constants.DATE_PARSE_FORMAT, Locale.US)
            format.isLenient = false
            val checkDate = format.parse(date)
            if (isCheckExpired) {
                val today = format.parse(format.format(Date()))
                !checkDate.before(today) && checkDate.before(endDate)
            } else {
                checkDate.before(endDate)
            }
        } catch (e: ParseException) {
            false
        }
    }

    fun setGoneErrorDateOfBirth(
        dayEditText: EditText,
        monthEditText: EditText,
        yearEditText: EditText,
        errorTextView: TextView,
        errorLayout: LinearLayout
    ) {
        if (dayEditText.text.toString().isNotEmpty()
            && monthEditText.text.toString().isNotEmpty()
            && yearEditText.text.toString().isNotEmpty()
        ) {
            val date = String.format(
                DATE_STRING_FORMAT,
                dayEditText.text.toString(),
                monthEditText.text.toString(),
                yearEditText.text.toString()
            )

            if (checkValidDate(date)
                && !(dayEditText.text.toString() != Constants.NO_VALUE_FIELD_CHAR
                        && monthEditText.text.toString() == Constants.NO_VALUE_FIELD_CHAR)
            ) {
                setDateFieldAsNormal(
                    dayEditText,
                    monthEditText,
                    yearEditText,
                    errorTextView,
                    errorLayout
                )
            } else {
                try {
                    var dateString = date
                    dateString = dateString.replace(Constants.NO_VALUE_FIELD_CHAR, EMPTY_DATE_SEND_TO_BACKEND)
                    val format = SimpleDateFormat(Constants.DATE_PARSE_FORMAT, Locale.US)
                    format.isLenient = false
                    format.parse(dateString)
                    setDateFieldAsError(
                        dayEditText,
                        monthEditText,
                        yearEditText,
                        errorTextView,
                        errorLayout,
                        activity.resources.getString(R.string.Ekyc_review_correct_error)
                    )
                } catch (e: ParseException) {
                    setDateFieldAsError(
                        dayEditText,
                        monthEditText,
                        yearEditText,
                        errorTextView,
                        errorLayout,
                        activity.resources.getString(R.string.Ekyc_review_date_of_birth_not_avaliable)
                    )
                }
            }
        } else {
            setDateFieldAsError(
                dayEditText,
                monthEditText,
                yearEditText,
                errorTextView,
                errorLayout,
                activity.resources.getString(R.string.Ekyc_review_require)
            )
        }
    }

    fun setGoneErrorDateOfIssued(
        dayEditText: EditText,
        monthEditText: EditText,
        yearEditText: EditText,
        errorTextView: TextView,
        errorLayout: LinearLayout
    ) {
        if (dayEditText.text.toString().isNotEmpty()
            && monthEditText.text.toString().isNotEmpty()
            && yearEditText.text.toString().isNotEmpty()
        ) {
            val date = String.format(
                DATE_STRING_FORMAT,
                dayEditText.text.toString(),
                monthEditText.text.toString(),
                yearEditText.text.toString()
            )
            if (checkValidDate(date)) {
                setDateFieldAsNormal(
                    dayEditText,
                    monthEditText,
                    yearEditText,
                    errorTextView,
                    errorLayout
                )
            } else {
                try {
                    val format = SimpleDateFormat(Constants.DATE_PARSE_FORMAT, Locale.US)
                    format.isLenient = false
                    val checkDate = format.parse(date)
                    if (checkDate.after(Date())) {
                        setDateFieldAsError(
                            dayEditText,
                            monthEditText,
                            yearEditText,
                            errorTextView,
                            errorLayout,
                            activity.resources.getString(R.string.Ekyc_review_exceed_card)
                        )
                    } else {
                        setDateFieldAsError(
                            dayEditText,
                            monthEditText,
                            yearEditText,
                            errorTextView,
                            errorLayout,
                            activity.resources.getString(R.string.Ekyc_review_correct_error)
                        )
                    }
                } catch (e: ParseException) {
                    setDateFieldAsError(
                        dayEditText,
                        monthEditText,
                        yearEditText,
                        errorTextView,
                        errorLayout,
                        activity.resources.getString(R.string.Ekyc_review_correct_error)
                    )
                }
            }
        } else {
            setDateFieldAsError(
                dayEditText,
                monthEditText,
                yearEditText,
                errorTextView,
                errorLayout,
                activity.resources.getString(R.string.Ekyc_review_require)
            )
        }
    }

    fun setGoneErrorDateOfExpire(
        dayEditText: EditText,
        monthEditText: EditText,
        yearEditText: EditText,
        errorTextView: TextView,
        errorLayout: LinearLayout,
        isCheckExpired: Boolean
    ) {
        if (dayEditText.text.toString().isNotEmpty()
            && monthEditText.text.toString().isNotEmpty()
            && yearEditText.text.toString().isNotEmpty()
        ) {
            val date = String.format(
                DATE_STRING_FORMAT,
                dayEditText.text.toString(),
                monthEditText.text.toString(),
                yearEditText.text.toString()
            )
            if (checkExpireDate(date, isCheckExpired)) {
                setDateFieldAsNormal(
                    dayEditText,
                    monthEditText,
                    yearEditText,
                    errorTextView,
                    errorLayout
                )
            } else {
                try {
                    val format = SimpleDateFormat(Constants.DATE_PARSE_FORMAT, Locale.US)
                    format.isLenient = false
                    val checkDate = format.parse(date)
                    if (checkDate.before(Date())
                        && yearEditText.text.toString().length == 4
                    ) {
                        activity.setErrorDateOfExpire(
                            activity.resources.getString(R.string.Ekyc_review_expire_card)
                        )
                    } else {
                        activity.setErrorDateOfExpire(
                            activity.resources.getString(R.string.Ekyc_review_correct_error)
                        )
                    }
                } catch (e: ParseException) {
                    activity.setErrorDateOfExpire(
                        activity.resources.getString(R.string.Ekyc_review_correct_error)
                    )
                }
            }
        } else {
            activity.setErrorDateOfExpire(
                activity.resources.getString(R.string.Ekyc_review_require)
            )
        }
    }
}