package com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.data.ndid.api.NdidApi
import com.scb.techx.ekycframework.data.ndid.datarepository.NdidDataRepository
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.Constants.TH_LOCALE
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.THEME_SETTING_ERROR
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ndidverification.model.NdidVerificationEnrollmentDisplay
import com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.presenter.NdidVerificationEnrollmentContract
import com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.presenter.NdidVerificationEnrollmentPresenter
import com.scb.techx.ekycframework.ui.ndidverification.themehelper.NdidThemeHelper
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.ndid.usecase.GetTimerTextUseCase
import com.scb.techx.ekycframework.Constants.ONE_HOUR
import com.scb.techx.ekycframework.Constants.ONE_SECOND
import com.scb.techx.ekycframework.util.EkycUtilities
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

class NdidVerificationEnrollmentFragment : Fragment(), NdidVerificationEnrollmentContract.View {

    lateinit var tvHeaderEnrollment: TextView
    lateinit var tvSubHeaderEnrollment: TextView
    lateinit var tvDescriptionEnrollment: TextView
    lateinit var ivOutsideCircle: ImageView
    lateinit var ivInsideCircle: ImageView
    lateinit var tvTimer: TextView
    lateinit var tvUnderTimer: TextView
    lateinit var tvRefIdUnderTimer: TextView
    lateinit var tvHeaderBankTile: TextView
    lateinit var tvCancel: TextView
    lateinit var btVerify: Button
    lateinit var rlBankTile: RelativeLayout

    lateinit var ivLogoTile: ImageView
    lateinit var tvBankNameTile: TextView

    private var ndidVerificationEnrollmentDisplay: NdidVerificationEnrollmentDisplay? = null

    private val repository = NdidDataRepository(GetApiClientUseCase.getApiClient().create(NdidApi::class.java))


    companion object {
        private var NDID_VERIFICATION_ENROLLMENT_DISPLAY = "NDID_VERIFICATION_ENROLLMENT_DISPLAY"

        @JvmStatic
        fun newInstance(ndidVerificationEnrollmentDisplay: NdidVerificationEnrollmentDisplay?) =
            NdidVerificationEnrollmentFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(
                        NDID_VERIFICATION_ENROLLMENT_DISPLAY,
                        ndidVerificationEnrollmentDisplay
                    )
                }
            }
    }

    private val ndidVerificationActivity by lazy { activity as NdidVerificationActivity }
    lateinit var presenter: NdidVerificationEnrollmentContract.Presenter
    lateinit var pref: EkycPreferenceUtil

    private lateinit var timer: CountDownTimer
    private var timerLengthSeconds: Long = ONE_HOUR
    private val countDownInterval: Long = ONE_SECOND
    private var isFirstTime = 1

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        setHasOptionsMenu(true)
        return inflater.inflate(R.layout.fragment_ndid_enrollment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initFindByID(view)
        setTheme()
        setText()
        updateTimer()
        rlBankTile.setOnClickListener {
            presenter.onClickBankTile(
                ndidVerificationEnrollmentDisplay?.deeplinkAndroid,
                ndidVerificationEnrollmentDisplay?.deeplinkHuawei
            )
        }
        tvCancel.setOnClickListener {
            presenter.onClickCancel()
        }
        btVerify.setOnClickListener {
            presenter.getNdidStatus(true)
        }
    }

    override fun showLoadingDialog() {
        ndidVerificationActivity.showLoadingDialog()
    }

    override fun hideLoadingDialog() {
        ndidVerificationActivity.hideLoadingDialog()
    }

    override fun setTimer(timer: Long) {
        timerLengthSeconds = timer
    }

    private fun initFindByID(view: View) {
        tvHeaderEnrollment = view.findViewById(R.id.tv_ndid_header_enrollment)
        tvSubHeaderEnrollment = view.findViewById(R.id.tv_ndid_sub_header_enrollment)
        tvDescriptionEnrollment = view.findViewById(R.id.tv_ndid_description_enrollment)
        ivOutsideCircle = view.findViewById(R.id.iv_ndid_outside_circle)
        ivInsideCircle = view.findViewById(R.id.iv_ndid_inside_circle)
        tvTimer = view.findViewById(R.id.tv_timer)
        tvUnderTimer = view.findViewById(R.id.tv_ndid_under_timer)
        tvRefIdUnderTimer = view.findViewById(R.id.tv_ndid_ref_id_under_timer)
        tvHeaderBankTile = view.findViewById(R.id.tv_header_bank_tile)
        tvBankNameTile = view.findViewById(R.id.tv_ndid_bank_name_tile)
        btVerify = view.findViewById(R.id.bt_ndid_verify)
        tvCancel = view.findViewById(R.id.tv_ndid_cancel)

        rlBankTile = view.findViewById(R.id.rl_ndid_bank_deeplink_tile)
        ivLogoTile = view.findViewById(R.id.iv_ndid_bank_logo_tile)
    }

    private fun setTheme() {
        try {
            NdidThemeHelper.setPrimaryColorTextTheme(tvHeaderEnrollment, true)
            NdidThemeHelper.setPrimaryColorTextTheme(tvHeaderBankTile, true)
            NdidThemeHelper.setSecondaryColorTextTheme(tvSubHeaderEnrollment)
            NdidThemeHelper.setSecondaryColorTextTheme(tvDescriptionEnrollment)
            NdidThemeHelper.setSecondaryColorTextTheme(tvUnderTimer)
            NdidThemeHelper.setSecondaryColorTextTheme(tvRefIdUnderTimer)
            NdidThemeHelper.setSecondaryColorTextTheme(tvBankNameTile, true)
            NdidThemeHelper.setBorderTileTheme(rlBankTile)
            NdidThemeHelper.setLinkTheme(tvCancel)
            NdidThemeHelper.setBorderButtonColor(btVerify)
            NdidThemeHelper.setTimerTheme(tvTimer, ivOutsideCircle, ivInsideCircle)
        } catch (e: IllegalArgumentException) {
            ndidVerificationActivity.handleCallback(
                false,
                THEME_SETTING_ERROR,
                null,
                null,
                null
            )
        }
    }

    @SuppressLint("StringFormatInvalid")
    private fun setText() {
        Glide.with(this)
            .load(ndidVerificationEnrollmentDisplay?.largeIconPath)
            .placeholder(R.drawable.ic_default_image_sm)
            .error(R.drawable.ic_default_image_sm)
            .into(ivLogoTile)

        val currentLocale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            resources.configuration.locales.get(0)
        } else {
            resources.configuration.locale
        }

        if (currentLocale.language.equals(TH_LOCALE)) {
            tvDescriptionEnrollment.text = getString(
                R.string.Ekyc_ndid_holding_autenticate_detail,
                ndidVerificationEnrollmentDisplay?.marketNameTh
            )
            tvUnderTimer.text = getString(R.string.Ekyc_ndid_holding_verify_identification)
            tvRefIdUnderTimer.text = getString(
                R.string.Ekyc_ndid_holding_referralCode,
                ndidVerificationEnrollmentDisplay?.referenceId
            )
            tvHeaderBankTile.text = getString(
                R.string.Ekyc_ndid_holding_open,
                ndidVerificationEnrollmentDisplay?.marketNameTh
            )
            tvBankNameTile.text = ndidVerificationEnrollmentDisplay?.marketNameTh
        } else {
            tvDescriptionEnrollment.text = getString(
                R.string.Ekyc_ndid_holding_autenticate_detail,
                ndidVerificationEnrollmentDisplay?.marketNameEn
            )
            // Don't delete it! Although it is red, it's still work!
            tvUnderTimer.text = getString(
                R.string.Ekyc_ndid_holding_verify_identification,
                ndidVerificationEnrollmentDisplay?.marketNameEn
            )
            tvRefIdUnderTimer.text = getString(
                R.string.Ekyc_ndid_holding_referralCode,
                ndidVerificationEnrollmentDisplay?.referenceId
            )
            tvHeaderBankTile.text = getString(
                R.string.Ekyc_ndid_holding_open,
                ndidVerificationEnrollmentDisplay?.marketNameEn
            )
            tvBankNameTile.text = ndidVerificationEnrollmentDisplay?.marketNameEn
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        pref = EkycPreferenceUtil(ndidVerificationActivity)
        presenter = NdidVerificationEnrollmentPresenter(
            this, pref, ndidVerificationActivity,
            Schedulers.io(), AndroidSchedulers.mainThread(),
            repository
        )
        ndidVerificationEnrollmentDisplay =
            arguments?.getParcelable(NDID_VERIFICATION_ENROLLMENT_DISPLAY)
        setTimer(
            presenter.timerCalculated(
                ndidVerificationEnrollmentDisplay?.expireTime
                    ?: 0
            )
        )
        presenter.start()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            presenter.onClickCancel()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onResume() {
        super.onResume()
        if (isFirstTime == 1) {
            isFirstTime++
        } else {
            presenter.getNdidStatus(false)
        }
    }

    override fun onPause() {
        super.onPause()
        presenter.stop()
    }

    override fun startTimer() {
        timer = object : CountDownTimer(timerLengthSeconds, countDownInterval) {
            override fun onTick(long: Long) {
                setTimer(long)
                updateTimer()
            }

            override fun onFinish() {
                showLoadingDialog()
                presenter.getNdidStatus(false)
            }
        }.start()
    }

    override fun stopTimer() {
        timer.cancel()
    }

    override fun updateTimer() {
        tvTimer.text = GetTimerTextUseCase.getTimerText(timerLengthSeconds)
    }

    override fun showDialog(
        message: String,
        isPositiveOnly: Boolean,
        positiveCallback: NdidVerificationActivity.PositiveCallback,
        negativeCallback: NdidVerificationActivity.NegativeCallback?,
        positiveButtonText: String?,
        negativeButtonText: String?,
    ) {
        ndidVerificationActivity.showDialog(
            message,
            isPositiveOnly,
            positiveCallback,
            negativeCallback,
            positiveButtonText,
            negativeButtonText
        )
    }

    override fun postRequestCancel() {
        presenter.postRequestCancel()
    }

    override fun handleHttpException(throwable: Throwable) {
        ndidVerificationActivity.handleHttpException(throwable)
    }

    override fun handleErrorEkyc(code: String) {
        ndidVerificationActivity.handleErrorEkyc(code)
    }

    override fun navigateToSuccess(description: String, status: String, referenceId: String, requestId: String) {
        ndidVerificationActivity.navigateToSuccess(description, status, referenceId, requestId)
    }

    override fun navigateToIdpList() {
        ndidVerificationActivity.navigateToIdpList()
    }

    override fun navigateDeeplink(deeplink: String?) {
        val uri = Uri.parse(deeplink)
        val intent = Intent(Intent.ACTION_VIEW, uri)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(intent)
    }

    override fun handleCallbackToClient(
        success: Boolean,
        description: String,
        status: String?,
        ndidError: EkycUtilities.NdidError?,
        ndidData: EkycUtilities.NdidData?
    ) {
        ndidVerificationActivity.handleCallback(
            success,
            description,
            status,
            ndidError,
            ndidData
        )
    }

    override fun handleNotPendingNdidStatus(response: NdidStatus) {
        ndidVerificationActivity.handleNotPendingNdidStatus(response)
    }

    override fun isPackageInstalled(packageName: String): Boolean {
        return ndidVerificationActivity.isPackageInstalled(packageName)
    }

}