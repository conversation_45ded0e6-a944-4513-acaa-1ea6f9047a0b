package com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.presenter

import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.ui.base.BasePresenter
import com.scb.techx.ekycframework.ui.base.BaseView
import com.scb.techx.ekycframework.util.EkycUtilities

interface NdidVerificationEnrollmentContract {

    interface View : BaseView {
        fun startTimer()
        fun stopTimer()
        fun updateTimer()
        fun navigateToSuccess(description: String, status: String, referenceId: String, requestId: String)
        fun navigateToIdpList()
        fun setTimer(timer: Long)
        fun navigateDeeplink(deeplink: String?)
        fun isPackageInstalled(packageName: String): Boolean
        fun handleCallbackToClient(
            success: Boolean,
            description: String,
            status: String?,
            ndidError: EkycUtilities.NdidError?,
            ndidData: EkycUtilities.NdidData?
        )

        fun handleErrorEkyc(code: String)
        fun showLoadingDialog()
        fun hideLoadingDialog()
        fun postRequestCancel()
        fun handleNotPendingNdidStatus(response: NdidStatus)
    }

    interface Presenter : BasePresenter {
        fun getNdidStatus(isVerifyClick: Boolean)
        fun postRequestCancel()
        fun timerCalculated(timer: Int): Long
        fun onClickBankTile(deeplinkAndroid: String?, deeplinkHuawei: String?)
        fun onClickCancel()
    }

}