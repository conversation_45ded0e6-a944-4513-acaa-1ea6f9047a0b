package com.scb.techx.ekycframework.domain.ocrliveness.model.response

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
data class ConfirmationInfoResponse(
    val code: String,
    val description: String,
    val data: DopaData?
)

@Keep
data class DopaData(
    val dopaCode: String?,
    val dopaDesc: String?,
    val userConfirmedValue: UserConfirmedValue?,
)

@Keep
@Parcelize
data class UserConfirmedValue(
    val nationalId: String?,
    val titleTh: String?,
    val firstNameTh: String?,
    val middleNameTh: String?,
    val lastNameTh: String?,
    val titleEn: String?,
    val firstNameEn: String?,
    val middleNameEn: String?,
    val lastNameEn: String?,
    val dateOfBirth: String?,
    val dateOfIssue: String?,
    val dateOfExpiry: String?,
    val laserId: String?,
) : Parcelable