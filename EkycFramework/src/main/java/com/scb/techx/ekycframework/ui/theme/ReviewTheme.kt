package com.scb.techx.ekycframework.ui.theme

import android.graphics.Typeface

object ReviewTheme {
    var mainHeaderTextColor: String? = null
    var sectionHeaderTextColor: String? = null
    var fieldLabelTextColor: String? = null
    var borderColor: String? = null
    var normalButtonTextColor: String? = null
    var normalButtonBackgroundColor: String? = null
    var disableButtonTextColor: String? = null
    var disableButtonBackgroundColor: String? = null
    var highlightButtonTextColor: String? = null
    var highlightButtonBackgroundColor: String? = null
    var fontName: Typeface? = null
    var cancelLogo: Int? = null
    var logo: Int? = null
}