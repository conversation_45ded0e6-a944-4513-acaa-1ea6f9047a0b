package com.scb.techx.ekycframework.domain.ndid.usecase

import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequestRequest
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestResponse
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.ui.processor.Config
import io.reactivex.rxjava3.core.Single

class GetNdidRequestUseCase {
    companion object {
        fun execute(
            pref: EkycPreferenceUtil,
            idpNodeId: String,
            idpCompanyCode: String,
            idpRegistered: Boolean,
            idpIndustryCode: String,
            repository: NdidRepository
        ): Single<NdidRequestResponse> {
            val request = NdidIdpRequestRequest(
                idpNodeId,
                idpIndustryCode,
                idpCompanyCode,
                idpRegistered,
                Config.identifierType,
                Config.identifierValue,
                Config.serviceId
            )
            return repository.getNdidRequest(
                ApiMainHeadersProvider().getAuthenticatedHeaders(
                    authorization = Config.token,
                    sessionId = Config.x_session_id,
                    tid = GetDeviceSettingUseCase.getUUID(),
                    ekycToken = pref.ekycToken,
                    correlationId = GetDeviceSettingUseCase.getUUID()
                ), request
            )
        }
    }
}