package com.scb.techx.ekycframework.data.ocridcard.datarepository

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.data.ocridcard.api.OcrIdCardAPI
import com.scb.techx.ekycframework.data.ocridcard.mapper.InitFlowRequestMapperToEntity
import com.scb.techx.ekycframework.data.ocridcard.mapper.InitFlowResponseMapperEntity
import com.scb.techx.ekycframework.domain.ocridcard.repository.OcrIdCardRepository
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowData
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowRequest
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowResponse
import io.reactivex.rxjava3.core.Single

class OcrIdCardDataRepository(private val service: OcrIdCardAPI) : OcrIdCardRepository {
    private val initFlowRequestMapper = InitFlowRequestMapperToEntity()
    private val initFlowResponseMapper = InitFlowResponseMapperEntity()

    override fun getInitFlow(
        authenticatedHeaders: AuthenticatedHeaders,
        request: InitFlowRequest
    ): Single<InitFlowResponse> {
        //TODO: Change later once init-flow finish
//        return service.getInitFlow(authenticatedHeaders, initFlowRequestMapper.mapToEntity(request)).map {
//            initFlowResponseMapper.mapFromEntity(it)
//        }
        return Single.just(
            InitFlowResponse(
                code = "CUS-KYC-1000",
                description = "success",
                data = InitFlowData(
                    sdkEncryptionKeyOcr = "key",
                    sdkEncryptionIv = "iv"
                )
            )
        )
    }
}