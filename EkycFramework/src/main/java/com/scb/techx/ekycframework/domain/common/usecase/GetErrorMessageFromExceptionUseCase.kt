package com.scb.techx.ekycframework.domain.common.usecase

import com.scb.techx.ekycframework.Constants
import okhttp3.internal.http2.StreamResetException
import retrofit2.HttpException

object GetErrorMessageFromExceptionUseCase {
    fun execute(throwable: Throwable): String {
        throwable.printStackTrace()
        return when(throwable) {
            is HttpException -> when (GetDeviceSettingUseCase.getStatusCode(throwable)) {
                Constants.EkycStatusCode.TIMEOUT -> {
                    Constants.EkycCallbackMessage.TIMEOUT_MESSAGE
                }
                Constants.EkycStatusCode.INVALID_TOKEN -> {
                    Constants.EkycCallbackMessage.INVALID_TOKEN_MESSAGE
                }
                else -> {
                    Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE
                }
            }
            is StreamResetException -> {
                Constants.EkycCallbackMessage.DES_CUS_EKYC_9002
            }
            else -> {
                Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE
            }
        }
    }
}