package com.scb.techx.ekycframework.ui.ndidverification.idplist.presenter

import android.content.Context
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1000
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_2003
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7301
import com.scb.techx.ekycframework.ui.base.presenter.BasePresenter
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.domain.ndid.mapper.NdidVerificationEnrollmentDisplayMapper
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ndid.usecase.GetIdpListUseCase
import com.scb.techx.ekycframework.domain.ndid.usecase.GetNdidRequestUseCase
import io.reactivex.rxjava3.core.Scheduler

class NdidIdpListPresenter(
    private val view: NdidIdpListContract.View,
    private val pref: EkycPreferenceUtil,
    private val context: Context,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val repository: NdidRepository
) : BasePresenter(pref), NdidIdpListContract.Presenter {
    override fun getIdpList() {
        GetIdpListUseCase.execute(pref, repository)
            .subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .doOnSubscribe { view.showLoadingDialog() }
            .doOnSuccess { view.hideLoadingDialog() }
            .doOnError { view.hideLoadingDialog() }
            .subscribe({
                when (it.code) {
                    CUS_EKYC_1000 -> {
                        view.callBackIdpResponse(it.data)
                    }
                    CUS_EKYC_7301, CUS_EKYC_2003 -> {
                        view.showDialog(
                            context.resources.getString(R.string.Ekyc_ndid_idp_error),
                            true,
                            object : NdidVerificationActivity.PositiveCallback {
                                override fun onPositive() {
                                    view.handleErrorEkyc(it.code)
                                }
                            },
                            null
                        )
                    }
                    else -> {
                        view.showDialog(
                            context.resources.getString(R.string.Ekyc_ndid_holding_proceed_error),
                            true,
                            object : NdidVerificationActivity.PositiveCallback {
                                override fun onPositive() {
                                    getIdpList()
                                }
                            },
                            null
                        )
                    }
                }
            }, { throwable ->
                view.handleHttpException(throwable)
            })
    }

    override fun getNdidRequest(
        idpNodeId: String,
        idpCompanyCode: String,
        idpRegistered: Boolean,
        idpIndustryCode: String
    ) {
        GetNdidRequestUseCase.execute(pref, idpNodeId, idpCompanyCode, idpRegistered, idpIndustryCode, repository)
            .subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .doOnSubscribe { view.showLoadingDialog() }
            .doOnSuccess { view.hideLoadingDialog() }
            .doOnError { view.hideLoadingDialog() }
            .subscribe({
                when (it.code) {
                    CUS_EKYC_1000 -> {
                        view.navigateToCountDown(
                            NdidVerificationEnrollmentDisplayMapper().transform(it.data)
                        )
                    }
                    else -> {
                        view.handleErrorEkyc(it.code)
                    }
                }

            }, { throwable ->
                view.handleHttpException(throwable)
            })
    }

    override fun start() {
        // No implementation
    }

    override fun stop() {
        // No implementation
    }
}