package com.scb.techx.ekycframework.data.ocridcard.mapper

import com.scb.techx.ekycframework.data.ocridcard.model.InitFlowRequestEntity
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowRequest

class InitFlowRequestMapperToEntity {
    fun mapToEntity(entity: InitFlowRequest): InitFlowRequestEntity {
        return InitFlowRequestEntity(
            ocr = if (entity.ocr) {
                "1"
            } else {
                "0"
            }
        )
    }
}