package com.scb.techx.ekycframework.data.facetec.mapper.request

import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.data.facetec.model.request.ConfirmationInfoRequestEntity

class GetConfirmationInfoRequestMapperToEntity {
    fun mapToEntity(entity: ConfirmationInfoRequest): ConfirmationInfoRequestEntity {
        return ConfirmationInfoRequestEntity(
            checkExpiredIdCard = entity.checkExpiredIdCard,
            checkDopa = entity.checkDopa,
            data = mapConfirmInfoRequestData(entity.data)
        )
    }

    private fun mapConfirmInfoRequestData(
        entity: ConfirmationInfoRequest.Data
    ): ConfirmationInfoRequestEntity.Data {
        return ConfirmationInfoRequestEntity.Data(
            nationalId = entity.nationalId,
            titleEn = entity.titleEn,
            firstNameEn = entity.firstNameEn,
            middleNameEn = entity.middleNameEn,
            lastNameEn = entity.lastNameEn,
            titleTh = entity.titleTh,
            firstNameTh = entity.firstNameTh,
            middleNameTh = entity.middleNameTh,
            lastNameTh = entity.lastNameTh,
            dateOfIssue = entity.dateOfIssue,
            dateOfExpiry = entity.dateOfExpiry,
            dateOfBirth = entity.dateOfBirth,
            laserId = entity.laserId
        )
    }
}