package com.scb.techx.ekycframework.data.ndid.mapper

import com.scb.techx.ekycframework.data.ndid.model.DataRequestCancelEntity
import com.scb.techx.ekycframework.data.ndid.model.NdidDataEntity
import com.scb.techx.ekycframework.data.ndid.model.RequestCancelEntity
import com.scb.techx.ekycframework.domain.ndid.model.response.DataRequestCancel
import com.scb.techx.ekycframework.domain.ndid.model.response.RequestCancel
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidDataRequestCancel

class RequestCancelMapperEntity {

    fun mapFromEntity(entity: RequestCancelEntity): RequestCancel {
        return RequestCancel(
            entity.code,
            entity.description,
            transformData(entity.dataEntity),
            transformNdidData(entity.ndidDataEntity)
        )
    }

    private fun transformNdidData(entity: NdidDataEntity?): NdidDataRequestCancel {
        return NdidDataRequestCancel(
            entity?.requestId,
        )
    }

    private fun transformData(entity: DataRequestCancelEntity?): DataRequestCancel {
        return DataRequestCancel(
            entity?.sessionId,
            entity?.referenceId,
            entity?.status
        )
    }
}