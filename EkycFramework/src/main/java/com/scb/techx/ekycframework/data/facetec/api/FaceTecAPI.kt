package com.scb.techx.ekycframework.data.facetec.api

import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.data.facetec.model.request.ConfirmationInfoRequestEntity
import com.scb.techx.ekycframework.data.facetec.model.request.Enrollment3DRequestEntity
import com.scb.techx.ekycframework.data.facetec.model.request.Match3D2DIdScanBackRequestEntity
import com.scb.techx.ekycframework.data.facetec.model.request.Match3D2DIdScanFrontRequestEntity
import com.scb.techx.ekycframework.data.facetec.model.response.ConfirmationInfoResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Enrollment3DResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Match3D2DIdScanResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.SessionFaceTecResponseEntity
import com.scb.techx.ekycframework.Constants
import io.reactivex.rxjava3.core.Single
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.HeaderMap
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Url

interface FaceTecAPI {
    @Headers(Constants.ContentType)
    @POST
    fun getConfirmationInfo(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: ConfirmationInfoRequestEntity
    ): Single<ConfirmationInfoResponseEntity>

    @Headers(Constants.ContentType)
    @POST
    fun getEnrollment3D(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: Enrollment3DRequestEntity
    ): Single<Enrollment3DResponseEntity>

    @Headers(Constants.ContentType)
    @POST
    fun getMatch3D2DIdScanFront(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: Match3D2DIdScanFrontRequestEntity
    ): Single<Match3D2DIdScanResponseEntity>

    @Headers(Constants.ContentType)
    @POST
    fun getMatch3D2DIdScanBack(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: Match3D2DIdScanBackRequestEntity
    ): Single<Match3D2DIdScanResponseEntity>

    @Headers(Constants.ContentType)
    @POST
    fun getIdScanOnly(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: Match3D2DIdScanFrontRequestEntity
    ): Single<Match3D2DIdScanResponseEntity>

    @Headers(Constants.ContentType)
    @POST
    fun getIdScanOnly(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders,
        @Body request: Match3D2DIdScanBackRequestEntity
    ): Single<Match3D2DIdScanResponseEntity>

    @GET
    fun getSessionFaceTec(
        @Url dynamicUrl: String,
        @HeaderMap authedHeaders: AuthenticatedHeaders
    ): Single<SessionFaceTecResponseEntity>
}