package com.scb.techx.ekycframework.ui.assets

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity

object EkycDialogAlert {
    fun show(
        message: String,
        activity: Activity,
        isPositiveOnly: <PERSON><PERSON><PERSON>,
        positiveCallback: NdidVerificationActivity.PositiveCallback,
        negativeCallback: NdidVerificationActivity.NegativeCallback?,
        positiveButtonText: String?,
        negativeButtonText: String?
    ) {
        if(!activity.isFinishing) {
            val builder = AlertDialog.Builder(activity)
            if (isPositiveOnly) {
                builder.setMessage(message)
                    .setPositiveButton(
                        positiveButtonText?: activity.resources.getString(R.string.positive_confirm_dialog)
                    ) { dialogInterface, _ ->
                        positiveCallback.onPositive()
                        dialogInterface.dismiss()
                    }.setCancelable(false).show()
            } else {
                builder.setMessage(message)
                    .setPositiveButton(
                        positiveButtonText?: activity.resources.getString(R.string.Ekyc_review_confirm_dialog)
                    ) { dialogInterface, _ ->
                        positiveCallback.onPositive()
                        dialogInterface.dismiss()
                    }
                    .setNegativeButton(
                        negativeButtonText?: activity.resources.getString(R.string.Ekyc_review_cancel_dialog)
                    ) { dialogInterface, _ ->
                        negativeCallback?.onNegative()
                        dialogInterface.dismiss()
                    }.setCancelable(false).show()
            }
        }
    }
}