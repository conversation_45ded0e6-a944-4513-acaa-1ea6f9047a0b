package com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.presenter

class EnableCameraPresenter(
    private val view: EnableCameraContract.View
) : EnableCameraContract.Presenter {

    override fun setActionButton(isNeverAskAgainSelected: Boolean) {
        if (isNeverAskAgainSelected) {
            view.navigateToSettings()
        } else {
            view.requestCameraPermissionDialog()
        }
    }

    override fun setActionPermissionDialog(
        isCameraGranted: Boolean,
        isNeverAskAgainSelected: Boolean
    ) {
        if (isCameraGranted) {
            view.navigateToScanFrontIdCard()
        } else {
            view.setShouldShowStatus()
            if (isNeverAskAgainSelected) {
                view.setEnableButton()
            }
        }
    }

    override fun onResume(isGranted: Boolean) {
        if (isGranted) {
            view.activityFinish()
        }
    }
}