package com.scb.techx.ekycframework.ui.ndidverification.themehelper

import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.graphics.drawable.DrawableContainer
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.scb.techx.ekycframework.ui.theme.NdidTheme

object NdidThemeHelper {
    private fun setFont(view: TextView, isBold: Boolean) {
        NdidTheme.fontName?.let {
            view.typeface = it
            if(isBold) {
                view.setTypeface(view.typeface, Typeface.BOLD)
            }
        }
    }

    private fun setFont(view: Button, isBold: Boolean) {
        NdidTheme.fontName?.let {
            view.typeface = it
            if(isBold) {
                view.setTypeface(view.typeface, Typeface.BOLD)
            }
        }
    }

    fun setPrimaryColorTextTheme(textView: TextView, isBold: Boolean = false) {
        if (!NdidTheme.headerTextColor.isNullOrEmpty()) {
            textView.setTextColor(Color.parseColor(NdidTheme.headerTextColor))
        }
        setFont(textView, isBold)
    }

    fun setSecondaryColorTextTheme(textView: TextView, isBold: Boolean = false) {
        if (!NdidTheme.subHeaderTextColor.isNullOrEmpty()) {
            textView.setTextColor(Color.parseColor(NdidTheme.subHeaderTextColor))
        }
        setFont(textView, isBold)
    }

    fun setBorderTileTheme(layout: RelativeLayout) {
        if (!NdidTheme.unselectedBorderColor.isNullOrEmpty()) {
            val bankTileBackground: GradientDrawable = layout.background as GradientDrawable
            bankTileBackground.setStroke(4, Color.parseColor(NdidTheme.unselectedBorderColor))
        }
    }

    fun setSelectableBorderTileTheme(layout: ConstraintLayout) {
        val cancelBackground: StateListDrawable = layout.background as StateListDrawable
        val drawableContainerState: DrawableContainer.DrawableContainerState =
            cancelBackground.constantState as DrawableContainer.DrawableContainerState
        val children: Array<Drawable> = drawableContainerState.children

        if (!NdidTheme.selectedBorderColor.isNullOrEmpty()) {
            val selectedItem: GradientDrawable = children[0] as GradientDrawable
            selectedItem.setStroke(3, Color.parseColor(NdidTheme.selectedBorderColor))
        }
        if (!NdidTheme.unselectedBorderColor.isNullOrEmpty()) {
            val unselectedItem: GradientDrawable = children[1] as GradientDrawable
            unselectedItem.setStroke(3, Color.parseColor(NdidTheme.unselectedBorderColor))
        }
    }

    fun setLinkTheme(textView: TextView) {
        if (!NdidTheme.linkTextColor.isNullOrEmpty()) {
            textView.setTextColor(Color.parseColor(NdidTheme.linkTextColor))
        }
        setFont(textView, true)
    }

    fun setBorderButtonColor(button: Button) {
        if (!NdidTheme.borderButtonColor.isNullOrEmpty()) {
            val verifyBackground: StateListDrawable =
                button.background as StateListDrawable
            val verifyDrawableContainerState: DrawableContainer.DrawableContainerState =
                verifyBackground.constantState as DrawableContainer.DrawableContainerState
            val verifyChildren: Array<Drawable> = verifyDrawableContainerState.children
            val verifyDrawableActive: GradientDrawable = verifyChildren[0] as GradientDrawable
            verifyDrawableActive.setColor(Color.parseColor(NdidTheme.borderButtonColor))
        }
        if (!NdidTheme.borderButtonBackgroundColor.isNullOrEmpty()) {
            val verifyBackground: StateListDrawable =
                button.background as StateListDrawable
            val verifyDrawableContainerState: DrawableContainer.DrawableContainerState =
                verifyBackground.constantState as DrawableContainer.DrawableContainerState
            val verifyChildren: Array<Drawable> = verifyDrawableContainerState.children
            val verifyDrawable: GradientDrawable = verifyChildren[0] as GradientDrawable
            verifyDrawable.setStroke(3, Color.parseColor(NdidTheme.borderButtonBackgroundColor))
        }
        if (!NdidTheme.borderButtonTextColor.isNullOrEmpty()) {
            button.setTextColor(Color.parseColor(NdidTheme.borderButtonTextColor))
        }
        setFont(button, true)
    }

    fun setButtonColor(button: Button) {
        if (!NdidTheme.buttonTextColor.isNullOrEmpty()) {
            button.setTextColor(Color.parseColor(NdidTheme.buttonTextColor))
        }
        if (!NdidTheme.buttonBackgroundColor.isNullOrEmpty()) {
            val nextBackground: StateListDrawable = button.background as StateListDrawable
            val nextDrawableContainerState: DrawableContainer.DrawableContainerState =
                nextBackground.constantState as DrawableContainer.DrawableContainerState
            val nextChildren: Array<Drawable> = nextDrawableContainerState.children
            val nextDrawableActive: GradientDrawable = nextChildren[0] as GradientDrawable
            nextDrawableActive.setColor(Color.parseColor(NdidTheme.buttonBackgroundColor))
            val nextDrawableInactive: GradientDrawable = nextChildren[1] as GradientDrawable
            nextDrawableInactive.setColor(Color.parseColor(NdidTheme.buttonBackgroundColor))
            nextDrawableInactive.alpha = 128
        }
        setFont(button, true)
    }

    fun setTimerTheme(text: TextView, outerCircle: ImageView, innerCircle: ImageView) {
        if (!NdidTheme.timerTextColor.isNullOrEmpty()) {
            text.setTextColor(Color.parseColor(NdidTheme.timerTextColor))
        }
        if (!NdidTheme.timerBackgroundColor.isNullOrEmpty()) {
            innerCircle.setColorFilter(
                Color.parseColor(NdidTheme.timerBackgroundColor),
                android.graphics.PorterDuff.Mode.MULTIPLY
            )
            outerCircle.setColorFilter(
                Color.parseColor(NdidTheme.timerBackgroundColor),
                android.graphics.PorterDuff.Mode.MULTIPLY
            )
        }
        setFont(text, true)
    }

    fun setSuccessIcon(image: ImageView) {
        NdidTheme.successIcon?.let {
            image.setImageResource(it)
        }
    }
}