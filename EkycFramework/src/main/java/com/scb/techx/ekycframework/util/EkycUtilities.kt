package com.scb.techx.ekycframework.util

import android.content.Context
import com.facetec.sdk.FaceTecSDK
import com.scb.techx.ekycframework.Constants.UserAgentHandling.LOCATION_OF_INSTALLATION_ID
import com.scb.techx.ekycframework.Constants.UserAgentHandling.USER_AGENT_SPLITTER
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.data.getsession.api.GetSessionAPI
import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.ui.processor.Config.Companion.baseUrl
import com.scb.techx.ekycframework.ui.processor.ObfuscatedUrlProvider
import com.scb.techx.ekycframework.data.getsession.datarepository.GetSessionDataRepository
import com.scb.techx.ekycframework.domain.common.usecase.IgnoreOpacityUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.getsession.repository.GetSessionRepository
import com.scb.techx.ekycframework.domain.getsession.usecase.EkycGetSessionUseCase
import com.scb.techx.ekycframework.ui.themehelper.ThemeHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ocridcard.activity.OcrIdCardActivity
import com.scb.techx.ekycframework.ui.model.ocrprefill.OcrPrefill
import com.scb.techx.ekycframework.ui.reviewconfirm.model.PrefillDisplayedToggle
import com.scb.techx.ekycframework.ui.theme.CustomizeTheme
import com.scb.techx.ekycframework.ui.theme.NdidTheme
import com.scb.techx.ekycframework.ui.theme.ReviewTheme
import com.scb.techx.ekycframework.util.facetechelper.helper.FaceTecInitializeHelper
import com.scb.techx.ekycframework.util.facetechelper.model.FaceTecFeatureType
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.Locale


class EkycUtilities {

    private var repository: GetSessionRepository? = null

    private var headersProvider = ApiMainHeadersProvider()
    lateinit var pref: EkycPreferenceUtil

    interface InitCallback {
        fun onSuccess(success: Boolean, description: String, ekycToken: String?)
    }

    interface OCRResultsCallback {
        fun onSuccess(
            success: Boolean,
            description: String,
            userOcrValue: UserConfirmedValue?,
            userConfirmedValue: UserConfirmedValue?,
            dopaResult: DopaResult? = null
        )
    }

    interface LivenessCheckCallback {
        fun onResult(
            success: Boolean,
            description: String
        )
    }

    interface NdidVerificationCallback {
        fun onResult(
            success: Boolean,
            description: String,
            ndidStatus: String?,
            ndidError: NdidError?,
            ndidData: NdidData?
        )
    }

    data class NdidError(
        val code: String?
    )

    data class NdidData(
        val referenceId: String?,
        val requestId: String?
    )

    interface NdidResultsCallback {
        fun onSuccess(
            success: Boolean,
            description: String?,
            status: String?
        )
    }

    private var customizeTheme: CustomizeTheme = CustomizeTheme()

    fun initEkyc(
        applicationContext: Context? = null,
        context: Context,
        sessionId: String,
        token: String,
        environment: String,
        customizeTheme: CustomizeTheme? = null,
        customizeHost: String? = null,
        language: String? = null,
        initCallback: InitCallback
    ) {
        pref = EkycPreferenceUtil(context)
        Config.token = token
        Config.sessionId = sessionId
        baseUrl = if(customizeHost.isNullOrEmpty()) {
            ObfuscatedUrlProvider.getBaseUrl(environment)
        } else{
            customizeHost
        }

        repository = GetSessionDataRepository(GetApiClientUseCase.getApiClient().create(GetSessionAPI::class.java))

        HandleCallback.initCallback = initCallback

        if (applicationContext != null && language != null) {
            setLanguage(applicationContext, context, language)
        }

        EkycGetSessionUseCase.execute(
            context,
            repository,
            pref,
            sessionId,
            Schedulers.io(),
            AndroidSchedulers.mainThread()
        )


        customizeTheme?.let { setTheme(it) } ?: setNonCustomedFaceTecTheme()
    }

    fun getInstallationId(): String? {
        val userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId)
        return try {
            val elements = userAgent.split(USER_AGENT_SPLITTER)
            if (elements.size > LOCATION_OF_INSTALLATION_ID) {
                elements[LOCATION_OF_INSTALLATION_ID]
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    fun ocrIdCard(
        context: Context,
        checkExpiredIdCard: Boolean? = true,
        checkDopa: Boolean? = false,
        enableConfirmInfo: Boolean? = true,
        ocrPrefill: OcrPrefill? = null,
        callback: OCRResultsCallback
    ) {
        Config.checkDopa = checkDopa?: false
        HandleCallback.ocrResultsCallback = callback
        pref.enableConfirmInfo = enableConfirmInfo?: true
        FaceTecInitializeHelper(
            context,
            faceTecFeature = FaceTecFeatureType.OCRONLY,
            isExpiredIdCard = checkExpiredIdCard ?: true,
            pref
        )
        setPrefillValue(ocrPrefill)
    }

    fun ocrIdCardVerifyByFace(
        context: Context,
        checkExpiredIdCard: Boolean? = true,
        checkDopa: Boolean? = false,
        enableConfirmInfo: Boolean? = true,
        ocrPrefill: OcrPrefill? = null,
        ocrResultsCallback: OCRResultsCallback,
    ) {
        Config.checkDopa = checkDopa ?: false
        HandleCallback.ocrResultsCallback = ocrResultsCallback
        pref.enableConfirmInfo = enableConfirmInfo?: true
        FaceTecInitializeHelper(
            context,
            faceTecFeature = FaceTecFeatureType.OCRLIVENESS,
            isExpiredIdCard = checkExpiredIdCard ?: true,
            pref
        )
        setPrefillValue(ocrPrefill)
    }

    fun ndidVerification(
        context: Context,
        identifierType: String,
        identifierValue: String,
        serviceId: String,
        ndidVerificationCallback: NdidVerificationCallback
    ) {
        Config.identifierType = identifierType
        Config.identifierValue = identifierValue
        Config.serviceId = serviceId
        HandleCallback.ndidVerificationCallback = ndidVerificationCallback
        NdidVerificationActivity.startActivity(context)
    }

    fun livenessCheck(
        context: Context,
        livenessCheckCallback: LivenessCheckCallback
    ) {
        HandleCallback.livenessCheckCallback = livenessCheckCallback
        FaceTecInitializeHelper(
            context,
            faceTecFeature = FaceTecFeatureType.LIVENESS,
            isExpiredIdCard = true,
            pref
        )
    }

    private fun setNonCustomedFaceTecTheme() {
        FaceTecSDK.setCustomization(ThemeHelper().getNonCustomizedTheme())
    }

    private fun setTheme(customizeTheme: CustomizeTheme) {
        this.customizeTheme = customizeTheme
        ReviewTheme.borderColor = customizeTheme.border?.borderColor
        ReviewTheme.fieldLabelTextColor = customizeTheme.ocr?.fieldLabelTextColor
        ReviewTheme.mainHeaderTextColor = customizeTheme.ocr?.mainHeaderTextColor
        ReviewTheme.sectionHeaderTextColor = customizeTheme.ocr?.sectionHeaderTextColor
        ReviewTheme.normalButtonTextColor = customizeTheme.button?.normalTextColor
        ReviewTheme.normalButtonBackgroundColor = customizeTheme.button?.normalBackgroundColor
        ReviewTheme.disableButtonTextColor = customizeTheme.button?.disabledTextColor
        ReviewTheme.disableButtonBackgroundColor = customizeTheme.button?.disabledBackgroundColor
        ReviewTheme.highlightButtonTextColor = customizeTheme.button?.highlightTextColor
        ReviewTheme.highlightButtonBackgroundColor = customizeTheme.button?.highlightBackgroundColor
        ReviewTheme.logo = customizeTheme.image?.logo
        ReviewTheme.cancelLogo = customizeTheme.image?.closeImage
        ReviewTheme.fontName = customizeTheme.text?.fontName
        NdidTheme.buttonBackgroundColor = IgnoreOpacityUseCase.execute(
            customizeTheme.button?.normalBackgroundColor
        )
        NdidTheme.buttonTextColor = IgnoreOpacityUseCase.execute(customizeTheme.button?.normalTextColor)
        NdidTheme.fontName = customizeTheme.text?.fontName
        NdidTheme.errorIcon = customizeTheme.ndid?.errorIcon
        NdidTheme.linkTextColor = IgnoreOpacityUseCase.execute(customizeTheme.text?.linkColor)
        NdidTheme.successIcon = customizeTheme.ndid?.successIcon
        NdidTheme.timerBackgroundColor = IgnoreOpacityUseCase.execute(
            customizeTheme.ndid?.timerBackgroundColor
        )
        NdidTheme.timerTextColor = IgnoreOpacityUseCase.execute(customizeTheme.ndid?.timerColor)
        NdidTheme.headerTextColor = IgnoreOpacityUseCase.execute(
            customizeTheme.text?.primaryTextColor
        )
        NdidTheme.subHeaderTextColor = IgnoreOpacityUseCase.execute(
            customizeTheme.text?.secondaryTextColor
        )
        NdidTheme.unselectedBorderColor = IgnoreOpacityUseCase.execute(
            customizeTheme.border?.borderColor
        )
        NdidTheme.selectedBorderColor = IgnoreOpacityUseCase.execute(
            customizeTheme.border?.selectedBorderColor
        )
        NdidTheme.borderButtonColor = IgnoreOpacityUseCase.execute(
            customizeTheme.button?.outlineBackgroundColor
        )
        NdidTheme.borderButtonTextColor = IgnoreOpacityUseCase.execute(
            customizeTheme.button?.outlineTextColor
        )
        NdidTheme.borderButtonBackgroundColor = IgnoreOpacityUseCase.execute(
            customizeTheme.button?.outlineBorderColor
        )
        FaceTecSDK.setCustomization(ThemeHelper().getCustomizationTheme(this.customizeTheme))
    }

    private fun setPrefillValue(ocrPrefill: OcrPrefill?) {
        val prefillValue = ocrPrefill?: OcrPrefill()
        PrefillDisplayedToggle.laserIdFlag = prefillValue.laserIdFlag?: true
        PrefillDisplayedToggle.dateOfBirthFlag = prefillValue.dateOfBirthFlag?: true
        PrefillDisplayedToggle.dateOfExpiryFlag = prefillValue.dateOfExpiryFlag?: true
        PrefillDisplayedToggle.dateOfIssueFlag = prefillValue.dateOfIssueFlag?: true
        PrefillDisplayedToggle.firstNameEnFlag = prefillValue.firstNameEnFlag?: true
        PrefillDisplayedToggle.firstNameThFlag = prefillValue.firstNameThFlag?: true
        PrefillDisplayedToggle.lastNameEnFlag = prefillValue.lastNameEnFlag?: true
        PrefillDisplayedToggle.lastNameThFlag = prefillValue.lastNameThFlag?: true
        PrefillDisplayedToggle.titleEnFlag = prefillValue.titleEnFlag?: true
        PrefillDisplayedToggle.titleThFlag = prefillValue.titleThFlag?: true
    }

    private fun setLanguage(applicationContext: Context, context: Context, language: String) {
        HandleCallback.language = language.lowercase()
        val locale = Locale(language)
        val config = context.resources.configuration
        config.setLocale(locale)
        context.resources.updateConfiguration(config, context.resources.displayMetrics)
        applicationContext.resources.updateConfiguration(
            config,
            context.resources.displayMetrics
        )
    }

}