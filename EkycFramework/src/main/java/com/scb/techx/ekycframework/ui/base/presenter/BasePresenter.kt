package com.scb.techx.ekycframework.ui.base.presenter

import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_1001
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_1002
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_1003
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_1004
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_1899
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_1999
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_2001
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_2002
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_2003
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_2004
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_3001
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_3003
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_4001
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_7101
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_7102
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_7103
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_7201
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_7301
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_9001
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.DES_CUS_EKYC_9002
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1001
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1002
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1003
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1004
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1899
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1999
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_2001
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_2002
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_2003
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_2004
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_3001
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_3003
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_4001
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7101
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7102
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7103
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7201
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7202
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7301
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_9001
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_9002
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.ui.ndidverification.presenter.NdidVerificationContract
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.common.usecase.GetErrorMessageFromExceptionUseCase

open class BasePresenter(private val pref: EkycPreferenceUtil) {

    fun handleHttpException(throwable: Throwable, view: NdidVerificationContract.View) {
        ClearTokenUseCase.execute(pref)
        view.showDialog(
            GetErrorMessageFromExceptionUseCase.execute(throwable),
            true,
            object : NdidVerificationActivity.PositiveCallback {
                override fun onPositive() {
                    view.handleCallback(
                        success = false,
                        description = GetErrorMessageFromExceptionUseCase.execute(throwable),
                        ndidStatus = null,
                        ndidError = null,
                        ndidData = null
                    )
                }
            },
            null
        )
    }

    fun handleErrorEkyc(code: String, view: NdidVerificationContract.View) {
        val message = when (code) {
            CUS_EKYC_1001 -> {
                DES_CUS_EKYC_1001
            }
            CUS_EKYC_1002 -> {
                DES_CUS_EKYC_1002
            }
            CUS_EKYC_1003 -> {
                DES_CUS_EKYC_1003
            }
            CUS_EKYC_1004 -> {
                DES_CUS_EKYC_1004
            }
            CUS_EKYC_1899 -> {
                DES_CUS_EKYC_1899
            }
            CUS_EKYC_1999 -> {
                DES_CUS_EKYC_1999
            }
            CUS_EKYC_2001 -> {
                DES_CUS_EKYC_2001
            }
            CUS_EKYC_2002 -> {
                DES_CUS_EKYC_2002
            }
            CUS_EKYC_2003 -> {
                DES_CUS_EKYC_2003
            }
            CUS_EKYC_2004 -> {
                DES_CUS_EKYC_2004
            }
            CUS_EKYC_3001 -> {
                DES_CUS_EKYC_3001
            }
            CUS_EKYC_3003 -> {
                DES_CUS_EKYC_3003
            }
            CUS_EKYC_4001 -> {
                DES_CUS_EKYC_4001
            }
            CUS_EKYC_7101 -> {
                DES_CUS_EKYC_7101
            }
            CUS_EKYC_7102 -> {
                DES_CUS_EKYC_7102
            }
            CUS_EKYC_7103 -> {
                DES_CUS_EKYC_7103
            }
            CUS_EKYC_7202,
            CUS_EKYC_7201 -> {
                DES_CUS_EKYC_7201
            }
            CUS_EKYC_7301 -> {
                DES_CUS_EKYC_7301
            }
            CUS_EKYC_9001 -> {
                DES_CUS_EKYC_9001
            }
            CUS_EKYC_9002 -> {
                DES_CUS_EKYC_9002
            }
            else -> COMMON_ERROR_MESSAGE
        }
        view.handleCallback(
            success = false,
            description = message,
            ndidStatus = null,
            ndidError = null,
            ndidData = null
        )

        ClearTokenUseCase.execute(pref)
    }
}