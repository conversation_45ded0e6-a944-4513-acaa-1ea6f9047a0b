package com.scb.techx.ekycframework.ui.themehelper

import android.content.res.ColorStateList
import android.graphics.Color
import com.facetec.sdk.FaceTecCancelButtonCustomization
import com.facetec.sdk.FaceTecCustomization
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.ui.theme.CustomizeTheme

class ThemeHelper {
    private fun textCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        if (!customizeTheme.text?.primaryTextColor.isNullOrEmpty()) {
            val primaryTextColor = Color.parseColor(customizeTheme.text?.primaryTextColor)
            customization.guidanceCustomization.foregroundColor =
                primaryTextColor //Header and description Text Enable Camera
            customization.guidanceCustomization.readyScreenHeaderTextColor = primaryTextColor
            customization.guidanceCustomization.readyScreenSubtextTextColor = primaryTextColor
            customization.guidanceCustomization.retryScreenHeaderTextColor = primaryTextColor
            customization.guidanceCustomization.retryScreenSubtextTextColor = primaryTextColor
            customization.resultScreenCustomization.foregroundColor =
                primaryTextColor // Text load result
            customization.idScanCustomization.selectionScreenForegroundColor = primaryTextColor
            customization.idScanCustomization.captureScreenFocusMessageTextColor =
                primaryTextColor //Tap Screen To Focus
        }

        if (!customizeTheme.text?.secondaryTextColor.isNullOrEmpty()) {
            val secondaryTextColor = Color.parseColor(customizeTheme.text?.secondaryTextColor)
            customization.feedbackCustomization.textColor = secondaryTextColor
            customization.idScanCustomization.captureScreenForegroundColor =
                secondaryTextColor //Show Front Id
            customization.idScanCustomization.reviewScreenForegroundColor =
                secondaryTextColor
        }

        if (!customizeTheme.text?.secondaryTextBackgroundColor.isNullOrEmpty()) {
            val secondaryBackgroundColor =
                Color.parseColor(customizeTheme.text?.secondaryTextBackgroundColor)
            customization.feedbackCustomization.backgroundColors = secondaryBackgroundColor
            customization.idScanCustomization.captureScreenTextBackgroundColor =
                secondaryBackgroundColor
            customization.idScanCustomization.reviewScreenTextBackgroundColor =
                secondaryBackgroundColor
        }

        return customization
    }

    fun buttonCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        if (!customizeTheme.button?.normalTextColor.isNullOrEmpty()) {
            val normalButtonTextColor = Color.parseColor(customizeTheme.button?.normalTextColor)
            customization.guidanceCustomization.buttonTextNormalColor = normalButtonTextColor
            customization.idScanCustomization.buttonTextNormalColor = normalButtonTextColor
        }

        if (!customizeTheme.button?.normalBackgroundColor.isNullOrEmpty()) {
            val normalButtonBackgroundColor =
                Color.parseColor(customizeTheme.button?.normalBackgroundColor)
            customization.guidanceCustomization.buttonBackgroundNormalColor =
                normalButtonBackgroundColor
            customization.idScanCustomization.buttonBackgroundNormalColor =
                normalButtonBackgroundColor
        }

        if (!customizeTheme.button?.disabledTextColor.isNullOrEmpty()) {
            val disabledButtonTextColor =
                Color.parseColor(customizeTheme.button?.disabledTextColor)
            customization.guidanceCustomization.buttonTextDisabledColor =
                disabledButtonTextColor
            customization.idScanCustomization.buttonTextDisabledColor = disabledButtonTextColor
        }

        if (!customizeTheme.button?.disabledBackgroundColor.isNullOrEmpty()) {
            val disabledButtonBackgroundColor =
                Color.parseColor(customizeTheme.button?.disabledBackgroundColor)
            customization.guidanceCustomization.buttonBackgroundDisabledColor =
                disabledButtonBackgroundColor
            customization.idScanCustomization.buttonBackgroundDisabledColor =
                disabledButtonBackgroundColor
        }

        if (!customizeTheme.button?.highlightTextColor.isNullOrEmpty()) {
            val highlightButtonTextColor =
                Color.parseColor(customizeTheme.button?.highlightTextColor)
            customization.guidanceCustomization.buttonTextHighlightColor =
                highlightButtonTextColor
            customization.idScanCustomization.buttonTextHighlightColor =
                highlightButtonTextColor
        }

        if (!customizeTheme.button?.highlightBackgroundColor.isNullOrEmpty()) {
            val highlightButtonBackgroundColor =
                Color.parseColor(customizeTheme.button?.highlightBackgroundColor)
            customization.guidanceCustomization.buttonBackgroundHighlightColor =
                highlightButtonBackgroundColor
            customization.idScanCustomization.buttonBackgroundHighlightColor =
                highlightButtonBackgroundColor
        }

        return customization
    }

    private fun ovalCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        if (!customizeTheme.oval?.strokeColor.isNullOrEmpty()) {
            val strokeColor = Color.parseColor(customizeTheme.oval?.strokeColor)
            customization.ovalCustomization.progressColor1 = strokeColor //Progress1 in Stroke
            customization.ovalCustomization.progressColor2 = strokeColor //Progress2 in Stroke
            customization.ovalCustomization.strokeColor = strokeColor //Outside line oval
            customization.guidanceCustomization.retryScreenOvalStrokeColor =
                strokeColor //Oval retry screen
        }

        return customization
    }

    private fun borderCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        if (!customizeTheme.border?.borderColor.isNullOrEmpty()) {
            val borderColor = Color.parseColor(customizeTheme.border?.borderColor)
            customization.frameCustomization.borderColor = borderColor
            customization.idScanCustomization.captureFrameStrokeColor = borderColor
            customization.guidanceCustomization.retryScreenImageBorderColor = borderColor
        }

        return customization
    }

    private fun progressColorCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        if (!customizeTheme.other?.progressColor.isNullOrEmpty()) {
            val progressColor = Color.parseColor(customizeTheme.other?.progressColor)
            customization.resultScreenCustomization.activityIndicatorColor = progressColor
            customization.resultScreenCustomization.resultAnimationBackgroundColor =
                progressColor
            customization.resultScreenCustomization.uploadProgressFillColor = progressColor
        }

        return customization
    }

    private fun imageCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        customizeTheme.image?.idCardImage?.let {
            customization.idScanCustomization.selectionScreenDocumentImage = it
        }

        customizeTheme.image?.permissionCameraImage?.let {
            customization.guidanceCustomization.cameraPermissionsScreenImage = it
        }

        customizeTheme.image?.activeFlash?.let {
            customization.idScanCustomization.activeTorchButtonImage = it
        }

        customizeTheme.image?.inActiveFlash?.let {
            customization.idScanCustomization.inactiveTorchButtonImage = it
        }

        customizeTheme.image?.closeImage?.let {
            customization.cancelButtonCustomization.customImage = it
        }

        if (customizeTheme.image?.logo != null) {
            customization.overlayCustomization.brandingImage = customizeTheme.image.logo
        } else {
            customization.overlayCustomization.showBrandingImage = false
        }

        return customization
    }

    private fun backgroundColorCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        if (!customizeTheme.other?.backgroundColor.isNullOrEmpty()) {
            customization.overlayCustomization.backgroundColor =
                Color.parseColor(customizeTheme.other?.backgroundColor)
        }

        return customization
    }

    private fun fontNameCustomizeThemeUpdate(
        customization: FaceTecCustomization,
        customizeTheme: CustomizeTheme
    ): FaceTecCustomization {
        customizeTheme.text?.fontName?.let {
            customization.guidanceCustomization.headerFont = it
            customization.guidanceCustomization.subtextFont = it
            customization.guidanceCustomization.buttonFont = it
            customization.guidanceCustomization.readyScreenHeaderFont = it
            customization.guidanceCustomization.readyScreenSubtextFont = it
            customization.guidanceCustomization.retryScreenHeaderFont = it
            customization.guidanceCustomization.retryScreenSubtextFont = it

            customization.feedbackCustomization.textFont = it

            customization.resultScreenCustomization.messageFont = it

            customization.idScanCustomization.headerFont = it
            customization.idScanCustomization.subtextFont = it
            customization.idScanCustomization.buttonFont = it
            customization.idScanCustomization.captureScreenFocusMessageFont = it
        }

        return customization
    }

    private fun removeCancelButtonInPermissionPage(customization: FaceTecCustomization): FaceTecCustomization {
        customization.cancelButtonCustomization.hideForCameraPermissions = true

        return customization
    }

    fun getNonCustomizedTheme(): FaceTecCustomization {
        val customization = FaceTecCustomization()

        return removeCancelButtonInPermissionPage(customization)
    }

    fun getCustomizationTheme(customizeTheme: CustomizeTheme): FaceTecCustomization {

        var customization = FaceTecCustomization()

        try {
            customization = removeCancelButtonInPermissionPage(customization)
            customization = textCustomizeThemeUpdate(customization, customizeTheme)
            customization = buttonCustomizeThemeUpdate(customization, customizeTheme)
            customization = ovalCustomizeThemeUpdate(customization, customizeTheme)
            customization = borderCustomizeThemeUpdate(customization, customizeTheme)
            customization = progressColorCustomizeThemeUpdate(customization, customizeTheme)
            customization = imageCustomizeThemeUpdate(customization, customizeTheme)
            customization = backgroundColorCustomizeThemeUpdate(customization, customizeTheme)
            customization = fontNameCustomizeThemeUpdate(customization, customizeTheme)
        } catch (e: IllegalArgumentException) {
            HandleCallback.initCallback?.onSuccess(
                false,
                "Please put color as #______",
                null
            )
        }
        return customization
    }

    fun buttonColorStateListMaker(list: IntArray, stateList: BooleanArray): ColorStateList {
        val defaultColorStates = arrayOf(
            intArrayOf(android.R.attr.state_pressed),
            intArrayOf(android.R.attr.state_enabled),
            intArrayOf(-android.R.attr.state_enabled)
        )

        var actualColorState: Array<IntArray> = arrayOf()

        for ((index, isStateSet) in stateList.withIndex()) {
            if(isStateSet) {
                actualColorState += defaultColorStates[index]
            }
        }

        return ColorStateList(actualColorState, list)
    }

    fun textColorStateListMaker(list: IntArray, stateList: BooleanArray): ColorStateList {
        var index = 0;
        val defaultColorStates = arrayOf(
            intArrayOf(android.R.attr.state_pressed),
            intArrayOf(android.R.attr.state_enabled),
            intArrayOf(-android.R.attr.state_enabled)
        )

        var actualColorList = intArrayOf()

        for (isStateSet in stateList) {
            if(isStateSet) {
                actualColorList += list[index]
                index++
            }
            else {
                actualColorList += Color.parseColor("#FFFFFF")
            }
        }

        return ColorStateList(defaultColorStates, actualColorList)
    }

    fun makeColorArray(
        normalColor: String?,
        disableColor: String?,
        highlightColor: String?
    ): IntArray {
        var colors = intArrayOf()

        if (!highlightColor.isNullOrEmpty()) {
            colors += Color.parseColor(highlightColor)
        }

        if (!normalColor.isNullOrEmpty()) {
            colors += Color.parseColor(normalColor)
        }

        if (!disableColor.isNullOrEmpty()) {
            colors += Color.parseColor(disableColor)
        }

        return colors
    }

    fun makeColorStateArray(
        normalColor: String?,
        disableColor: String?,
        highlightColor: String?
    ): BooleanArray{
        var colorState = booleanArrayOf()

        colorState += !highlightColor.isNullOrEmpty()
        colorState += !normalColor.isNullOrEmpty()
        colorState += !disableColor.isNullOrEmpty()

        return colorState
    }
}