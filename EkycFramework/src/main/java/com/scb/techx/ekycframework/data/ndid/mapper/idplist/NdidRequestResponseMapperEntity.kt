package com.scb.techx.ekycframework.data.ndid.mapper.idplist

import com.scb.techx.ekycframework.data.ndid.model.idplist.*
import com.scb.techx.ekycframework.domain.ndid.model.response.Idp
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidData
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestData
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestResponse

class NdidRequestResponseMapperEntity {
    fun mapFromEntity(entity: NdidRequestResponseEntity): NdidRequestResponse {
        return NdidRequestResponse(
            code = entity.code,
            description = entity.description,
            data = transformIdpData(entity.data)
        )
    }

    private fun transformIdpData(entity: NdidRequestDataEntity?): NdidRequestData? {
        return if (entity == null) {
            null
        } else {
            NdidRequestData(
                sessionId = entity.sessionId,
                referenceId = entity.referenceId,
                status = entity.status,
                expireTime = entity.expireTime,
                idp = transformIdpDataValue(entity.idp),
                ndidData = transformIdpNdidData(entity.ndidData)
            )
        }
    }

    private fun transformIdpDataValue(entity: IdpEntity): Idp {
        return Idp(
                nodeId = entity.nodeId,
                industryCode = entity.industryCode,
                companyCode = entity.companyCode,
                shortName = entity.shortName,
                marketingNameTh = entity.marketingNameTh,
                marketingNameEn = entity.marketingNameEn,
                smallIconPath = entity.smallIconPath,
                mediumIconPath = entity.mediumIconPath,
                largeIconPath = entity.largeIconPath,
                deepLinkIos = entity.deepLinkIos,
                deepLinkAndroid = entity.deepLinkAndroid,
                deepLinkHuawei = entity.deepLinkHuawei
            )

    }

    private fun transformIdpNdidData(entity: NdidDataEntity): NdidData {
        return NdidData(
            requestId = entity.requestId
        )
    }
}