package com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.presenter

import android.content.Context
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_1000
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_3001
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_3002
import com.scb.techx.ekycframework.Constants.EkycStatusCode.CUS_EKYC_7301
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_CONFIRMED_ACCEPT
import com.scb.techx.ekycframework.Constants.NdidStatus.IDP_PENDING
import com.scb.techx.ekycframework.Constants.NdidStatus.USER_SELECT_IDP
import com.scb.techx.ekycframework.Constants.PACKAGE_NAME_GOOGLE_PLAY
import com.scb.techx.ekycframework.ui.base.presenter.BasePresenter
import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.domain.ndid.usecase.GetNdidStatusUseCase
import com.scb.techx.ekycframework.domain.ndid.usecase.PostRequestCancelUseCase
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Scheduler
import io.reactivex.rxjava3.schedulers.Schedulers

class NdidVerificationEnrollmentPresenter(
    private val view: NdidVerificationEnrollmentContract.View,
    private val pref: EkycPreferenceUtil,
    private val context: Context,
    private val processScheduler: Scheduler,
    private val androidScheduler: Scheduler,
    private val repository: NdidRepository

) : BasePresenter(pref), NdidVerificationEnrollmentContract.Presenter {

    override fun getNdidStatus(isVerifyClick: Boolean) {
        GetNdidStatusUseCase.execute(pref, repository)
            .subscribeOn(processScheduler)
            .observeOn(androidScheduler)
            .doOnSubscribe { view.showLoadingDialog() }
            .doOnSuccess { view.hideLoadingDialog() }
            .doOnError { view.hideLoadingDialog() }
            .subscribe({ response ->
                view.stopTimer()
                if (CUS_EKYC_1000 == response.code
                    || CUS_EKYC_7301 == response.code
                    || CUS_EKYC_3001 == response.code
                ) {
                    dealWithResponseStatus(response, isVerifyClick)
                } else {
                    view.handleErrorEkyc(response.code)
                }
            }, { throwable ->
                view.handleHttpException(throwable)
            })
    }

    private fun dealWithResponseStatus(response: NdidStatus, isVerifyClick: Boolean) {
        when (response.data.status) {
            USER_SELECT_IDP,
            IDP_PENDING,
            IDP_CONFIRMED_ACCEPT -> {
                if (isVerifyClick) {
                    view.showDialog(
                        context.resources.getString(R.string.Ekyc_ndid_holding_identification_error),
                        true,
                        object : NdidVerificationActivity.PositiveCallback {
                            override fun onPositive() {
                                //Just dismiss()
                            }
                        },
                        null
                    )
                }
                view.setTimer(timerCalculated(response.data.expireTime))
                view.startTimer()
            }
            else -> {
                view.handleNotPendingNdidStatus(response)
            }
        }
    }

    override fun start() {
        view.startTimer()
    }

    override fun stop() {
        view.stopTimer()
    }

    override fun onClickBankTile(deeplinkAndroid: String?, deeplinkHuawei: String?) {
        if (deeplinkAndroid.isNullOrEmpty()) {
            view.showDialog(
                context.resources.getString(R.string.Ekyc_ndid_holding_deeplink_alert),
                true,
                object : NdidVerificationActivity.PositiveCallback {
                    override fun onPositive() {
                        //dismiss()
                    }
                },
                null
            )
        } else {
            if (view.isPackageInstalled(PACKAGE_NAME_GOOGLE_PLAY)) {
                view.navigateDeeplink(deeplinkAndroid)
            } else {
                view.navigateDeeplink(deeplinkHuawei)
            }
        }
    }

    override fun onClickCancel() {
        view.showDialog(
            context.resources.getString(R.string.Ekyc_ndid_holding_confirm_cancel),
            false,
            object : NdidVerificationActivity.PositiveCallback {
                override fun onPositive() {
                    view.postRequestCancel()
                }
            },
            object : NdidVerificationActivity.NegativeCallback {
                override fun onNegative() {
                    //Do nothing
                }
            }
        )
    }

    override fun timerCalculated(timer: Int): Long {
        return (timer * 1000).toLong()
    }

    override fun postRequestCancel() {
        PostRequestCancelUseCase.execute(pref, repository)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnSubscribe { view.showLoadingDialog() }
            .doOnSuccess { view.hideLoadingDialog() }
            .doOnError { view.hideLoadingDialog() }
            .subscribe({ response ->
                when (response.code) {
                    CUS_EKYC_1000 -> {
                        view.showDialog(
                            context.resources.getString(R.string.Ekyc_ndid_holding_canceled_alert),
                            true,
                            object : NdidVerificationActivity.PositiveCallback {
                                override fun onPositive() {
                                    view.navigateToIdpList()
                                }
                            },
                            null
                        )
                    }
                    CUS_EKYC_7301 -> {
                        view.showDialog(
                            context.resources.getString(R.string.Ekyc_ndid_holding_proceed_error),
                            true,
                            object : NdidVerificationActivity.PositiveCallback {
                                override fun onPositive() {
                                    //Do nothing just dismiss()
                                }
                            },
                            null
                        )
                    }
                    CUS_EKYC_3002 -> {
                        getNdidStatus(false)
                    }
                    else -> {
                        view.handleErrorEkyc(response.code)
                    }
                }
            }, { throwable ->
                view.handleHttpException(throwable)
            })
    }


}