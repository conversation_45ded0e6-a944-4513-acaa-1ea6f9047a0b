package com.scb.techx.ekycframework.domain.apihelper.usecase

import androidx.annotation.Keep

@Keep
class ApiMainHeadersProvider {

    fun getAuthenticatedHeaders(
        authorization: String,
        ekycToken: String,
        userAgent: String,
        sdkVersion: String,
        sessionId: String,
        tid: String,
        correlationId: String
    ): AuthenticatedHeaders =
        AuthenticatedHeaders().apply {
            put(AUTHORIZATION, getBearer(authorization))
            put(EKYC_TOKEN, ekycToken)
            put(USER_AGENT, userAgent)
            put(EKYC_VERSION, sdkVersion)
            put(SESSION_ID, sessionId)
            put(TID, tid)
            put(CORRELATION_ID, correlationId)
        }

    fun getAuthenticatedHeaders(
        authorization: String,
        deviceKey: String,
        userAgent: String,
        sdkVersion: String,
        sessionId: String,
        tid: String,
        correlationId: String,
        ekycToken: String
    ): AuthenticatedHeaders =
        AuthenticatedHeaders().apply {
            put(AUTHORIZATION, getBearer(authorization))
            put(DEVICE_KEY, deviceKey)
            put(USER_AGENT, userAgent)
            put(EKYC_VERSION, sdkVersion)
            put(SESSION_ID, sessionId)
            put(TID, tid)
            put(CORRELATION_ID, correlationId)
            put(EKYC_TOKEN, ekycToken)
        }

    fun getAuthenticatedHeaders(
        authorization: String,
        sessionId: String,
        tid: String,
        ekycToken: String,
        correlationId: String,
    ): AuthenticatedHeaders =
        AuthenticatedHeaders().apply {
            put(AUTHORIZATION, getBearer(authorization))
            put(SESSION_ID, sessionId)
            put(TID, tid)
            put(EKYC_TOKEN, ekycToken)
            put(CORRELATION_ID, correlationId)
        }

    fun getAuthenticatedHeaders(
        authorization: String,
        sdkVersion: String,
        deviceInfo: String?,
        sessionId: String,
        tid: String,
        correlationId: String
    ): AuthenticatedHeaders =
        AuthenticatedHeaders().apply {
            put(AUTHORIZATION, getBearer(authorization))
            put(SESSION_ID, sessionId)
            put(TID, tid)
            if (deviceInfo != null) {
                put(DEVICE_INFO, deviceInfo)
            }
            put(CORRELATION_ID, correlationId)
            put(EKYC_VERSION, sdkVersion)
        }

    companion object {
        private const val AUTHORIZATION = "Authorization"
        private const val DEVICE_KEY = "X-Device-Key"
        private const val USER_AGENT = "X-User-Agent"
        private const val EKYC_VERSION = "X-Ekyc-Sdk-Version"
        private const val SESSION_ID = "X-Session-Id"
        private const val TID = "X-Tid"
        private const val DEVICE_INFO = "X-Ekyc-Device-Info"
        private const val CORRELATION_ID = "correlationid"
        private const val EKYC_TOKEN = "X-Ekyc-Token"

        private fun getBearer(authorization: String) = "Bearer $authorization"
    }
}

@Keep
open class ApiMainHeaders : HashMap<String, String>()
@Keep
class AuthenticatedHeaders : ApiMainHeaders()