package com.scb.techx.ekycframework.ui.processor.enrollment.presenter

import com.facetec.sdk.FaceTecFaceScanResultCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest

interface EnrollmentProcessorContract {
    interface Processor {
        fun proceedToNextStepFaceScan(
            faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
            scanResultBlob: String?
        )
        fun faceTecCancelFaceScan(
            faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
            description: String
        )
    }

    interface Presenter {
        fun sendApiForEnrollment3D(
            faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
            authenticatedHeaders: AuthenticatedHeaders,
            request: Enrollment3DRequest
        )
    }
}