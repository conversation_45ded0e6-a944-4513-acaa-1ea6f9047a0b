package com.scb.techx.ekycframework.data.getsession.model.response

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class SessionTokenResponseEntity(
    @SerializedName("code")
    val code: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("data")
    val data: SessionTokenDataEntity?
)

@Keep
data class SessionTokenDataEntity(
    @SerializedName("ekycToken")
    val ekycToken: String?,
)