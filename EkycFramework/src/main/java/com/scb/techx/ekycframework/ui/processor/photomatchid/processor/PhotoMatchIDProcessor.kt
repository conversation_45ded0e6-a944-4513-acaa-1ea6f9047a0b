package com.scb.techx.ekycframework.ui.processor.photomatchid.processor

import android.content.Context
import com.facetec.sdk.FaceTecFaceScanProcessor
import com.facetec.sdk.FaceTecFaceScanResultCallback
import com.facetec.sdk.FaceTecIDScanProcessor
import com.facetec.sdk.FaceTecIDScanResult
import com.facetec.sdk.FaceTecIDScanResultCallback
import com.facetec.sdk.FaceTecIDScanStatus
import com.facetec.sdk.FaceTecSDK
import com.facetec.sdk.FaceTecSessionActivity
import com.facetec.sdk.FaceTecSessionResult
import com.facetec.sdk.FaceTecSessionStatus
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.domain.apihelper.usecase.GetApiClientUseCase
import com.scb.techx.ekycframework.domain.apihelper.usecase.ApiMainHeadersProvider
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.ui.processor.photomatchid.presenter.PhotoMatchIDProcessorContract
import com.scb.techx.ekycframework.ui.processor.photomatchid.presenter.PhotoMatchIDProcessorPresenter
import com.scb.techx.ekycframework.data.facetec.datarepository.FaceTecDataRepository
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.data.facetec.api.FaceTecAPI
import com.scb.techx.ekycframework.domain.common.usecase.ClearTokenUseCase
import com.scb.techx.ekycframework.Constants.EkycCallbackMessage.USER_CANCELLED
import com.scb.techx.ekycframework.Constants.OCR
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

class PhotoMatchIDProcessor(
    private val context: Context,
    private val checkExpiredIdCard: Boolean = true,
) : FaceTecFaceScanProcessor,
    FaceTecIDScanProcessor,
    PhotoMatchIDProcessorContract.Processor
{
    var success: Boolean = false
    var faceScanWasSuccessful = false
    private val repository: FaceTecRepository by lazy {
        FaceTecDataRepository(GetApiClientUseCase.getApiClient().create(FaceTecAPI::class.java))
    }
    private var headersProvider = ApiMainHeadersProvider()
    private var pref = EkycPreferenceUtil(context)
    val presenter: PhotoMatchIDProcessorContract.Presenter

    init {
        FaceTecSessionActivity.createAndLaunchSession(context, this, this, pref.sessionFaceTec)
        presenter = PhotoMatchIDProcessorPresenter(
            this,
            pref,
            Schedulers.io(),
            AndroidSchedulers.mainThread(),
            repository
        )
    }

    override fun faceTecCancelFaceScan(faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?) {
        faceTecFaceScanResultCallback?.cancel()
    }

    override fun faceTecCancelIDScan(faceTecIDScanResultCallback: FaceTecIDScanResultCallback?) {
        faceTecIDScanResultCallback?.cancel()
    }

    override fun failCallbackWithMessage(description: String) {
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = false,
            description = description,
            userOcrValue = null,
            userConfirmedValue = null
        )
    }

    override fun successCallbackWithMessage(
        description: String,
        userOcrValue: com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
    ) {
        HandleCallback.ocrResultsCallback?.onSuccess(
            success = true,
            description = description,
            userOcrValue = userOcrValue,
            userConfirmedValue = null
        )
    }

    override fun proceedToNextStepIDScan(
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback?,
        scanResultBlob: String?
    ) {
        success =
            faceTecIDScanResultCallback?.proceedToNextStep(
                scanResultBlob ?: ""
            )?: false
    }

    override fun proceedToNextStepFaceScan(
        faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback?,
        scanResultBlob: String?
    ) {
        faceScanWasSuccessful =
            faceTecFaceScanResultCallback?.proceedToNextStep(
                scanResultBlob ?: ""
            )?: false
    }

    override fun processSessionWhileFaceTecSDKWaits(
        faceTecSessionResult: FaceTecSessionResult,
        faceTecFaceScanResultCallback: FaceTecFaceScanResultCallback
    ) {
        if (faceTecSessionResult.status != FaceTecSessionStatus.SESSION_COMPLETED_SUCCESSFULLY) {
            ClearTokenUseCase.execute(pref)
            failCallbackWithMessage(USER_CANCELLED)
            faceTecFaceScanResultCallback.cancel()
            return
        }

        val request = Enrollment3DRequest(
            faceScan = faceTecSessionResult.faceScanBase64,
            auditTrailImage = faceTecSessionResult.auditTrailCompressedBase64[0],
            lowQualityAuditTrailImage = faceTecSessionResult
                .lowQualityAuditTrailCompressedBase64[0],
            function = OCR
        )

        val authedHeaders = headersProvider.getAuthenticatedHeaders(
            authorization = Config.token,
            deviceKey = pref.deviceKey,
            userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId),
            sessionId = Config.x_session_id,
            tid = GetDeviceSettingUseCase.getUUID(),
            ekycToken = pref.ekycToken,
            correlationId = GetDeviceSettingUseCase.getUUID(),
            sdkVersion = GetDeviceSettingUseCase.getVersionName()
        )

        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback,
            authedHeaders,
            request
        )
    }

    private fun scanFrontImage(
        faceTecIDScanResult: FaceTecIDScanResult,
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback
    ) {
        val frontImagesCompressedBase64 =
            if (faceTecIDScanResult.frontImagesCompressedBase64.size > 0) faceTecIDScanResult.frontImagesCompressedBase64[0] else ""

        val request = Match3D2DIdScanFrontRequest(
            idScan = faceTecIDScanResult.idScanBase64,
            idScanFrontImage = frontImagesCompressedBase64,
            enableConfirmInfo = pref.enableConfirmInfo
        )

        val authedHeaders = headersProvider.getAuthenticatedHeaders(
            authorization = Config.token,
            deviceKey = pref.deviceKey,
            userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId),
            sessionId = Config.x_session_id,
            tid = GetDeviceSettingUseCase.getUUID(),
            ekycToken = pref.ekycToken,
            correlationId = GetDeviceSettingUseCase.getUUID(),
            sdkVersion = GetDeviceSettingUseCase.getVersionName(),
        )

        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback,
            authedHeaders,
            request
        )
    }

    private fun scanBackImage(
        faceTecIDScanResult: FaceTecIDScanResult,
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback
    ) {
        val backImagesCompressedBase64 =
            if (faceTecIDScanResult.backImagesCompressedBase64.size > 0) faceTecIDScanResult.backImagesCompressedBase64[0] else ""
        val request = Match3D2DIdScanBackRequest(
            idScan = faceTecIDScanResult.idScanBase64,
            idScanBackImage = backImagesCompressedBase64,
            enableConfirmInfo = pref.enableConfirmInfo
        )
        val authedHeaders = headersProvider.getAuthenticatedHeaders(
            authorization = Config.token,
            deviceKey = pref.deviceKey,
            userAgent = FaceTecSDK.createFaceTecAPIUserAgentString(Config.sessionId),
            sessionId = Config.x_session_id,
            tid = GetDeviceSettingUseCase.getUUID(),
            ekycToken = pref.ekycToken,
            correlationId = GetDeviceSettingUseCase.getUUID(),
            sdkVersion = GetDeviceSettingUseCase.getVersionName()
        )

        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback,
            authedHeaders,
            request
        )
    }

    override fun processIDScanWhileFaceTecSDKWaits(
        faceTecIDScanResult: FaceTecIDScanResult,
        faceTecIDScanResultCallback: FaceTecIDScanResultCallback
    ) {
        if (faceTecIDScanResult.status != FaceTecIDScanStatus.SUCCESS) {
            ClearTokenUseCase.execute(pref)
            HandleCallback.ocrResultsCallback?.onSuccess(
                success = false,
                description = USER_CANCELLED,
                userOcrValue = null,
                userConfirmedValue = null
            )
            faceTecIDScanResultCallback.cancel()
            return
        }

        if (faceTecIDScanResult.frontImagesCompressedBase64.size > 0 && faceTecIDScanResult.backImagesCompressedBase64.size <= 0) {
            scanFrontImage(
                faceTecIDScanResult,
                faceTecIDScanResultCallback
            )
        } else if (faceTecIDScanResult.frontImagesCompressedBase64.size > 0 && faceTecIDScanResult.backImagesCompressedBase64.size > 0) {
            scanBackImage(
                faceTecIDScanResult,
                faceTecIDScanResultCallback
            )
        }
    }

    override fun navigateToReviewInformation(
        userConfirmedValueDisplay: UserConfirmedValueDisplay
    ) {
        ReviewInformationEkycActivity.startActivity(
            context,
            userConfirmedValueDisplay,
            checkExpiredIdCard
        )
    }
}