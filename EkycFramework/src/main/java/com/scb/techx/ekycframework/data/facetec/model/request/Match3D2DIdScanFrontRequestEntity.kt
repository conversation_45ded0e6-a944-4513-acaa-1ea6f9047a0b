package com.scb.techx.ekycframework.data.facetec.model.request

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class Match3D2DIdScanFrontRequestEntity(
    @SerializedName("idScan")
    val idScan: String,
    @SerializedName("idScanFrontImage")
    val idScanFrontImage: String?,
    @SerializedName("enableConfirmInfo")
    val enableConfirmInfo: Boolean
)