package com.scb.techx.ekycframework.ui.base

import com.scb.techx.ekycframework.ui.ndidverification.activity.NdidVerificationActivity

interface BaseView {

    fun showDialog(
        message: String,
        isPositiveOnly: <PERSON><PERSON><PERSON>,
        positiveCallback: NdidVerificationActivity.PositiveCallback,
        negativeCallback: NdidVerificationActivity.NegativeCallback?,
        positiveButtonText: String? = null,
        negativeButtonText: String? = null
    )

    fun handleHttpException(throwable: Throwable)
}