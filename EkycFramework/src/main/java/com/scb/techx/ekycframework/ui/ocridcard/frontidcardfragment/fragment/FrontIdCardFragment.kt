
package com.scb.techx.ekycframework.ui.ocridcard.frontidcardfragment.fragment

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.ui.ocridcard.activity.OcrIdCardActivity

class FrontIdCardFragment : Fragment() {

    override fun onResume() {
        super.onResume()
        if (!isAllowCameraPermission()) {
            activity?.finish()
        }
    }

    override fun onPause() {
        super.onPause()
        activity?.finish()
    }

    private fun isAllowCameraPermission(): Boolean {
        return activity?.let {
            ActivityCompat.checkSelfPermission(
                it,
                Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
        } ?: false
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_front_id_card, container, false)
    }

    companion object {
        @JvmStatic
        fun newInstance() = FrontIdCardFragment()
    }
}