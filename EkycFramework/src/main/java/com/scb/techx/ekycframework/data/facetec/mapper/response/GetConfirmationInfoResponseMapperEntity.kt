package com.scb.techx.ekycframework.data.facetec.mapper.response

import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.DopaData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.data.facetec.model.response.ConfirmationInfoResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.DopaDataEntity
import com.scb.techx.ekycframework.data.facetec.model.response.UserConfirmedValueEntity

class GetConfirmationInfoResponseMapperEntity {
    fun mapFromEntity(entity: ConfirmationInfoResponseEntity): ConfirmationInfoResponse {
        return ConfirmationInfoResponse(
            code = entity.code,
            description = entity.description,
            data = mapDopaData(entity.data)
        )
    }

    private fun mapDopaData(entity: DopaDataEntity?): DopaData? {
        return if(entity == null) {
            null
        }
        else {
            DopaData(
                dopaCode = entity.dopaCode,
                dopaDesc = entity.dopaDesc,
                userConfirmedValue = entity.userConfirmedValue?.let {
                    mapUserConfirm(it)
                }
            )
        }
    }

    private fun mapUserConfirm(entity: UserConfirmedValueEntity): UserConfirmedValue? {
        return UserConfirmedValue(
            nationalId = entity.nationalId,
            titleEn = entity.titleEn,
            firstNameEn = entity.firstNameEn,
            middleNameEn = entity.middleNameEn,
            lastNameEn = entity.lastNameEn,
            titleTh = entity.titleTh,
            firstNameTh = entity.firstNameTh,
            middleNameTh = entity.middleNameTh,
            lastNameTh = entity.lastNameTh,
            dateOfIssue = entity.dateOfIssue,
            dateOfExpiry = entity.dateOfExpiry,
            dateOfBirth = entity.dateOfBirth,
            laserId = entity.laserId
        )
    }
}