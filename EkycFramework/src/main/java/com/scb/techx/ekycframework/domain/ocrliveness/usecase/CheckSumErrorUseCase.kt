package com.scb.techx.ekycframework.domain.ocrliveness.usecase

class CheckSumErrorUseCase {
    companion object {
        fun isValidIdNumber(idCard: String): Boolean {
            return try {
                var result = 0
                for (i in 0 .. idCard.length - 2) {
                    result += (13 - i) * idCard[i].digitToInt()
                }
                result = 11 - (result % 11)
                if (result >= 10) {
                    result -= 10
                }
                result == idCard[12].digitToInt()
            } catch (e: Exception) {
                false
            }
        }
    }
}