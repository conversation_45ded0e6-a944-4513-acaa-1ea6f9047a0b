<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="@dimen/margin20">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_ndid_header_enrollment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/Ekyc_ndid_holding_title"
                android:textColor="@android:color/black"
                android:textSize="@dimen/fontSize18"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_ndid_sub_header_enrollment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_ndid_header_enrollment"
                android:layout_marginTop="@dimen/margin8"
                android:text="@string/Ekyc_ndid_holding_autenticate_title"
                android:textColor="@color/colorBlack"
                android:textSize="@dimen/fontSize16"
                android:alpha="@integer/dimen60"/>

            <TextView
                android:id="@+id/tv_ndid_description_enrollment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_ndid_sub_header_enrollment"
                android:text="@string/Ekyc_ndid_holding_autenticate_detail"
                android:textColor="@color/colorBlack"
                android:textSize="@dimen/fontSize16"
                android:alpha="@integer/dimen40"/>

            <RelativeLayout
                android:id="@+id/rl_ndid_circle_timer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_ndid_description_enrollment"
                android:layout_marginTop="42dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_ndid_outside_circle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:src="@drawable/circle_171"
                    android:alpha="@integer/dimen60"
                    android:tint="#f0f0ff" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_ndid_inside_circle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:src="@drawable/circle_125"
                    android:tint="#f0f0ff" />

                <TextView
                    android:id="@+id/tv_timer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="59:35"
                    android:textColor="@color/colorButtonNext"
                    android:textSize="24sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_ndid_under_timer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_ndid_circle_timer"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="14dp"
                android:gravity="center"
                android:text="@string/Ekyc_ndid_holding_verify_identification"
                android:textColor="@color/colorBlack"
                android:textSize="@dimen/fontSize16" />

            <TextView
                android:id="@+id/tv_ndid_ref_id_under_timer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_ndid_under_timer"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center_horizontal"
                android:text="@string/Ekyc_ndid_holding_referralCode"
                android:textColor="@color/colorBlack"
                android:alpha="@integer/dimen40"
                android:textSize="@dimen/fontSize14" />

            <LinearLayout
                android:id="@+id/ll_ndid_bank_tile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_ndid_ref_id_under_timer"
                android:layout_marginTop="58dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_header_bank_tile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/Ekyc_ndid_holding_open"
                    android:textColor="@color/colorBlack"
                    android:alpha="@integer/dimen60"
                    android:textSize="@dimen/fontSize14"
                    android:textStyle="bold" />

                <RelativeLayout
                    android:id="@+id/rl_ndid_bank_deeplink_tile"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:layout_marginTop="@dimen/margin8"
                    android:background="@drawable/background_ndid_border"
                    android:paddingHorizontal="@dimen/margin16">

                    <ImageView
                        android:id="@+id/iv_ndid_bank_logo_tile"
                        android:layout_width="32dp"
                        android:layout_height="match_parent"
                        android:contentDescription="logo"
                        android:src="@drawable/new_scb_logo_24_px_copy_2" />

                    <TextView
                        android:id="@+id/tv_ndid_bank_name_tile"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/margin8"
                        android:layout_toEndOf="@+id/iv_ndid_bank_logo_tile"
                        android:gravity="center"
                        android:text="K Bank"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textColor="#4d4d4d"
                        android:textSize="@dimen/fontSize18"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/ic_baseline_arrow_forward_ios_24"
                        android:tint="@android:color/black" />

                </RelativeLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/tv_ndid_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_ndid_bank_tile"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="21dp"
                android:clickable="true"
                android:textStyle="bold"
                android:text="@string/Ekyc_ndid_holding_cancel_button"
                android:textColor="@color/colorButtonNext"
                android:textSize="@dimen/fontSize16" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/bt_ndid_verify"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_below="@+id/tv_ndid_cancel"
                android:layout_alignParentBottom="true"
                android:layout_marginTop="21dp"
                android:layout_marginBottom="21dp"
                android:background="@drawable/shape_btn_verify_complete"
                android:gravity="center"
                android:text="@string/Ekyc_ndid_holding_confirm"
                android:textAllCaps="false"
                android:textColor="#855aff"
                android:textStyle="bold"
                android:textSize="@dimen/fontSize16" />

        </RelativeLayout>
    </ScrollView>

</RelativeLayout>