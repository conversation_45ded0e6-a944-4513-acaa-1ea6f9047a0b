<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_bank_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/margin21"
        android:text="@string/Ekyc_ndid_idp_title"
        android:textSize="@dimen/fontSize18"
        android:textStyle="bold"
        android:textColor="@color/colorBlack"
        />

    <TextView
        android:id="@+id/tv_bank_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/colorBlack"
        app:layout_constraintTop_toBottomOf="@id/tv_bank_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/margin8"
        android:alpha="@integer/dimen60"
        android:text="@string/Ekyc_ndid_idp_detail"
        android:textSize="@dimen/fontSize14"
        />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_bank_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:layout_marginBottom="@dimen/margin40"
        app:layout_constraintBottom_toTopOf="@+id/cl_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_bank_description" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_button"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/margin24">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_idp_cancel"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:text="@string/Ekyc_ndid_cancel"
            android:gravity="center"
            android:textSize="@dimen/fontSize16"
            android:textStyle="bold"
            android:textAllCaps="false"
            android:textColor="@color/colorButtonCancel"
            android:layout_marginEnd="@dimen/margin8"
            android:background="@drawable/shape_btn_cancel"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/guideline"
            />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_idp_next"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:text="@string/Ekyc_ndid_next"
            android:enabled="false"
            android:gravity="center"
            android:textStyle="bold"
            android:textSize="@dimen/fontSize16"
            android:textAllCaps="false"
            android:textColor="@color/colorWhite"
            android:layout_marginStart="@dimen/margin8"
            android:background="@drawable/shape_btn_next"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/guideline"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>