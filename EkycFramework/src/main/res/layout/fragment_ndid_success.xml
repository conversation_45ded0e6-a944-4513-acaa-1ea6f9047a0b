<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.ndidverification.ndidsuccess.fragment.NdidSuccessFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_ndid_success_icon"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toTopOf="@+id/bt_ndid_next"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="@fraction/bias50"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="@fraction/bias25"
            app:layout_constraintWidth_percent="@fraction/bias25"
            app:srcCompat="@drawable/ndid_success_logo" />

        <TextView
            android:id="@+id/tv_ndid_success_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin16"
            android:layout_marginEnd="@dimen/margin16"
            android:gravity="center"
            android:singleLine="false"
            android:text="@string/Ekyc_ndid_successful_title"
            android:textColor="@android:color/black"
            android:textSize="28sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/bt_ndid_next"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_ndid_success_icon"
            app:layout_constraintVertical_bias="@fraction/bias06" />

        <TextView
            android:id="@+id/tv_ndid_success_subtext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin20"
            android:gravity="center"
            android:text="@string/Ekyc_ndid_successful_detail"
            android:textColor="@android:color/black"
            android:textSize="@dimen/fontSize16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_ndid_success_text" />

        <TextView
            android:id="@+id/tv_ndid_refcode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin4"
            android:textSize="@dimen/fontSize14"
            android:text="refcode"
            android:textColor="@android:color/black"
            android:alpha="@integer/dimen40"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_ndid_success_subtext" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/bt_ndid_next"
            android:gravity="center"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginBottom="@dimen/margin24"
            android:background="@drawable/shape_btn_next"
            android:text="@string/Ekyc_ndid_successful_button"
            android:textAllCaps="false"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/fontSize16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>