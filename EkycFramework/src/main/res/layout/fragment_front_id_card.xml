<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.ocridcard.frontidcardfragment.fragment.FrontIdCardFragment">
    
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/facetec_inactive_torch"
        android:layout_marginTop="@dimen/margin16"
        android:layout_marginEnd="@dimen/margin16"
        android:layout_alignParentEnd="true"
        android:contentDescription="torch" />

    <RelativeLayout
        android:layout_width="200dp"
        android:layout_height="44dp"
        android:layout_above="@id/v_camera"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/margin16"
        android:background="@drawable/shape_btn_ocr">

        <TextView
            android:id="@+id/tv_front_scan_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="Show Front of ID"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/fontSize20" />

    </RelativeLayout>

    <View
        android:id="@+id/v_camera"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_centerInParent="true"
        android:layout_marginHorizontal="@dimen/margin16"
        android:background="@drawable/shape_ocr_camera" />

    <TextView
        android:id="@+id/tv_front_scan_focus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/v_camera"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/margin16"
        android:text="Tap Screen To Focus"
        android:textColor="@color/colorBlack"
        android:textSize="@dimen/fontSize16" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/bt_front_scan_photo"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="@dimen/margin16"
        android:layout_marginBottom="@dimen/margin16"
        android:background="@drawable/shape_btn_ocr"
        android:gravity="center"
        android:text="TAKE PHOTO"
        android:textAllCaps="false"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/fontSize16" />

</RelativeLayout>