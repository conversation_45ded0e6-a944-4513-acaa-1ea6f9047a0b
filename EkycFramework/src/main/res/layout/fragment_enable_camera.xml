<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_enable_camera_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tv_enable_camera_header"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/margin16"
        android:contentDescription="enable_camera_icon"
        android:src="@drawable/facetec_camera" />

    <TextView
        android:id="@+id/tv_enable_camera_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="@string/Ekyc_camera_permission_header"
        android:textColor="@color/colorBlue"
        android:textSize="@dimen/fontSize30"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_enable_camera_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_enable_camera_header"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/margin16"
        android:gravity="center"
        android:text="@string/Ekyc_camera_permission_message_enroll"
        android:textColor="@color/colorBlue"
        android:textSize="@dimen/fontSize16" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/bt_enable_camera"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="@dimen/margin16"
        android:layout_marginBottom="@dimen/margin16"
        android:background="@drawable/shape_btn_ocr"
        android:gravity="center"
        android:text="@string/Ekyc_camera_permission_enable_camera"
        android:textAllCaps="false"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/fontSize16" />

</RelativeLayout>