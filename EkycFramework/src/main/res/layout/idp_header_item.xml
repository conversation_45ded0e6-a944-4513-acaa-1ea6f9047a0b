<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_banK_selection_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textSize="@dimen/fontSize14"
        android:layout_marginTop="@dimen/margin24"
        android:textColor="@color/colorBlack"
        android:alpha="@integer/dimen60"
        android:textStyle="bold"/>

</androidx.constraintlayout.widget.ConstraintLayout>