<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/rl_root"
    android:orientation="vertical"
    android:background="@color/colorWhite"
    android:theme="@style/Theme.MaterialComponents.Light">

    <RelativeLayout
        android:id="@+id/rl_review"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/margin16"
        android:layout_marginTop="@dimen/margin4"
        android:layout_weight="@fraction/weight5"
        android:layout_above="@+id/iv_ndid_bank_logo_tile"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:background="@drawable/background_ocr_border">

        <ScrollView
            android:id="@+id/sv_review"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin2"
            android:layout_marginTop="@dimen/margin16"
            android:layout_above="@+id/tv_confirm"
            android:fadeScrollbars="false"
            android:orientation="vertical"
            android:scrollbarThumbVertical="@drawable/scrollbar_vertical_thumb"
            android:scrollbarTrackVertical="@drawable/scrollbar_vertical_track">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageButton
                        android:id="@+id/ib_cancel"
                        android:layout_width="@dimen/width20"
                        android:layout_height="@dimen/height20"
                        android:adjustViewBounds="true"
                        android:background="@null"
                        android:scaleType="fitCenter"
                        android:layout_marginBottom="@dimen/margin16"
                        android:layout_marginStart="@dimen/margin16"
                        android:contentDescription="cancel_button"
                        android:src="@drawable/facetec_cancel" />

                    <TextView
                        android:id="@+id/tv_main_header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin16"
                        android:gravity="center"
                        android:text="@string/Ekyc_review_main_title"
                        android:textColor="@color/facetec_blue"
                        android:textSize="@dimen/fontSize18"
                        android:textStyle="bold" />

                </RelativeLayout>

                <View
                    android:id="@+id/v_section_header"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/height2"
                    android:layout_marginHorizontal="@dimen/margin8"
                    android:background="@color/facetec_blue" />

                <TextView
                    android:id="@+id/tv_section_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginVertical="@dimen/margin16"
                    android:text="@string/Ekyc_review_header_title"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/fontSize18"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/margin8"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_id_number"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:text="@string/Ekyc_review_card_id_number"
                            android:layout_gravity="center"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_id_number"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_id_number"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:ellipsize="end"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:hint="@string/Ekyc_review_card_id_number_placeholder"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:inputType="date"
                                    android:imeOptions="actionDone"
                                    android:maxLength="17"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_title_th"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_title_th"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:text="@string/Ekyc_review_title_name_th"
                            android:layout_gravity="center"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_title_th"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_title_th"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_title_name_th_placeholder"
                                    android:inputType="text"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:maxLength="20"
                                    android:imeOptions="actionDone"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_firstname_th"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:layout_gravity="center"
                            android:text="@string/Ekyc_review_first_name_th"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_firstname_th"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_firstname_th"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_first_name_th_placeholder"
                                    android:inputType="text"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:maxLength="50"
                                    android:imeOptions="actionDone"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_middle_th"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:layout_gravity="center"
                            android:text="@string/Ekyc_review_middle_name_th"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_middle_th"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_middle_th"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_middle_name_th_placeholder"
                                    android:inputType="text"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:maxLength="50"
                                    android:imeOptions="actionDone"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_lastname_th"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:layout_gravity="center"
                            android:text="@string/Ekyc_review_last_name_th"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_lastname_th"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_lastname_th"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_last_name_th_placeholder"
                                    android:inputType="text"
                                    android:imeOptions="actionDone"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:maxLength="50"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_title_en"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_title_en"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:layout_gravity="center"
                            android:text="@string/Ekyc_review_title_name_en"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_title_en"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_title_en"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_title_name_en_placeholder"
                                    android:inputType="text"
                                    android:imeOptions="actionDone"
                                    android:maxLength="20"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_firstname_en"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:layout_gravity="center"
                            android:text="@string/Ekyc_review_first_name_en"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_firstname_en"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_firstname_en"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_first_name_en_placeholder"
                                    android:inputType="text"
                                    android:maxLength="50"
                                    android:imeOptions="actionDone"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_middle_en"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:layout_gravity="center"
                            android:text="@string/Ekyc_review_middle_name_en"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_middle_en"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_middle_en"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_middle_name_en_placeholder"
                                    android:inputType="text"
                                    android:maxLength="50"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:maxLines="1"
                                    android:imeOptions="actionDone"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_lastname_en"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:layout_gravity="center"
                            android:text="@string/Ekyc_review_last_name_en"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_lastname_en"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_lastname_en"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_last_name_en_placeholder"
                                    android:inputType="text"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:maxLength="50"
                                    android:imeOptions="actionDone"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:maxLines="1"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_date_of_birth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="@fraction/weight1"
                            android:text="@string/Ekyc_review_date_of_birth"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                            <LinearLayout
                                android:id="@+id/ll_date_of_birth"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/margin8"
                                android:layout_weight="@fraction/weight1.9"
                                android:orientation="horizontal">

                                <EditText
                                    android:id="@+id/et_day_date_of_birth"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight2"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:hint="@string/Ekyc_review_day_placeholder"
                                    android:inputType="date"
                                    android:maxLength="2"
                                    android:imeOptions="actionDone"
                                    android:nextFocusDown="@id/et_month_date_of_birth"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="@fraction/weight1"
                                    android:gravity="center"
                                    android:text="/"
                                    android:textColor="@android:color/black"
                                    android:textSize="@dimen/fontSize20" />

                                <EditText
                                    android:id="@+id/et_month_date_of_birth"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight2"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:hint="@string/Ekyc_review_month_placeholder"
                                    android:nextFocusDown="@id/et_year_date_of_birth"
                                    android:inputType="date"
                                    android:maxLength="2"
                                    android:imeOptions="actionDone"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="@fraction/weight1"
                                    android:gravity="center"
                                    android:text="/"
                                    android:textColor="@android:color/black"
                                    android:textSize="@dimen/fontSize20" />

                                <EditText
                                    android:id="@+id/et_year_date_of_birth"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight4"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:imeOptions="actionDone"
                                    android:hint="@string/Ekyc_review_year_placeholder"
                                    android:inputType="date"
                                    android:nextFocusDown="@id/et_day_date_of_issued"
                                    android:maxLength="4"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                            </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_error_date_of_birth"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1" />

                        <TextView
                            android:id="@+id/tv_error_date_of_birth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1.9"
                            android:layout_marginStart="@dimen/margin8"
                            android:paddingStart="@dimen/padding8"
                            android:paddingLeft="@dimen/padding8"
                            android:text="@string/Ekyc_review_require"
                            android:textColor="@android:color/holo_red_dark"
                            android:textSize="@dimen/fontSize12" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_issued_date"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="@fraction/weight1"
                            android:text="@string/Ekyc_review_date_of_issue"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                            <LinearLayout
                                android:id="@+id/ll_date_of_issued"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/margin8"
                                android:layout_weight="@fraction/weight1.9"
                                android:orientation="horizontal">

                                <EditText
                                    android:id="@+id/et_day_date_of_issued"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight2"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:imeOptions="actionDone"
                                    android:hint="@string/Ekyc_review_day_placeholder"
                                    android:nextFocusDown="@id/et_month_date_of_issued"
                                    android:inputType="date"
                                    android:maxLength="2"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="@fraction/weight1"
                                    android:gravity="center"
                                    android:text="/"
                                    android:textColor="@android:color/black"
                                    android:textSize="@dimen/fontSize20" />

                                <EditText
                                    android:id="@+id/et_month_date_of_issued"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight2"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:hint="@string/Ekyc_review_month_placeholder"
                                    android:inputType="date"
                                    android:imeOptions="actionDone"
                                    android:nextFocusDown="@id/et_year_date_of_issued"
                                    android:maxLength="2"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="@fraction/weight1"
                                    android:gravity="center"
                                    android:text="/"
                                    android:textColor="@android:color/black"
                                    android:textSize="@dimen/fontSize20" />

                                <EditText
                                    android:id="@+id/et_year_date_of_issued"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight4"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:imeOptions="actionDone"
                                    android:nextFocusDown="@id/et_day_date_of_expiration"
                                    android:hint="@string/Ekyc_review_year_placeholder"
                                    android:inputType="date"
                                    android:maxLength="4"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                            </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_error_date_of_issued"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1" />

                        <TextView
                            android:id="@+id/tv_error_date_of_issued"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1.9"
                            android:layout_marginStart="@dimen/margin8"
                            android:paddingStart="@dimen/padding8"
                            android:paddingLeft="@dimen/padding8"
                            android:text="@string/Ekyc_review_require"
                            android:textColor="@android:color/holo_red_dark"
                            android:textSize="@dimen/fontSize12" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_expiration_date"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:text="@string/Ekyc_review_date_of_expire"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                            <LinearLayout
                                android:id="@+id/ll_date_of_expiration"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/margin8"
                                android:layout_weight="@fraction/weight1.9"
                                android:orientation="horizontal">

                                <EditText
                                    android:id="@+id/et_day_date_of_expiration"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight2"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:imeOptions="actionDone"
                                    android:hint="@string/Ekyc_review_day_placeholder"
                                    android:inputType="date"
                                    android:nextFocusDown="@id/et_month_date_of_expiration"
                                    android:maxLength="2"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="@fraction/weight1"
                                    android:gravity="center"
                                    android:text="/"
                                    android:textColor="@android:color/black"
                                    android:textSize="@dimen/fontSize20" />

                                <EditText
                                    android:id="@+id/et_month_date_of_expiration"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight2"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:nextFocusDown="@id/et_year_date_of_expiration"
                                    android:hint="@string/Ekyc_review_month_placeholder"
                                    android:inputType="date"
                                    android:imeOptions="actionDone"
                                    android:maxLength="2"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="@fraction/weight1"
                                    android:gravity="center"
                                    android:text="/"
                                    android:textColor="@android:color/black"
                                    android:textSize="@dimen/fontSize20" />

                                <EditText
                                    android:id="@+id/et_year_date_of_expiration"
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/height45"
                                    android:layout_weight="@fraction/weight4"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:gravity="center"
                                    android:hint="@string/Ekyc_review_year_placeholder"
                                    android:inputType="date"
                                    android:imeOptions="actionDone"
                                    android:maxLength="4"
                                    android:textColor="@android:color/black"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:textSize="@dimen/fontSize14" />

                            </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1" />

                        <CheckBox
                            android:id="@+id/cb_expiration"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1.9"
                            android:buttonTint="@android:color/black"
                            android:layout_marginStart="@dimen/margin8"
                            android:paddingStart="@dimen/padding8"
                            android:paddingLeft="@dimen/padding8"
                            android:text="@string/Ekyc_review_no_expire_date_placeholder"
                            android:textSize="@dimen/fontSize14" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_error_date_of_expiration"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1" />

                        <TextView
                            android:id="@+id/tv_error_date_of_expiration"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1.9"
                            android:layout_marginStart="@dimen/margin8"
                            android:paddingStart="@dimen/padding8"
                            android:paddingLeft="@dimen/padding8"
                            android:text="@string/Ekyc_review_require"
                            android:textColor="@android:color/holo_red_dark"
                            android:textSize="@dimen/fontSize12" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin8"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_laser_id"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="@fraction/weight1"
                            android:text="@string/Ekyc_review_laser_id_number"
                            android:layout_gravity="center"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/fontSize14" />

                        <RelativeLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin8"
                            android:layout_weight="@fraction/weight1.9">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/tl_laser_id"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                app:endIconMode="clear_text"
                                app:helperTextEnabled="true"
                                app:helperTextTextColor="@android:color/holo_red_dark"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/te_laser_id"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:ellipsize="end"
                                    android:hint="@string/Ekyc_review_laser_id_number_placeholder"
                                    android:inputType="text"
                                    android:background="@drawable/shape_review_edit_text_normal"
                                    android:textCursorDrawable="@drawable/cursor_color"
                                    android:maxLines="1"
                                    android:maxLength="14"
                                    android:imeOptions="actionDone"
                                    android:paddingStart="@dimen/padding8"
                                    android:paddingLeft="@dimen/padding8"
                                    android:textSize="@dimen/fontSize14" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </RelativeLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </ScrollView>

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/rl_button"
            android:layout_centerHorizontal="true"
            android:layout_marginHorizontal="@dimen/margin8"
            android:layout_marginTop="@dimen/margin8"
            android:gravity="center"
            android:text="@string/Ekyc_review_dialog_description"
            android:textColor="@color/facetec_blue"
            android:textSize="@dimen/fontSize14" />

        <RelativeLayout
            android:id="@+id/rl_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginHorizontal="@dimen/margin16"
            android:layout_marginTop="@dimen/margin8"
            android:layout_marginBottom="@dimen/margin16"
            >

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_confirm"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height60"
            android:backgroundTint="@color/facetec_button_background_color"
            android:padding="1dp"
            android:text="@string/Ekyc_review_confirm"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/fontSize20"
            app:cornerRadius="@dimen/radius8" />
        </RelativeLayout>


    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_ndid_bank_logo_tile"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/height28"
        android:layout_marginHorizontal="@dimen/margin16"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/margin24"
        android:contentDescription="logo"
        android:src="@drawable/facetec_your_app_logo" />

</RelativeLayout>