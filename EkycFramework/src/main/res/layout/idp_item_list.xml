<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bg_selection"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/margin8"
        android:background="@drawable/shape_bank_selection"
        >

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imv_bank_logo"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/margin11"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/margin11"
            android:layout_marginStart="@dimen/margin16"
           />

        <TextView
            android:id="@+id/tv_bank_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/imv_bank_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/imv_bank_logo"
            app:layout_constraintTop_toTopOf="@id/imv_bank_logo"
            android:textSize="@dimen/fontSize16"
            android:textStyle="bold"
            android:textColor="@color/colorBlack"
            android:layout_marginStart="@dimen/margin8"/>

</androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>