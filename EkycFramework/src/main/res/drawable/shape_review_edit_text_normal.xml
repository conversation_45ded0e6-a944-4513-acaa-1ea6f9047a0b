<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:state_enabled="true"
        android:state_focused="true"
        >
        <shape android:shape="rectangle" >
            <solid android:color="@android:color/white"/>
            <corners android:radius="8dp" />
            <stroke
                android:color="@color/colorBlue"
                android:width="2dp"/>
        </shape>
    </item>

    <item android:state_enabled="true">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/white"/>
            <corners android:radius="8dp" />
            <stroke
                android:color="@color/colorEnableTextField"
                android:width="1dp"/>
        </shape>
    </item>

    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/white"/>
            <corners android:radius="8dp"/>
            <stroke android:color="@color/colorDisableTextField"
                android:width="1dp"/>
        </shape>
    </item>

</selector>