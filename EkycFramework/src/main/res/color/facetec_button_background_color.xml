<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--When But<PERSON> is not enabled -->
    <item android:state_enabled="false" android:color="@color/colorButtonOcrDisable"/>
    <!--When But<PERSON> is in pressed state -->
    <item android:state_pressed="true" android:color="@color/colorButtonOcrEnable" />
    <!--Default Background Color -->
    <item android:color="@color/colorButtonOcrEnable" />
</selector>