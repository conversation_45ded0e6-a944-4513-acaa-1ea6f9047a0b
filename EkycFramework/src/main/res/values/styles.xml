<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="PopupStyle" parent="Theme.AppCompat.Dialog.Alert">
        <item name="colorBackgroundFloating">@color/colorWhite</item>
        <item name="android:textColorPrimary">@color/colorBlack</item>
        <item name="buttonBarPositiveButtonStyle">@style/PopupStyle.PositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/PopupStyle.NegativeButtonStyle</item>
    </style>
    <style name="PopupStyle.PositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/androidDefaultTeal</item>
        <item name="rippleColor">@color/androidLighterDefaultTeal</item>
    </style>
    <style name="PopupStyle.NegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">#757575</item>
        <item name="rippleColor">#a4a4a4</item>
    </style>
</resources>