package com.scb.techx.ekycframework.data.facetec.mapper

import com.scb.techx.ekycframework.data.facetec.mapper.response.GetConfirmationInfoResponseMapperEntity
import com.scb.techx.ekycframework.data.facetec.model.response.ConfirmationInfoResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.DopaDataEntity
import com.scb.techx.ekycframework.data.facetec.model.response.UserConfirmedValueEntity
import org.junit.Assert
import org.junit.Test

class GetConfirmationInfoResponseMapperEntityTest {
    private val mapper: GetConfirmationInfoResponseMapperEntity = GetConfirmationInfoResponseMapperEntity()

    @Test
    fun `when convert confirmationInfoResponseEntityFull`() {
        //given
        val confirmationInfoResponseEntityFull: ConfirmationInfoResponseEntity = ConfirmationInfoResponseEntity(
            code = "code",
            description = "description",
            data = DopaDataEntity(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValueEntity(
                    nationalId = "nationalId",
                    titleTh = "titleTh",
                    titleEn = "titleEn",
                    firstNameTh = "firstNameTh",
                    firstNameEn = "firstNameEn",
                    middleNameTh = "middleNameTh",
                    middleNameEn = "middleNameEn",
                    lastNameTh = "lastNameTh",
                    lastNameEn = "lastNameEn",
                    dateOfBirth = "dateOfBirth",
                    dateOfIssue = "dateOfIssue",
                    dateOfExpiry = "dateOfExpiry",
                    laserId = "laserId"
                )
            )
        )
        //when
        val result = mapper.mapFromEntity(confirmationInfoResponseEntityFull)

        //then
        Assert.assertEquals(confirmationInfoResponseEntityFull.code, result.code)
        Assert.assertEquals(confirmationInfoResponseEntityFull.description, result.description)
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.dateOfBirth,
            result.data?.userConfirmedValue?.dateOfBirth
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.dateOfExpiry,
            result.data?.userConfirmedValue?.dateOfExpiry
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.dateOfIssue,
            result.data?.userConfirmedValue?.dateOfIssue
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.laserId,
            result.data?.userConfirmedValue?.laserId
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.firstNameEn,
            result.data?.userConfirmedValue?.firstNameEn
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.firstNameTh,
            result.data?.userConfirmedValue?.firstNameTh
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.lastNameEn,
            result.data?.userConfirmedValue?.lastNameEn
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.lastNameTh,
            result.data?.userConfirmedValue?.lastNameTh
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.middleNameEn,
            result.data?.userConfirmedValue?.middleNameEn
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.middleNameTh,
            result.data?.userConfirmedValue?.middleNameTh
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.nationalId,
            result.data?.userConfirmedValue?.nationalId
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.titleEn,
            result.data?.userConfirmedValue?.titleEn
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.userConfirmedValue?.titleTh,
            result.data?.userConfirmedValue?.titleTh
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.dopaCode,
            result.data?.dopaCode
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityFull.data?.dopaDesc,
            result.data?.dopaDesc
        )
    }

    @Test
    fun `when convert confirmationInfoResponseEntityUserConfirmEmpty`() {
        //given
        val confirmationInfoResponseEntityUserConfirmEmpty: ConfirmationInfoResponseEntity = ConfirmationInfoResponseEntity(
            code = "code",
            description = "description",
            data = DopaDataEntity(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = null
            )
        )

        //when
        val result = mapper.mapFromEntity(confirmationInfoResponseEntityUserConfirmEmpty)

        //then
        Assert.assertEquals(confirmationInfoResponseEntityUserConfirmEmpty.code, result.code)
        Assert.assertEquals(confirmationInfoResponseEntityUserConfirmEmpty.description, result.description)
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.dateOfBirth
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.dateOfExpiry
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.dateOfIssue
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.laserId
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.firstNameEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.firstNameTh
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.lastNameEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.lastNameTh
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.middleNameEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.middleNameTh
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.nationalId
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.titleEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.titleTh
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityUserConfirmEmpty.data?.dopaCode,
            result.data?.dopaCode
        )
        Assert.assertEquals(
            confirmationInfoResponseEntityUserConfirmEmpty.data?.dopaDesc,
            result.data?.dopaDesc
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue
        )
    }

    @Test
    fun `when convert confirmationInfoResponseEntityDopaEmpty`() {
        //given
        val confirmationInfoResponseEntityDopaEmpty: ConfirmationInfoResponseEntity = ConfirmationInfoResponseEntity(
            code = "code",
            description = "description",
            data = null
        )

        //when
        val result = mapper.mapFromEntity(confirmationInfoResponseEntityDopaEmpty)

        //then
        Assert.assertEquals(confirmationInfoResponseEntityDopaEmpty.code, result.code)
        Assert.assertEquals(confirmationInfoResponseEntityDopaEmpty.description, result.description)
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.dateOfBirth
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.dateOfExpiry
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.dateOfIssue
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.laserId
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.firstNameEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.firstNameTh
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.lastNameEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.lastNameTh
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.middleNameEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.middleNameTh
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.nationalId
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.titleEn
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue?.titleTh
        )
        Assert.assertEquals(
            null,
            result.data?.dopaCode
        )
        Assert.assertEquals(
            null,
            result.data?.dopaDesc
        )
        Assert.assertEquals(
            null,
            result.data?.userConfirmedValue
        )
    }
}