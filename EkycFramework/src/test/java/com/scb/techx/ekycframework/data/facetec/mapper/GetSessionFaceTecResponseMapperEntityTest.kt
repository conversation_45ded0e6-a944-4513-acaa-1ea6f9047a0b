package com.scb.techx.ekycframework.data.facetec.mapper

import com.scb.techx.ekycframework.data.facetec.mapper.response.GetSessionFaceTecResponseMapperEntity
import com.scb.techx.ekycframework.data.facetec.model.response.SessionFaceTecDataEntity
import com.scb.techx.ekycframework.data.facetec.model.response.SessionFaceTecResponseEntity
import org.junit.Assert
import org.junit.Test

class GetSessionFaceTecResponseMapperEntityTest {
    private val mapper: GetSessionFaceTecResponseMapperEntity = GetSessionFaceTecResponseMapperEntity()

    @Test
    fun `when convert sessionFaceTecResponseEntity`() {
        //given
        val sessionFaceTecResponseEntityFull: SessionFaceTecResponseEntity = SessionFaceTecResponseEntity(
            code = "code",
            description = "description",
            data = SessionFaceTecDataEntity(
                sessionFaceTec = "sessionFaceTec",
                productionKey = "productionKey",
                deviceKey = "deviceKey",
                encryptionKey = "encryptionKey"
            )
        )

        //when
        val result = mapper.mapFromEntity(sessionFaceTecResponseEntityFull)

        //then
        Assert.assertEquals(sessionFaceTecResponseEntityFull.code, result.code)
        Assert.assertEquals(sessionFaceTecResponseEntityFull.description, result.description)
        Assert.assertEquals(
            sessionFaceTecResponseEntityFull.data?.sessionFaceTec,
            result.data?.sessionFaceTec
        )
        Assert.assertEquals(
            sessionFaceTecResponseEntityFull.data?.productionKey,
            result.data?.productionKey,
        )
        Assert.assertEquals(
            sessionFaceTecResponseEntityFull.data?.deviceKey,
            result.data?.deviceKey,
        )
        Assert.assertEquals(
            sessionFaceTecResponseEntityFull.data?.encryptionKey,
            result.data?.encryptionKey,
        )
    }

    @Test
    fun `when convert match3D2DIdScanResponseEntityNull`() {
        //given
        val sessionFaceTecResponseEntityNull: SessionFaceTecResponseEntity = SessionFaceTecResponseEntity(
            code = "code",
            description = "description",
            data = null
        )

        //when
        val result = mapper.mapFromEntity(sessionFaceTecResponseEntityNull)

        //then
        Assert.assertEquals(sessionFaceTecResponseEntityNull.code, result.code)
        Assert.assertEquals(sessionFaceTecResponseEntityNull.description, result.description)
        Assert.assertEquals(
            null,
            result.data?.sessionFaceTec
        )
        Assert.assertEquals(
            null,
            result.data?.productionKey,
        )
        Assert.assertEquals(
            null,
            result.data?.deviceKey,
        )
        Assert.assertEquals(
            null,
            result.data?.encryptionKey,
        )
        Assert.assertEquals(
            null,
            result.data,
        )
    }
}