package com.scb.techx.ekycframework.data.ndid.mapper.idpList

import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidIdpResponseMapperEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.IdpDataEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.IdpListEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidIdpResponseEntity
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class NdidIdpResponseMapperEntityTest {
    private val mapper = NdidIdpResponseMapperEntity()

    private val ndidIdpResponseEntityMock = NdidIdpResponseEntity(
        code = "code",
        description = "description",
        data = IdpDataEntity(
            registeredIdpList = mutableListOf(
                IdpListEntity(
                    nodeId = "registeredNodeId1",
                    industryCode = "registeredIndustryCode1",
                    companyCode = "registeredCompanyCode1",
                    marketingNameTh = "registeredMarketingNameTh1",
                    marketingNameEn = "registeredMarketingNameEn1",
                    smallIconPath = "registeredSmallIconPath1",
                    mediumIconPath = "registeredMediumIconPath1",
                    largeIconPath = "registeredLargeIconPath1",
                    deepLinkAndroid = "registeredDeepLinkAndroid1",
                    deepLinkIos = "registeredDeepLinkIos1",
                    deepLinkHuawei = "registeredDeepLinkHuawei1"
                ),
                IdpListEntity(
                    nodeId = "registeredNodeId2",
                    industryCode = "registeredIndustryCode2",
                    companyCode = "registeredCompanyCode2",
                    marketingNameTh = "registeredMarketingNameTh2",
                    marketingNameEn = "registeredMarketingNameEn2",
                    smallIconPath = "registeredSmallIconPath2",
                    mediumIconPath = "registeredMediumIconPath2",
                    largeIconPath = "registeredLargeIconPath2",
                    deepLinkAndroid = "registeredDeepLinkAndroid2",
                    deepLinkIos = "registeredDeepLinkIos2",
                    deepLinkHuawei = "registeredDeepLinkHuawei2"
                )
            ),
            idpList = mutableListOf(
                IdpListEntity(
                    nodeId = "nodeId1",
                    industryCode = "industryCode1",
                    companyCode = "companyCode1",
                    marketingNameTh = "marketingNameTh1",
                    marketingNameEn = "marketingNameEn1",
                    smallIconPath = "smallIconPath1",
                    mediumIconPath = "mediumIconPath1",
                    largeIconPath = "largeIconPath1",
                    deepLinkAndroid = "deepLinkAndroid1",
                    deepLinkIos = "deepLinkIos1",
                    deepLinkHuawei = "deepLinkHuawei1"
                ),
                IdpListEntity(
                    nodeId = "nodeId2",
                    industryCode = "industryCode2",
                    companyCode = "companyCode2",
                    marketingNameTh = "marketingNameTh2",
                    marketingNameEn = "marketingNameEn2",
                    smallIconPath = "smallIconPath2",
                    mediumIconPath = "mediumIconPath2",
                    largeIconPath = "largeIconPath2",
                    deepLinkAndroid = "deepLinkAndroid2",
                    deepLinkIos = "deepLinkIos2",
                    deepLinkHuawei = "deepLinkHuawei2"
                )
            )
        )
    )

    private val ndidIdpResponseEntityRegisteredNullMock = NdidIdpResponseEntity(
        code = "code",
        description = "description",
        data = IdpDataEntity(
            registeredIdpList = null,
            idpList = mutableListOf(
                IdpListEntity(
                    nodeId = "nodeId1",
                    industryCode = "industryCode1",
                    companyCode = "companyCode1",
                    marketingNameTh = "marketingNameTh1",
                    marketingNameEn = "marketingNameEn1",
                    smallIconPath = "smallIconPath1",
                    mediumIconPath = "mediumIconPath1",
                    largeIconPath = "largeIconPath1",
                    deepLinkAndroid = "deepLinkAndroid1",
                    deepLinkIos = "deepLinkIos1",
                    deepLinkHuawei = "deepLinkHuawei1"
                ),
                IdpListEntity(
                    nodeId = "nodeId2",
                    industryCode = "industryCode2",
                    companyCode = "companyCode2",
                    marketingNameTh = "marketingNameTh2",
                    marketingNameEn = "marketingNameEn2",
                    smallIconPath = "smallIconPath2",
                    mediumIconPath = "mediumIconPath2",
                    largeIconPath = "largeIconPath2",
                    deepLinkAndroid = "deepLinkAndroid2",
                    deepLinkIos = "deepLinkIos2",
                    deepLinkHuawei = "deepLinkHuawei2"
                )
            )
        )
    )

    private val ndidIdpResponseEntityNotRegisteredNullMock = NdidIdpResponseEntity(
        code = "code",
        description = "description",
        data = IdpDataEntity(
            registeredIdpList = mutableListOf(
                IdpListEntity(
                    nodeId = "registeredNodeId1",
                    industryCode = "registeredIndustryCode1",
                    companyCode = "registeredCompanyCode1",
                    marketingNameTh = "registeredMarketingNameTh1",
                    marketingNameEn = "registeredMarketingNameEn1",
                    smallIconPath = "registeredSmallIconPath1",
                    mediumIconPath = "registeredMediumIconPath1",
                    largeIconPath = "registeredLargeIconPath1",
                    deepLinkAndroid = "registeredDeepLinkAndroid1",
                    deepLinkIos = "registeredDeepLinkIos1",
                    deepLinkHuawei = "registeredDeepLinkHuawei1"
                ),
                IdpListEntity(
                    nodeId = "registeredNodeId2",
                    industryCode = "registeredIndustryCode2",
                    companyCode = "registeredCompanyCode2",
                    marketingNameTh = "registeredMarketingNameTh2",
                    marketingNameEn = "registeredMarketingNameEn2",
                    smallIconPath = "registeredSmallIconPath2",
                    mediumIconPath = "registeredMediumIconPath2",
                    largeIconPath = "registeredLargeIconPath2",
                    deepLinkAndroid = "registeredDeepLinkAndroid2",
                    deepLinkIos = "registeredDeepLinkIos2",
                    deepLinkHuawei = "registeredDeepLinkHuawei2"
                )
            ),
            idpList = null
        )
    )

    private val ndidIdpResponseEntityDataNullMock = NdidIdpResponseEntity(
        code = "code",
        description = "description",
        data = null
    )

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Test
    fun `when convert full ndid idp response entity`() {
        val result = mapper.mapFromEntity(ndidIdpResponseEntityMock)

        Assert.assertEquals(ndidIdpResponseEntityMock.code, result.code)
        Assert.assertEquals(ndidIdpResponseEntityMock.description, result.description)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.nodeId, result.data?.idpList?.get(0)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.industryCode, result.data?.idpList?.get(0)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.companyCode, result.data?.idpList?.get(0)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.marketingNameTh, result.data?.idpList?.get(0)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.marketingNameEn, result.data?.idpList?.get(0)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.smallIconPath, result.data?.idpList?.get(0)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.mediumIconPath, result.data?.idpList?.get(0)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.largeIconPath, result.data?.idpList?.get(0)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.deepLinkAndroid, result.data?.idpList?.get(0)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.deepLinkIos, result.data?.idpList?.get(0)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(0)?.deepLinkHuawei, result.data?.idpList?.get(0)?.deepLinkHuawei)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.nodeId, result.data?.idpList?.get(1)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.industryCode, result.data?.idpList?.get(1)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.companyCode, result.data?.idpList?.get(1)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.marketingNameTh, result.data?.idpList?.get(1)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.marketingNameEn, result.data?.idpList?.get(1)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.smallIconPath, result.data?.idpList?.get(1)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.mediumIconPath, result.data?.idpList?.get(1)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.largeIconPath, result.data?.idpList?.get(1)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.deepLinkAndroid, result.data?.idpList?.get(1)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.deepLinkIos, result.data?.idpList?.get(1)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.idpList?.get(1)?.deepLinkHuawei, result.data?.idpList?.get(1)?.deepLinkHuawei)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.nodeId, result.data?.registeredIdpList?.get(0)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.industryCode, result.data?.registeredIdpList?.get(0)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.companyCode, result.data?.registeredIdpList?.get(0)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.marketingNameTh, result.data?.registeredIdpList?.get(0)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.marketingNameEn, result.data?.registeredIdpList?.get(0)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.smallIconPath, result.data?.registeredIdpList?.get(0)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.mediumIconPath, result.data?.registeredIdpList?.get(0)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.largeIconPath, result.data?.registeredIdpList?.get(0)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.deepLinkAndroid, result.data?.registeredIdpList?.get(0)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.deepLinkIos, result.data?.registeredIdpList?.get(0)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(0)?.deepLinkHuawei, result.data?.registeredIdpList?.get(0)?.deepLinkHuawei)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.nodeId, result.data?.registeredIdpList?.get(1)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.industryCode, result.data?.registeredIdpList?.get(1)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.companyCode, result.data?.registeredIdpList?.get(1)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.marketingNameTh, result.data?.registeredIdpList?.get(1)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.marketingNameEn, result.data?.registeredIdpList?.get(1)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.smallIconPath, result.data?.registeredIdpList?.get(1)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.mediumIconPath, result.data?.registeredIdpList?.get(1)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.largeIconPath, result.data?.registeredIdpList?.get(1)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.deepLinkAndroid, result.data?.registeredIdpList?.get(1)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.deepLinkIos, result.data?.registeredIdpList?.get(1)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityMock.data?.registeredIdpList?.get(1)?.deepLinkHuawei, result.data?.registeredIdpList?.get(1)?.deepLinkHuawei)
    }

    @Test
    fun `when convert full ndid idp response entity with unregistered idp list null`() {
        val result = mapper.mapFromEntity(ndidIdpResponseEntityNotRegisteredNullMock)

        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.code, result.code)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.description, result.description)
        Assert.assertNull(result.data?.idpList)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.nodeId, result.data?.registeredIdpList?.get(0)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.industryCode, result.data?.registeredIdpList?.get(0)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.companyCode, result.data?.registeredIdpList?.get(0)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.marketingNameTh, result.data?.registeredIdpList?.get(0)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.marketingNameEn, result.data?.registeredIdpList?.get(0)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.smallIconPath, result.data?.registeredIdpList?.get(0)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.mediumIconPath, result.data?.registeredIdpList?.get(0)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.largeIconPath, result.data?.registeredIdpList?.get(0)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.deepLinkAndroid, result.data?.registeredIdpList?.get(0)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.deepLinkIos, result.data?.registeredIdpList?.get(0)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(0)?.deepLinkHuawei, result.data?.registeredIdpList?.get(0)?.deepLinkHuawei)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.nodeId, result.data?.registeredIdpList?.get(1)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.industryCode, result.data?.registeredIdpList?.get(1)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.companyCode, result.data?.registeredIdpList?.get(1)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.marketingNameTh, result.data?.registeredIdpList?.get(1)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.marketingNameEn, result.data?.registeredIdpList?.get(1)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.smallIconPath, result.data?.registeredIdpList?.get(1)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.mediumIconPath, result.data?.registeredIdpList?.get(1)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.largeIconPath, result.data?.registeredIdpList?.get(1)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.deepLinkAndroid, result.data?.registeredIdpList?.get(1)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.deepLinkIos, result.data?.registeredIdpList?.get(1)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityNotRegisteredNullMock.data?.registeredIdpList?.get(1)?.deepLinkHuawei, result.data?.registeredIdpList?.get(1)?.deepLinkHuawei)
    }

    @Test
    fun `when convert full ndid idp response entity with registered idp list null`() {
        val result = mapper.mapFromEntity(ndidIdpResponseEntityRegisteredNullMock)

        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.code, result.code)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.description, result.description)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.nodeId, result.data?.idpList?.get(0)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.industryCode, result.data?.idpList?.get(0)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.companyCode, result.data?.idpList?.get(0)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.marketingNameTh, result.data?.idpList?.get(0)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.marketingNameEn, result.data?.idpList?.get(0)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.smallIconPath, result.data?.idpList?.get(0)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.mediumIconPath, result.data?.idpList?.get(0)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.largeIconPath, result.data?.idpList?.get(0)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.deepLinkAndroid, result.data?.idpList?.get(0)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.deepLinkIos, result.data?.idpList?.get(0)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(0)?.deepLinkHuawei, result.data?.idpList?.get(0)?.deepLinkHuawei)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.nodeId, result.data?.idpList?.get(1)?.nodeId)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.industryCode, result.data?.idpList?.get(1)?.industryCode)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.companyCode, result.data?.idpList?.get(1)?.companyCode)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.marketingNameTh, result.data?.idpList?.get(1)?.marketingNameTh)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.marketingNameEn, result.data?.idpList?.get(1)?.marketingNameEn)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.smallIconPath, result.data?.idpList?.get(1)?.smallIconPath)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.mediumIconPath, result.data?.idpList?.get(1)?.mediumIconPath)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.largeIconPath, result.data?.idpList?.get(1)?.largeIconPath)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.deepLinkAndroid, result.data?.idpList?.get(1)?.deepLinkAndroid)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.deepLinkIos, result.data?.idpList?.get(1)?.deepLinkIos)
        Assert.assertEquals(ndidIdpResponseEntityRegisteredNullMock.data?.idpList?.get(1)?.deepLinkHuawei, result.data?.idpList?.get(1)?.deepLinkHuawei)
        Assert.assertNull(result.data?.registeredIdpList)
    }

    @Test
    fun `when convert full ndid idp response entity with data null`() {
        val result = mapper.mapFromEntity(ndidIdpResponseEntityDataNullMock)

        Assert.assertEquals(ndidIdpResponseEntityDataNullMock.code, result.code)
        Assert.assertEquals(ndidIdpResponseEntityDataNullMock.description, result.description)
        Assert.assertNull(result.data)
    }
}