package com.scb.techx.ekycframework.util

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import com.nhaarman.mockito_kotlin.*
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.SessionFaceTecData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.SessionFaceTecResponse
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.util.facetechelper.model.FaceTecFeatureType
import com.scb.techx.ekycframework.util.facetechelper.presenter.FaceTecInitializeHelperContract
import com.scb.techx.ekycframework.util.facetechelper.presenter.FaceTecInitializeHelperPresenter
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.ResponseBody
import okhttp3.internal.http2.ErrorCode
import okhttp3.internal.http2.StreamResetException
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import retrofit2.HttpException
import retrofit2.Response
import java.util.concurrent.TimeUnit

class FaceTecInitializeHelperPresenterTest {
    private lateinit var presenter: FaceTecInitializeHelperContract.Presenter
    private lateinit var pref: EkycPreferenceUtil
    private lateinit var ekycFormatterUtil: GetDeviceSettingUseCase
    private lateinit var packageInfo: PackageInfo
    private lateinit var testScheduler: TestScheduler
    private lateinit var ekycUtilities: EkycUtilities

    @Mock
    lateinit var editor: SharedPreferences.Editor

    @Mock
    lateinit var context: Context


    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var repository: FaceTecRepository

    @Mock
    lateinit var packageManager: PackageManager

    @Mock
    lateinit var helper: FaceTecInitializeHelperContract.Helper

    private fun mockForClearPreference() {
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        Mockito.`when`(sharedPreferences.edit()).thenReturn(editor)
        Mockito.`when`(editor.putString(any(), any())).thenReturn(editor)
        Mockito.`when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()
    }

    private fun validateClearPreference() {
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)

        pref = EkycPreferenceUtil(context)
        ekycFormatterUtil = GetDeviceSettingUseCase
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"
        testScheduler = TestScheduler()
        ekycUtilities = EkycUtilities()

        packageInfo = PackageInfo()
        packageInfo.versionName = "Mock Version"

        presenter = FaceTecInitializeHelperPresenter(
            helper,
            repository
        )
    }

    @Test
    fun `getSessionToken Receive code 1000`() {
        //given
        val expectedResponse = SessionFaceTecResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = SessionFaceTecData(
                sessionFaceTec = "mock sessionFaceTec",
                productionKey = "mock productionKey",
                deviceKey = "mock deviceKey",
                encryptionKey = "mock encryptionKey"
            )
        )

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.LIVENESS,
            true, pref ,
            testScheduler,
            testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(editor).putString(eq("sessionFaceTec"), eq("mock sessionFaceTec"))
        verify(editor).putString(eq("productionKey"), eq("mock productionKey"))
        verify(editor).putString(eq("deviceKey"), eq("mock deviceKey"))
        verify(editor).putString(eq("encryptionKey"), eq("mock encryptionKey"))
        verify(helper).initializeFaceTecSDK(any(), any(), any())
    }

    @Test
    fun `getSessionToken Receive other code instead of 1000 when is enrollment`() {
        //given
        val expectedResponse = SessionFaceTecResponse(
            code = "CUS-KYC-2002",
            description = "Unable to process",
            data = null
        )

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.LIVENESS,
            true,
            pref,
            testScheduler,
            testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(helper).handleCallbackFalseEnrollment(eq("Unable to process"))
    }

    @Test
    fun `getSessionToken Receive other code instead of 1000 when is ocr liveness`() {
        //given
        val expectedResponse = SessionFaceTecResponse(
            code = "CUS-KYC-2002",
            description = "Unable to process",
            data = null
        )

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.OCRLIVENESS,
            true,
            pref,
            testScheduler,
            testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(helper).handleCallbackFalseOCRLiveness(eq("Unable to process"))
    }

    @Test
    fun `getSessionToken Receive throw 504`() {
        //given
        val throwable = HttpException(
            Response.error<Any>(504, ResponseBody.create(
                "plain/text".toMediaType(), ""
            )))

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.error(
            throwable
        ))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.OCRLIVENESS,
            true,
            pref,
            testScheduler,
            testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(helper).handleCallbackFalseOCRLiveness(eq("Timeout"))
    }

    @Test
    fun `getSessionToken Receive throw 401`() {
        //given
        val throwable = HttpException(
            Response.error<Any>(401, ResponseBody.create(
                "plain/text".toMediaType(), ""
            )))

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.error(
            throwable
        ))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.OCRLIVENESS,
            true,
            pref,
            testScheduler,
            testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(helper).handleCallbackFalseOCRLiveness(eq("Invalid token or token expired"))
    }

    @Test
    fun `getSessionToken Receive throw other http error`() {
        //given
        val throwable = HttpException(
            Response.error<Any>(500, ResponseBody.create(
                "plain/text".toMediaType(), ""
            )))

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.error(
            throwable
        ))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.OCRLIVENESS,
            true,
            pref,
            testScheduler,
            testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(helper).handleCallbackFalseOCRLiveness(eq("Unable to process"))
    }

    @Test
    fun `getSessionToken Receive stream reset (kong error)`() {
        //given
        val throwable = StreamResetException(ErrorCode.NO_ERROR)

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.error(
            throwable
        ))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.OCRLIVENESS,
            true,
            pref,
            testScheduler,
            testScheduler
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(helper).handleCallbackFalseOCRLiveness(eq("Connection error"))
    }

    @Test
    fun `getSessionToken Receive unknown error`() {
        //given
        val throwable = java.lang.Exception()

        whenever(repository.getSessionFaceTec(any())).thenReturn(Single.error(
            throwable
        ))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.OCRLIVENESS,
            true,
            pref,
            testScheduler,
            testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(helper).handleCallbackFalseOCRLiveness(eq("Unable to process"))
    }

    @Test
    fun `getSessionToken while already have value in preference`() {
        //given
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("sessionFaceTec", "")).thenReturn("not empty")
        whenever(sharedPreferences.getString("productionKey", "")).thenReturn("not empty")
        whenever(sharedPreferences.getString("deviceKey", "")).thenReturn("not empty")
        whenever(sharedPreferences.getString("encryptionKey", "")).thenReturn("not empty")

        mockForClearPreference()

        //when
        presenter.getSessionFaceTec(
            context,
            FaceTecFeatureType.LIVENESS,
            true, pref ,
            testScheduler,
            testScheduler)

        //then
        verify(repository, never()).getSessionFaceTec(any())
        verify(helper).initializeFaceTecSDK(any(), any(), any())
    }
}