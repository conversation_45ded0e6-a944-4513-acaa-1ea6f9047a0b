package com.scb.techx.ekycframework.data.facetec.mapper

import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.data.facetec.mapper.request.GetEnrollment3DRequestMapperToEntity
import org.junit.Assert
import org.junit.Test

class GetEnrollment3DRequestMapperToEntityTest {
    private val mapper: GetEnrollment3DRequestMapperToEntity = GetEnrollment3DRequestMapperToEntity()

    @Test
    fun `when convert confirmationInfoRequest`() {
        //given
        val confirmationInfoRequest: Enrollment3DRequest = Enrollment3DRequest(
            faceScan = "faceScan",
            auditTrailImage = "auditTrailImage",
            lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
            function = "function"
        )

        //when
        val result = mapper.mapToEntity(confirmationInfoRequest)

        //then
        Assert.assertEquals(confirmationInfoRequest.auditTrailImage, result.auditTrailImage)
        Assert.assertEquals(confirmationInfoRequest.faceScan, result.faceScan)
        Assert.assertEquals(confirmationInfoRequest.function, result.function)
        Assert.assertEquals(confirmationInfoRequest.lowQualityAuditTrailImage, result.lowQualityAuditTrailImage)
    }
}