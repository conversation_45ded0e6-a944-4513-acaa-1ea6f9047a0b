package com.scb.techx.ekycframework.util
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.OcrValidateUseCase
import org.junit.Test
import org.junit.Assert.*

class OcrValidateUseCaseTest {
    @Test
    fun `validate english text when name is null`() {
        val result = OcrValidateUseCase.isTextEnglishValid(null)
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when name is empty`() {
        val result = OcrValidateUseCase.isTextEnglishValid("")
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when name is dash and dot`() {
        val result = OcrValidateUseCase.isTextEnglishValid("James Jr.-")
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when name is thai`() {
        val result = OcrValidateUseCase.isTextEnglishValid("Jamesก")
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when name is valid with dash`() {
        val result = OcrValidateUseCase.isTextEnglishValid("Sae-Lee")
        assertEquals(true, result)
    }

    @Test
    fun `validate english text when name is valid with dot`() {
        val result = OcrValidateUseCase.isTextEnglishValid("James Jr.")
        assertEquals(true, result)
    }

    @Test
    fun `validate english text when title is null`() {
        val result = OcrValidateUseCase.isTextTitleEnglishValid(null)
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when title is empty`() {
        val result = OcrValidateUseCase.isTextTitleEnglishValid("")
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when title is with dash`() {
        val result = OcrValidateUseCase.isTextTitleEnglishValid("Mr-")
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when title is with thai`() {
        val result = OcrValidateUseCase.isTextTitleEnglishValid("Mrข")
        assertEquals(false, result)
    }

    @Test
    fun `validate english text when title is valid`() {
        val result = OcrValidateUseCase.isTextTitleEnglishValid("Mr.")
        assertEquals(true, result)
    }

    @Test
    fun `validate thai text when name is null`() {
        val result = OcrValidateUseCase.isTextThaiValid(null)
        assertEquals(false, result)
    }

    @Test
    fun `validate thai text when name is empty`() {
        val result = OcrValidateUseCase.isTextThaiValid("")
        assertEquals(false, result)
    }

    @Test
    fun `validate thai text when name is with special character`() {
        val result = OcrValidateUseCase.isTextThaiValid("ส.")
        assertEquals(true, result)
    }

    @Test
    fun `validate thai text when name is with invalid special character`() {
        val result1 = OcrValidateUseCase.isTextThaiValid("ส.-")
        val result2 = OcrValidateUseCase.isTextThaiValid("ส..")
        val result3 = OcrValidateUseCase.isTextThaiValid("ส--")
        assertEquals(false, result1)
        assertEquals(false, result2)
        assertEquals(false, result3)
    }

    @Test
    fun `validate thai text when name is with english`() {
        val result = OcrValidateUseCase.isTextThaiValid("สูs")
        assertEquals(false, result)
    }

    @Test
    fun `validate thai text when name have space`() {
        val result = OcrValidateUseCase.isTextThaiValid("สู อี้")
        assertEquals(true, result)
    }

    @Test
    fun `validate thai text when name have double space`() {
        val result = OcrValidateUseCase.isTextThaiValid("สู  อี้")
        assertEquals(false, result)
    }

    @Test
    fun `validate thai text when name is valid`() {
        val result = OcrValidateUseCase.isTextThaiValid("สูอี้")
        assertEquals(true, result)
    }

    @Test
    fun `validate thai text when title is null`() {
        val result = OcrValidateUseCase.isTextTitleThaiValid(null)
        assertEquals(false, result)
    }

    @Test
    fun `validate thai text when title is empty`() {
        val result = OcrValidateUseCase.isTextTitleThaiValid("")
        assertEquals(false, result)
    }

    @Test
    fun `validate thai text when title is with english`() {
        val result = OcrValidateUseCase.isTextTitleThaiValid("น.ส.l")
        assertEquals(false, result)
    }

    @Test
    fun `validate thai text when title is valid`() {
        val result = OcrValidateUseCase.isTextTitleThaiValid("น.ส.")
        assertEquals(true, result)
    }

    @Test
    fun `validate id card when it is null`() {
        val result = OcrValidateUseCase.isIdCardNumberValid(null)
        assertEquals(false, result)
    }

    @Test
    fun `validate id card when it is empty`() {
        val result = OcrValidateUseCase.isIdCardNumberValid("")
        assertEquals(false, result)
    }

    @Test
    fun `validate id card when it have non numeric`() {
        val result = OcrValidateUseCase.isIdCardNumberValid("123ab67890123")
        assertEquals(false, result)
    }

    @Test
    fun `validate id card when it is all number`() {
        val result = OcrValidateUseCase.isIdCardNumberValid("1234567890123")
        assertEquals(true, result)
    }

    @Test
    fun `validate laser id when it is null`() {
        val result = OcrValidateUseCase.isLaserIdValid(null)
        assertEquals(false, result)
    }

    @Test
    fun `validate laser id when it is wrong format`() {
        val result = OcrValidateUseCase.isLaserIdValid("ASD122332344")
        assertEquals(false, result)
    }

    @Test
    fun `validate laser id when it is right format`() {
        val result = OcrValidateUseCase.isLaserIdValid("AS1122332344")
        assertEquals(true, result)
    }
}