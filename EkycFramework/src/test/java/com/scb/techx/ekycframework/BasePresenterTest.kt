package com.scb.techx.ekycframework

import android.content.Context
import android.content.SharedPreferences
import com.nhaarman.mockito_kotlin.any
import com.nhaarman.mockito_kotlin.doNothing
import com.nhaarman.mockito_kotlin.eq
import com.nhaarman.mockito_kotlin.verify
import com.scb.techx.ekycframework.ui.base.presenter.BasePresenter
import com.scb.techx.ekycframework.ui.ndidverification.presenter.NdidVerificationContract
import com.scb.techx.ekycframework.domain.common.usecase.IgnoreOpacityUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.ResponseBody
import okhttp3.internal.http2.ErrorCode
import okhttp3.internal.http2.StreamResetException
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import retrofit2.HttpException
import retrofit2.Response

class BasePresenterTest {
    lateinit var presenter: BasePresenter
    lateinit var pref: EkycPreferenceUtil
    lateinit var ekycCommonUtil: IgnoreOpacityUseCase

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var view: NdidVerificationContract.View

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var editor: SharedPreferences.Editor

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)

        pref = EkycPreferenceUtil(context)
        ekycCommonUtil = IgnoreOpacityUseCase

        presenter = BasePresenter(pref)
    }

    @Test
    fun handleHttpException401() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        val throwable = HttpException(Response.error<Any>(401, ResponseBody.create(
            "plain/text".toMediaType(), ""
        )))

        //when
        presenter.handleHttpException(throwable, view)

        //then
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
        verify(view).showDialog(
            message = eq("Invalid token or token expired"),
            isPositiveOnly = eq(true),
            positiveCallback = any(),
            negativeCallback = eq(null),
            positiveButtonText = eq(null),
            negativeButtonText = eq(null)
        )
    }

    @Test
    fun handleHttpException504() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        val throwable = HttpException(Response.error<Any>(504, ResponseBody.create(
            "plain/text".toMediaType(), ""
        )))

        //when
        presenter.handleHttpException(throwable, view)

        //then
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
        verify(view).showDialog(
            message = eq("Timeout"),
            isPositiveOnly = eq(true),
            positiveCallback = any(),
            negativeCallback = eq(null),
            positiveButtonText = eq(null),
            negativeButtonText = eq(null)
        )
    }

    @Test
    fun handleHttpExceptionElse() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        val throwable = HttpException(Response.error<Any>(404, ResponseBody.create(
            "plain/text".toMediaType(), ""
        )))

        //when
        presenter.handleHttpException(throwable, view)

        //then
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
        verify(view).showDialog(
            message = eq("Unable to process"),
            isPositiveOnly = eq(true),
            positiveCallback = any(),
            negativeCallback = eq(null),
            positiveButtonText = eq(null),
            negativeButtonText = eq(null)
        )
    }

    @Test
    fun handleStreamResetException() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        val throwable = StreamResetException(ErrorCode.CONNECT_ERROR)

        //when
        presenter.handleHttpException(throwable, view)

        //then
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
        verify(view).showDialog(
            message = eq("Connection error"),
            isPositiveOnly = eq(true),
            positiveCallback = any(),
            negativeCallback = eq(null),
            positiveButtonText = eq(null),
            negativeButtonText = eq(null)
        )
    }

    @Test
    fun handleOtherException() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        val throwable = RuntimeException()

        //when
        presenter.handleHttpException(throwable, view)

        //then
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
        verify(view).showDialog(
            message = eq("Unable to process"),
            isPositiveOnly = eq(true),
            positiveCallback = any(),
            negativeCallback = eq(null),
            positiveButtonText = eq(null),
            negativeButtonText = eq(null)
        )
    }

    @Test
    fun handleErrorEkyc1001() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-1001", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Invalid ekycToken"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc1002() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-1002", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("ekycToken expired"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc1003() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-1003", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Access denied"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc1004() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-1004", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("ekycToken not enough time"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc1899() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-1899", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Unable to process"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc1999() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-1999", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Unable to process"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc2001() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-2001", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Unable to process"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc2002() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-2002", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Unable to process"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc2003() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-2003", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Data not found"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc2004() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-2004", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Unable to decrypt image"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc3001() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-3001", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("NDID on processing."),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc3003() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-3003", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("NDID is not available time"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc4001() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-4001", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Duplicate session Id"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc7101() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-7101", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("OCR Check Liveness Fail (Enrollment-3d)"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc7102() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-7102", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("OCR Match Photo ID and Selfie Fail (Match-3d-2d-idscan)"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc7103() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-7103", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Compare Reference Image and Selfie Fail (Match-3d-2d-face-portrait)"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc7201() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-7201", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("DOPA Fail"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc7301() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-7301", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("NDID Fail"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc9001() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-9001", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Timeout"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkyc9002() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-9002", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Connection error"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Test
    fun handleErrorEkycElse() {
        //given
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(any(), any())).thenReturn(editor)
        `when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()

        //when
        presenter.handleErrorEkyc("CUS-KYC-9999", view)

        //then
        verify(view).handleCallback(
            success = eq(false),
            description = eq("Unable to process"),
            ndidData = eq(null),
            ndidStatus = eq(null),
            ndidError = eq(null)
        )
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }
}