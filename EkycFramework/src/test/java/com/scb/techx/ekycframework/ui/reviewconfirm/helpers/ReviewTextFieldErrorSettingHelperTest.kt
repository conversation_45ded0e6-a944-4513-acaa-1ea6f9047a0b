package com.scb.techx.ekycframework.ui.reviewconfirm.helpers

import android.content.res.ColorStateList
import android.content.res.Resources
import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.textfield.TextInputLayout
import com.nhaarman.mockito_kotlin.eq
import com.nhaarman.mockito_kotlin.verify
import com.nhaarman.mockito_kotlin.whenever
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.ui.reviewconfirm.activity.ReviewInformationEkycActivity
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType
import com.scb.techx.ekycframework.ui.reviewconfirm.helper.ReviewTextFieldErrorSettingHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycPresenter
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations

class ReviewTextFieldErrorSettingHelperTest {

    private lateinit var fieldErrorSettingHelper: ReviewTextFieldErrorSettingHelper

    @Mock
    lateinit var activity: ReviewInformationEkycActivity

    @Mock
    lateinit var presenter: ReviewInformationEkycPresenter

    @Mock
    lateinit var resources: Resources

    @Mock
    lateinit var editText: EditText

    @Mock
    lateinit var dayEditText: EditText

    @Mock
    lateinit var monthEditText: EditText

    @Mock
    lateinit var yearEditText: EditText

    @Mock
    lateinit var dateErrorTextView: TextView

    @Mock
    lateinit var dateErrorLayout: LinearLayout

    @Mock
    lateinit var textInputLayout: TextInputLayout

    @Mock
    lateinit var colorStateList: ColorStateList

    @Mock
    lateinit var dayEditable: Editable

    @Mock
    lateinit var monthEditable: Editable

    @Mock
    lateinit var yearEditable: Editable


    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)

        fieldErrorSettingHelper = ReviewTextFieldErrorSettingHelper(
            activity,
            presenter
        )
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"
    }

    @Test
    fun `validate when setting text field error`() {
        // when
        fieldErrorSettingHelper.setFieldAsError(
            editText,
            textInputLayout,
            "Mock"
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(textInputLayout).setHelperText("Mock")
    }

    @Test
    fun `validate when setting text field as non-error`() {
        // when
        fieldErrorSettingHelper.setFieldAsNormal(
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        verify(textInputLayout).setHelperText(null)
    }

    @Test
    fun `validate when setting date field as error`() {
        // when
        fieldErrorSettingHelper.setDateFieldAsError(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout,
            "Mock"
        )

        // then
        verify(dayEditText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(monthEditText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(yearEditText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(dateErrorLayout).setVisibility(View.VISIBLE)
        verify(dateErrorTextView).setVisibility(View.VISIBLE)
        verify(dateErrorTextView).setText("Mock")
    }

    @Test
    fun `validate when setting date field as non-error`() {
        // when
        fieldErrorSettingHelper.setDateFieldAsNormal(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        verify(monthEditText).setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        verify(yearEditText).setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        verify(dateErrorLayout).setVisibility(View.GONE)
        verify(dateErrorTextView).setVisibility(View.GONE)
    }

    @Test
    fun `validate when setting text field as disable`() {
        // given
        whenever(editText.textColors).thenReturn(
            colorStateList
        )

        // when
        fieldErrorSettingHelper.setFieldAsDisable(
            editText
        )

        // then
        verify(editText).isFocusable = false
        verify(editText).isCursorVisible = false
        verify(editText).isFocusableInTouchMode = false
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        verify(colorStateList).withAlpha(eq(128))
    }

    @Test
    fun `validate when setting date field as disable`() {
        // when
        fieldErrorSettingHelper.setDateFieldAsDisable(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        verify(monthEditText).setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        verify(yearEditText).setBackgroundResource(R.drawable.shape_review_edit_text_disable)

        verify(dateErrorLayout).setVisibility(View.GONE)
        verify(dateErrorTextView).setVisibility(View.GONE)
    }

    @Test
    fun `validate when setting optional field is valid and in english`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en)).thenReturn(
            "Mock EN"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_th)).thenReturn(
            "Mock TH"
        )

        // when
        fieldErrorSettingHelper.setErrorOptionalField(
            ValidateType.VALID,
            editText,
            textInputLayout,
            true
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        verify(textInputLayout).setHelperText(null)
    }

    @Test
    fun `validate when setting optional field is not valid and in english`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en)).thenReturn(
            "Mock EN"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_th)).thenReturn(
            "Mock TH"
        )

        // when
        fieldErrorSettingHelper.setErrorOptionalField(
            ValidateType.VALIDATE_INVALID,
            editText,
            textInputLayout,
            true
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(textInputLayout).setHelperText(resources.getString(R.string.Ekyc_review_allow_en))
    }

    @Test
    fun `validate when setting optional field is not valid and in thai`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en)).thenReturn(
            "Mock EN"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_th)).thenReturn(
            "Mock TH"
        )

        // when
        fieldErrorSettingHelper.setErrorOptionalField(
            ValidateType.VALIDATE_INVALID,
            editText,
            textInputLayout,
            false
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(textInputLayout).setHelperText(resources.getString(R.string.Ekyc_review_allow_th))
    }

    @Test
    fun `validate when setting error message for laser id as valid`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_laser)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Invalid"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en_and_number)).thenReturn(
            "Wrong Letter"
        )

        // when
        fieldErrorSettingHelper.setErrorLaserId(
            ValidateType.VALID,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        verify(textInputLayout).setHelperText(null)
    }

    @Test
    fun `validate when setting error message for laser id as empty`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_laser)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Invalid"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en_and_number)).thenReturn(
            "Wrong Letter"
        )

        // when
        fieldErrorSettingHelper.setErrorLaserId(
            ValidateType.NULL_OR_EMPTY,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(textInputLayout).setHelperText(resources.getString(R.string.Ekyc_review_require))
    }

    @Test
    fun `validate when setting error message for laser id as not enough length`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_laser)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Invalid"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en_and_number)).thenReturn(
            "Wrong Letter"
        )

        // when
        fieldErrorSettingHelper.setErrorLaserId(
            ValidateType.LENGTH,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(textInputLayout).setHelperText(resources.getString(R.string.Ekyc_review_allow_laser))
    }

    @Test
    fun `validate when setting error message for laser id as invalid format`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_laser)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Invalid"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en_and_number)).thenReturn(
            "Wrong Letter"
        )

        // when
        fieldErrorSettingHelper.setErrorLaserId(
            ValidateType.VALIDATE_INVALID,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(textInputLayout).setHelperText(resources.getString(R.string.Ekyc_review_correct_error))
    }

    @Test
    fun `validate when setting error message for laser id as wrong letter`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_laser)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Invalid"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en_and_number)).thenReturn(
            "Wrong Letter"
        )

        // when
        fieldErrorSettingHelper.setErrorLaserId(
            ValidateType.WRONG_LETTER,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_error)
        verify(textInputLayout).setHelperText(resources.getString(R.string.Ekyc_review_allow_en_and_number))
    }

    @Test
    fun `validate when setting error message for national id as valid`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_card_id)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_number)).thenReturn(
            "Wrong Letter"
        )
        whenever(resources.getString(R.string.Ekyc_review_check_sum_card_id)).thenReturn(
            "Sum id card"
        )

        // when
        fieldErrorSettingHelper.setErrorNationalId(
            ValidateType.VALID,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_normal)
        verify(textInputLayout).setHelperText(null)
    }

    @Test
    fun `validate when setting error message for national id as valid and locked`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_card_id)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_number)).thenReturn(
            "Wrong Letter"
        )
        whenever(resources.getString(R.string.Ekyc_review_check_sum_card_id)).thenReturn(
            "Sum id card"
        )
        // given
        whenever(editText.textColors).thenReturn(
            colorStateList
        )

        // when
        fieldErrorSettingHelper.setErrorNationalId(
            ValidateType.VALID,
            editText,
            textInputLayout,
            false
        )

        // then
        verify(editText).setBackgroundResource(R.drawable.shape_review_edit_text_disable)
        verify(editText).isClickable = false
        verify(textInputLayout).setHelperText(null)
    }

    @Test
    fun `validate when setting error message for national id as empty`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_card_id)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_number)).thenReturn(
            "Wrong Letter"
        )
        whenever(resources.getString(R.string.Ekyc_review_check_sum_card_id)).thenReturn(
            "Sum id card"
        )

        // when
        fieldErrorSettingHelper.setErrorNationalId(
            ValidateType.NULL_OR_EMPTY,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(textInputLayout).setHelperText(
            eq(resources.getString(R.string.Ekyc_review_require))
        )
    }

    @Test
    fun `validate when setting error message for national id as length too short`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_card_id)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_number)).thenReturn(
            "Wrong Letter"
        )
        whenever(resources.getString(R.string.Ekyc_review_check_sum_card_id)).thenReturn(
            "Sum id card"
        )

        // when
        fieldErrorSettingHelper.setErrorNationalId(
            ValidateType.LENGTH,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(textInputLayout).setHelperText(
            eq(resources.getString(R.string.Ekyc_review_allow_card_id))
        )
    }

    @Test
    fun `validate when setting error message for national id as invalid format`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_card_id)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_number)).thenReturn(
            "Wrong Letter"
        )
        whenever(resources.getString(R.string.Ekyc_review_check_sum_card_id)).thenReturn(
            "Sum id card"
        )

        // when
        fieldErrorSettingHelper.setErrorNationalId(
            ValidateType.VALIDATE_INVALID,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(textInputLayout).setHelperText(
            eq(resources.getString(R.string.Ekyc_review_allow_number))
        )
    }

    @Test
    fun `validate when setting error message for national id as invalid sum`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_card_id)).thenReturn(
            "Length"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_number)).thenReturn(
            "Wrong Letter"
        )
        whenever(resources.getString(R.string.Ekyc_review_check_sum_card_id)).thenReturn(
            "Sum id card"
        )

        // when
        fieldErrorSettingHelper.setErrorNationalId(
            ValidateType.INVALID_ID_SUM,
            editText,
            textInputLayout
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(textInputLayout).setHelperText(
            eq(resources.getString(R.string.Ekyc_review_check_sum_card_id))
        )
    }

    @Test
    fun `validate when setting error message for text as valid`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en)).thenReturn(
            "Allow only english"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_th)).thenReturn(
            "Allow only thai"
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )

        // when
        fieldErrorSettingHelper.setErrorText(
            ValidateType.VALID,
            editText,
            textInputLayout,
            true
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(textInputLayout).setHelperText(null)
    }

    @Test
    fun `validate when setting error message for text as empty`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en)).thenReturn(
            "Allow only english"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_th)).thenReturn(
            "Allow only thai"
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )

        // when
        fieldErrorSettingHelper.setErrorText(
            ValidateType.NULL_OR_EMPTY,
            editText,
            textInputLayout,
            true
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(textInputLayout).setHelperText(
            eq(resources.getString(R.string.Ekyc_review_require))
        )
    }

    @Test
    fun `validate when setting error message for text as wrong letter in english`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en)).thenReturn(
            "Allow only english"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_th)).thenReturn(
            "Allow only thai"
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )

        // when
        fieldErrorSettingHelper.setErrorText(
            ValidateType.VALIDATE_INVALID,
            editText,
            textInputLayout,
            true
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(textInputLayout).setHelperText(
            eq(resources.getString(R.string.Ekyc_review_allow_en))
        )
    }

    @Test
    fun `validate when setting error message for text as wrong letter in thai`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_en)).thenReturn(
            "Allow only english"
        )
        whenever(resources.getString(R.string.Ekyc_review_allow_th)).thenReturn(
            "Allow only thai"
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Require"
        )

        // when
        fieldErrorSettingHelper.setErrorText(
            ValidateType.VALIDATE_INVALID,
            editText,
            textInputLayout,
            false
        )

        // then
        verify(editText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(textInputLayout).setHelperText(
            eq(resources.getString(R.string.Ekyc_review_allow_th))
        )
    }

    @Test
    fun `validate when setting error message for date of birth as valid`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_date_of_birth_not_avaliable)).thenReturn(
            "Please correct the error. If date or month of birth is not available, please fill in -"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "02"
        )
        whenever(monthEditable.toString()).thenReturn(
            "02"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2010"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfBirth(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.GONE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.GONE)
        )
    }

    @Test
    fun `validate when setting error message for date of birth as empty`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_date_of_birth_not_avaliable)).thenReturn(
            "Please correct the error. If date or month of birth is not available, please fill in -"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            ""
        )
        whenever(monthEditable.toString()).thenReturn(
            ""
        )
        whenever(yearEditable.toString()).thenReturn(
            ""
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfBirth(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorTextView).text = eq("Required")
    }

    @Test
    fun `validate when setting error message for date of birth as correct error`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_date_of_birth_not_avaliable)).thenReturn(
            "Please correct the error. If date or month of birth is not available, please fill in -"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "32"
        )
        whenever(monthEditable.toString()).thenReturn(
            "02"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2010"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfBirth(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorTextView).text = eq("Please correct the error. If date or month of birth is not available, please fill in -")
    }

    @Test
    fun `validate when setting error message for date of birth as correct error (date exceed today)`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_date_of_birth_not_avaliable)).thenReturn(
            "Please correct the error. If date or month of birth is not available, please fill in -"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "31"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2099"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfBirth(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorTextView).text = eq("Please correct the error.")
    }

    @Test
    fun `validate when setting error message for date of issue as valid`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_exceed_card)).thenReturn(
            "Issued date cannot be greater than today\'s date"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "31"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2010"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfIssued(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.GONE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.GONE)
        )
    }

    @Test
    fun `validate when setting error message for date of issue as exceed today`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_exceed_card)).thenReturn(
            "Issued date cannot be greater than today\'s date"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "31"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2032"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfIssued(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorTextView).text = eq("Issued date cannot be greater than today\'s date")
    }

    @Test
    fun `validate when setting error message for date of issue as error date`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_exceed_card)).thenReturn(
            "Issued date cannot be greater than today\'s date"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "32"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2032"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfIssued(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorTextView).text = eq("Please correct the error.")
    }

    @Test
    fun `validate when setting error message for date of issue as empty`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_exceed_card)).thenReturn(
            "Issued date cannot be greater than today\'s date"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            ""
        )
        whenever(monthEditable.toString()).thenReturn(
            ""
        )
        whenever(yearEditable.toString()).thenReturn(
            ""
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfIssued(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_error)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.VISIBLE)
        )
        verify(dateErrorTextView).text = eq("Required")
    }

    @Test
    fun `validate when setting error message for date of expire as valid`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_expire_card)).thenReturn(
            "Card is expired"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "31"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2033"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfExpire(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout,
            true
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.GONE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.GONE)
        )
    }

    @Test
    fun `validate when setting error message for date of expire as valid and not check expire`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_expire_card)).thenReturn(
            "Card is expired"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "31"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2021"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfExpire(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout,
            false
        )

        // then
        verify(dayEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(monthEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(yearEditText).setBackgroundResource(
            eq(R.drawable.shape_review_edit_text_normal)
        )
        verify(dateErrorTextView).setVisibility(
            eq(View.GONE)
        )
        verify(dateErrorLayout).setVisibility(
            eq(View.GONE)
        )
    }

    @Test
    fun `validate when setting error message for date of expire as expire`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_expire_card)).thenReturn(
            "Card is expired"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "31"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2021"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfExpire(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout,
            true
        )

        // then
        verify(activity).setErrorDateOfExpire(
            eq("Card is expired")
        )
    }

    @Test
    fun `validate when setting error message for date of expire as wrong format`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_expire_card)).thenReturn(
            "Card is expired"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            "32"
        )
        whenever(monthEditable.toString()).thenReturn(
            "12"
        )
        whenever(yearEditable.toString()).thenReturn(
            "2024"
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfExpire(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout,
            true
        )

        // then
        verify(activity).setErrorDateOfExpire(
            eq("Please correct the error.")
        )
    }

    @Test
    fun `validate when setting error message for date of expire as empty`() {
        // given
        whenever(activity.resources).thenReturn(
            resources
        )
        whenever(resources.getString(R.string.Ekyc_review_expire_card)).thenReturn(
            "Card is expired"
        )
        whenever(resources.getString(R.string.Ekyc_review_correct_error)).thenReturn(
            "Please correct the error."
        )
        whenever(resources.getString(R.string.Ekyc_review_require)).thenReturn(
            "Required"
        )
        whenever(dayEditText.text).thenReturn(
            dayEditable
        )
        whenever(monthEditText.text).thenReturn(
            monthEditable
        )
        whenever(yearEditText.text).thenReturn(
            yearEditable
        )
        whenever(dayEditable.toString()).thenReturn(
            ""
        )
        whenever(monthEditable.toString()).thenReturn(
            ""
        )
        whenever(yearEditable.toString()).thenReturn(
            ""
        )

        // when
        fieldErrorSettingHelper.setGoneErrorDateOfExpire(
            dayEditText,
            monthEditText,
            yearEditText,
            dateErrorTextView,
            dateErrorLayout,
            true
        )

        // then
        verify(activity).setErrorDateOfExpire(
            eq("Required")
        )
    }
}