package com.scb.techx.ekycframework.util

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import com.nhaarman.mockito_kotlin.any
import com.nhaarman.mockito_kotlin.verify
import com.nhaarman.mockito_kotlin.whenever
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.getsession.model.SessionTokenResponse
import com.scb.techx.ekycframework.domain.getsession.repository.GetSessionRepository
import com.scb.techx.ekycframework.domain.getsession.usecase.EkycGetSessionUseCase
import com.scb.techx.ekycframework.ui.processor.Config
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import java.util.concurrent.TimeUnit


class EkycInitializeHelperTest {

    private lateinit var ekycFormatterUtil: GetDeviceSettingUseCase
    private lateinit var packageInfo: PackageInfo
    private lateinit var testScheduler: TestScheduler
    private lateinit var ekycUtilities: EkycUtilities

    @Mock
    lateinit var editor: SharedPreferences.Editor

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var service: GetSessionRepository

    @Mock
    lateinit var packageManager: PackageManager

    @Mock
    lateinit var pref: EkycPreferenceUtil

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)

        ekycFormatterUtil = GetDeviceSettingUseCase
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"
        testScheduler = TestScheduler()
        ekycUtilities = EkycUtilities()

        whenever(pref.ekycToken).thenReturn("")
        whenever(pref.sessionFaceTec).thenReturn("")
        whenever(pref.deviceKey).thenReturn("")
        whenever(pref.encryptionKey).thenReturn("")
        whenever(pref.installationId).thenReturn("")
        whenever(pref.enableConfirmInfo).thenReturn(true)
        whenever(pref.productionKey).thenReturn("")
        whenever(pref.sdkEncryptionIv).thenReturn("")
        whenever(pref.sdkEncryptionIv).thenReturn("")

        packageInfo = PackageInfo()
        packageInfo.versionName = "Mock Version"
    }

    @Test
    fun `getSessionToken Receive code 4001`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        val expectedResponse = SessionTokenResponse(
            code = "CUS_KYC_4001",
            description = "Duplicate session Id",
            data = null
        )

        whenever(service.getSessionToken(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id" ,testScheduler, testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(service).getSessionToken(any())
        Assert.assertEquals(
            false,
            resultSuccess
        )
        Assert.assertEquals(
            "Duplicate session Id",
            resultDescription
        )
    }

    @Test
    fun `getSessionToken Receive code 9001`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        val expectedResponse = SessionTokenResponse(
            code = "CUS_KYC_9001",
            description = "Timeout",
            data = null
        )

        whenever(service.getSessionToken(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id", testScheduler, testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(service).getSessionToken(any())
        Assert.assertEquals(
            false,
            resultSuccess
        )
        Assert.assertEquals(
            "Timeout",
            resultDescription
        )
    }

    @Test
    fun `getSessionToken Receive code 9002`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        val expectedResponse = SessionTokenResponse(
            code = "CUS_KYC_9002",
            description = "Connection error",
            data = null
        )

        whenever(service.getSessionToken(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id", testScheduler, testScheduler)
        testScheduler.advanceTimeBy(20, TimeUnit.SECONDS)

        //then
        verify(service).getSessionToken(any())
        Assert.assertEquals(
            false,
            resultSuccess
        )
        Assert.assertEquals(
            "Connection error",
            resultDescription
        )
    }

    @Test
    fun `getSessionToken Receive code 1999`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        val expectedResponse = SessionTokenResponse(
            code = "CUS_KYC_1999",
            description = "Unable to process",
            data = null
        )

        whenever(service.getSessionToken(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id", testScheduler, testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(service).getSessionToken(any())
        Assert.assertEquals(
            false,
            resultSuccess
        )
        Assert.assertEquals(
            "Unable to process",
            resultDescription
        )
    }

    @Test
    fun `getSessionToken Receive code 2001`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        val expectedResponse = SessionTokenResponse(
            code = "CUS_KYC_2001",
            description = "Unable to process",
            data = null
        )

        whenever(service.getSessionToken(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id", testScheduler, testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(service).getSessionToken(any())
        Assert.assertEquals(
            false,
            resultSuccess
        )
        Assert.assertEquals(
            "Unable to process",
            resultDescription
        )
    }

    @Test
    fun `getSessionToken Receive code 2002`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        val expectedResponse = SessionTokenResponse(
            code = "CUS_KYC_2002",
            description = "Unable to process",
            data = null
        )

        whenever(service.getSessionToken(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id", testScheduler, testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(service).getSessionToken(any())
        Assert.assertEquals(
            false,
            resultSuccess
        )
        Assert.assertEquals(
            "Unable to process",
            resultDescription
        )
    }

    @Test
    fun `getSessionToken when there is already session token`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        whenever(pref.ekycToken).thenReturn("***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id", testScheduler, testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(service, Mockito.times(0)).getSessionToken(any())
        Assert.assertEquals(
            true,
            resultSuccess
        )
        Assert.assertEquals(
            "Success",
            resultDescription
        )
    }

    @Test
    fun `getSessionToken when there is already session token but different session`() {
        var resultSuccess = false
        var resultDescription = ""
        //given
        val expectedResponse = SessionTokenResponse(
            code = "CUS_KYC_2002",
            description = "Unable to process",
            data = null
        )

        whenever(service.getSessionToken(any())).thenReturn(Single.just(expectedResponse))
        whenever(
            context.getSharedPreferences(
                "EkycSharedPreferences",
                Context.MODE_PRIVATE
            )
        ).thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.getString("installationId", "")).thenReturn("not empty")
        Config.x_session_id = "different"

        //when
        HandleCallback.initCallback = object : EkycUtilities.InitCallback {
            override fun onSuccess(
                success: Boolean,
                description: String,
                ekycToken: String?
            ) {
                resultSuccess = success
                resultDescription = description
            }
        }
        EkycGetSessionUseCase.execute(context, service, pref, "session id", testScheduler, testScheduler)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(service).getSessionToken(any())
        Assert.assertEquals(
            false,
            resultSuccess
        )
        Assert.assertEquals(
            "Unable to process",
            resultDescription
        )
    }
}