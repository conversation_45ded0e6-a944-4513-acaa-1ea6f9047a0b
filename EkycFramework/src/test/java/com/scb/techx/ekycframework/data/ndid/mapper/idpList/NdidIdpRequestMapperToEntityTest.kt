package com.scb.techx.ekycframework.data.ndid.mapper.idpList

import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidIdpRequestMapperToEntity
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequest
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class NdidIdpRequestMapperToEntityTest {
    val mapper = NdidIdpRequestMapperToEntity()

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    private val ndidIdpRequestMock = NdidIdpRequest(
        identifierType = "identifierType",
        identifierValue = "identifierValue",
        serviceId = "serviceId"
    )

    @Test
    fun `when convert full ndid idp reqiest entity`() {
        val result = mapper.mapToEntity(ndidIdpRequestMock)

        Assert.assertEquals(ndidIdpRequestMock.serviceId, result.serviceId)
        Assert.assertEquals(ndidIdpRequestMock.identifierType, result.identifierType)
        Assert.assertEquals(ndidIdpRequestMock.identifierValue, result.identifierValue)
    }
}