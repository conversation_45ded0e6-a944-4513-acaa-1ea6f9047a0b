package com.scb.techx.ekycframework.util

import androidx.test.espresso.matcher.ViewMatchers.assertThat
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DOcrData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.FormatOcrFieldUseCase
import org.hamcrest.CoreMatchers.instanceOf
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations


class OcrFormatterUtilTest {
    private val ocrFormatterUtil: FormatOcrFieldUseCase = FormatOcrFieldUseCase()
    val mockUserConfirmedValueDisplay =
        com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay(
            "nationalId",
            "titleTh",
            "firstNameTh",
            "middleNameTh",
            "lastNameTh",
            "titleEn",
            "firstNameEn",
            "middleNameEn",
            "lastNameEn",
            "12AUG2012",
            "11DEC2012",
            "09JAN2014",
            "laserId"
        )
    private val mockOcrDataWith0 = Match3D2DOcrData(
        "nationalId",
        "titleTh",
        "titleEn",
        "firstNameTh",
        "middleNameTh",
        "lastNameTh",
        "firstNameEn",
        "middleNameEn",
        "lastNameEn",
        "12AUG2012",
        "0",
        "11DEC2012",
        "09JAN2014",
        "laserId"
    )
    private val mockOcrDataWith1 = Match3D2DOcrData(
        "nationalId",
        "titleTh",
        "titleEn",
        "firstNameTh",
        "middleNameTh",
        "lastNameTh",
        "firstNameEn",
        "middleNameEn",
        "lastNameEn",
        "01AUG2012",
        "1",
        "11DEC2012",
        "09JAN2014",
        "laserId"
    )
    private val mockOcrDataWith2 = Match3D2DOcrData(
        "nationalId",
        "titleTh",
        "titleEn",
        "firstNameTh",
        "middleNameTh",
        "lastNameTh",
        "firstNameEn",
        "middleNameEn",
        "lastNameEn",
        "01JAN2012",
        "2",
        "11DEC2012",
        "09JAN2014",
        "laserId"
    )
    private val mockOcrDataWithNull = Match3D2DOcrData(
        "nationalId",
        "titleTh",
        "titleEn",
        "firstNameTh",
        "middleNameTh",
        "lastNameTh",
        "firstNameEn",
        "middleNameEn",
        "lastNameEn",
        "01JAN2012",
        null,
        "11DEC2012",
        "09JAN2014",
        "laserId"
    )

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Test
    fun `remove space and special character`() {
        // given
        val testString = "ABC ,. - DEF"

        // when
        val result = ocrFormatterUtil.removeSpaceAndSpecialCharacter(testString)

        // then
        Assert.assertEquals("ABCDEF", result)
    }

    @Test
    fun `format date for not edit text`() {
        // given
        val testStringFull = "18 AUG. 2012"
        val testStringMonthYear = "AUG. 2012"
        val testStringYear = "2012"
        val testStringNull = null
        val testStringDayWrong = "UTDEC2012"
        val testStringMonthWrong = "12 GUR. 2012"
        val testStringAllWrong = "qwreasdfad"

        // when
        val resultFull = ocrFormatterUtil.formatDate(testStringFull, false)
        val resultMonthYear = ocrFormatterUtil.formatDate(testStringMonthYear, false)
        val resultYear = ocrFormatterUtil.formatDate(testStringYear, false)
        val resultNull = ocrFormatterUtil.formatDate(testStringNull, false)
        val resultDayWrong: String = ocrFormatterUtil.formatDate(testStringDayWrong, false)
        val resultMonthWrong: String = ocrFormatterUtil.formatDate(testStringMonthWrong, false)
        val resultAllWrong: String = ocrFormatterUtil.formatDate(testStringAllWrong, false)

        // then
        Assert.assertEquals("18/08/2012", resultFull)
        Assert.assertEquals("-/08/2012", resultMonthYear)
        Assert.assertEquals("-/-/2012", resultYear)
        Assert.assertEquals("", resultNull)
        Assert.assertEquals("", resultDayWrong)
        Assert.assertEquals("", resultMonthWrong)
        Assert.assertEquals("", resultAllWrong)
    }

    @Test
    fun `format date for edit text`() {
        // given
        val testStringFull = "18 AUG. 2012"
        val testStringMonthYear = "AUG. 2012"
        val testStringYear = "2012"
        val testStringNull = null
        val testStringDayWrong = "UTDEC2012"
        val testStringMonthWrong = "12 GUR. 2012"
        val testStringAllWrong = "qwreasdfad"

        // when
        val resultFull = ocrFormatterUtil.formatDate(testStringFull, true)
        val resultMonthYear = ocrFormatterUtil.formatDate(testStringMonthYear, true)
        val resultYear = ocrFormatterUtil.formatDate(testStringYear, true)
        val resultNull = ocrFormatterUtil.formatDate(testStringNull, true)
        val resultDayWrong: String = ocrFormatterUtil.formatDate(testStringDayWrong, true)
        val resultMonthWrong: String = ocrFormatterUtil.formatDate(testStringMonthWrong, true)
        val resultAllWrong: String = ocrFormatterUtil.formatDate(testStringAllWrong, true)

        // then
        Assert.assertEquals("18/08/2012", resultFull)
        Assert.assertEquals("-/08/2012", resultMonthYear)
        Assert.assertEquals("-/-/2012", resultYear)
        Assert.assertEquals("", resultNull)
        Assert.assertEquals("/12/2012", resultDayWrong)
        Assert.assertEquals("//2012", resultMonthWrong)
        Assert.assertEquals("//", resultAllWrong)
    }

    @Test
    fun formatExpirationDate() {
        //when
        val resultFull: String = ocrFormatterUtil.formatExpirationDate("12 DEC. 2012")
        val resultLifeLong: String = ocrFormatterUtil.formatExpirationDate("LIFELONG")
        val resultWrong: String = ocrFormatterUtil.formatExpirationDate("DEC. 2012")

        //then
        Assert.assertEquals("12/12/2012", resultFull)
        Assert.assertEquals("LIFELONG", resultLifeLong)
        Assert.assertEquals("", resultWrong)
    }

    @Test
    fun `convert ocr data to UserConfirmedValue without null and flag is 0`() {
        // when
        val result = ocrFormatterUtil.userConfirmValueMapFromOcrData(mockOcrDataWith0)

        // then
        assertThat(result, instanceOf(UserConfirmedValue::class.java))
        Assert.assertEquals("nationalId", result.nationalId)
        Assert.assertEquals("titleTh", result.titleTh)
        Assert.assertEquals("firstNameTh", result.firstNameTh)
        Assert.assertEquals("middleNameTh", result.middleNameTh)
        Assert.assertEquals("lastNameTh", result.lastNameTh)
        Assert.assertEquals("titleEn", result.titleEn)
        Assert.assertEquals("firstNameEn", result.firstNameEn)
        Assert.assertEquals("middleNameEn", result.middleNameEn)
        Assert.assertEquals("lastNameEn", result.lastNameEn)
        Assert.assertEquals("12/08/2012", result.dateOfBirth)
        Assert.assertEquals("11/12/2012", result.dateOfIssue)
        Assert.assertEquals("09/01/2014", result.dateOfExpiry)
    }

    @Test
    fun `convert ocr data to UserConfirmedValue without null and flag is 1`() {
        // when
        val result = ocrFormatterUtil.userConfirmValueMapFromOcrData(mockOcrDataWith1)

        // then
        assertThat(result, instanceOf(UserConfirmedValue::class.java))
        Assert.assertEquals("nationalId", result.nationalId)
        Assert.assertEquals("titleTh", result.titleTh)
        Assert.assertEquals("firstNameTh", result.firstNameTh)
        Assert.assertEquals("middleNameTh", result.middleNameTh)
        Assert.assertEquals("lastNameTh", result.lastNameTh)
        Assert.assertEquals("titleEn", result.titleEn)
        Assert.assertEquals("firstNameEn", result.firstNameEn)
        Assert.assertEquals("middleNameEn", result.middleNameEn)
        Assert.assertEquals("lastNameEn", result.lastNameEn)
        Assert.assertEquals("-/08/2012", result.dateOfBirth)
        Assert.assertEquals("11/12/2012", result.dateOfIssue)
        Assert.assertEquals("09/01/2014", result.dateOfExpiry)
    }

    @Test
    fun `convert ocr data to UserConfirmedValue without null and flag is 2`() {
        // when
        val result = ocrFormatterUtil.userConfirmValueMapFromOcrData(mockOcrDataWith2)

        // then
        assertThat(result, instanceOf(UserConfirmedValue::class.java))
        Assert.assertEquals("nationalId", result.nationalId)
        Assert.assertEquals("titleTh", result.titleTh)
        Assert.assertEquals("firstNameTh", result.firstNameTh)
        Assert.assertEquals("middleNameTh", result.middleNameTh)
        Assert.assertEquals("lastNameTh", result.lastNameTh)
        Assert.assertEquals("titleEn", result.titleEn)
        Assert.assertEquals("firstNameEn", result.firstNameEn)
        Assert.assertEquals("middleNameEn", result.middleNameEn)
        Assert.assertEquals("lastNameEn", result.lastNameEn)
        Assert.assertEquals("-/-/2012", result.dateOfBirth)
        Assert.assertEquals("11/12/2012", result.dateOfIssue)
        Assert.assertEquals("09/01/2014", result.dateOfExpiry)
    }

    @Test
    fun `convert ocr data to UserConfirmedValue without null and flag is other`() {
        // when
        val result = ocrFormatterUtil.userConfirmValueMapFromOcrData(mockOcrDataWithNull)

        // then
        assertThat(result, instanceOf(UserConfirmedValue::class.java))
        Assert.assertEquals("nationalId", result.nationalId)
        Assert.assertEquals("titleTh", result.titleTh)
        Assert.assertEquals("firstNameTh", result.firstNameTh)
        Assert.assertEquals("middleNameTh", result.middleNameTh)
        Assert.assertEquals("lastNameTh", result.lastNameTh)
        Assert.assertEquals("titleEn", result.titleEn)
        Assert.assertEquals("firstNameEn", result.firstNameEn)
        Assert.assertEquals("middleNameEn", result.middleNameEn)
        Assert.assertEquals("lastNameEn", result.lastNameEn)
        Assert.assertEquals("", result.dateOfBirth)
        Assert.assertEquals("11/12/2012", result.dateOfIssue)
        Assert.assertEquals("09/01/2014", result.dateOfExpiry)
    }

    @Test
    fun formatNationalId() {
        //when
        val resultFull: String = ocrFormatterUtil.formatNationalId("1234567890123")
        val resulNotFull: String = ocrFormatterUtil.formatNationalId("123456789")


        //then
        Assert.assertEquals("1-2345-67890-12-3", resultFull)
        Assert.assertEquals("1-2345-6789", resulNotFull)
    }

    @Test
    fun formatLaserId() {
        //when
        val resultFull: String = ocrFormatterUtil.formatLaserId("123456789012")
        val resulNotFull: String = ocrFormatterUtil.formatLaserId("123456789")


        //then
        Assert.assertEquals("123-4567890-12", resultFull)
        Assert.assertEquals("123-456789", resulNotFull)
    }
}