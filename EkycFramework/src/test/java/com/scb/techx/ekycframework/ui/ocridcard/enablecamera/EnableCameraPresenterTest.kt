package com.scb.techx.ekycframework.ui.ocridcard.enablecamera

import com.nhaarman.mockito_kotlin.verify
import com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.presenter.EnableCameraContract
import com.scb.techx.ekycframework.ui.ocridcard.enablecamerafragment.presenter.EnableCameraPresenter
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations

class EnableCameraPresenterTest {

    private lateinit var presenter: EnableCameraPresenter

    @Mock
    lateinit var view: EnableCameraContract.View

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
        presenter = EnableCameraPresenter(view)
    }

    @Test
    fun `when setActionButton parameter is true`() {
        //when
        presenter.setActionButton(true)

        //then
        verify(view).navigateToSettings()
    }

    @Test
    fun `when setActionButton parameter is false`() {
        //when
        presenter.setActionButton(false)

        //then
        verify(view).requestCameraPermissionDialog()
    }

    @Test
    fun `when setActionPermissionDialog isCameraGranted is true`() {
        //when
        presenter.setActionPermissionDialog(isCameraGranted = true, isNeverAskAgainSelected = true)

        //then
        verify(view).navigateToScanFrontIdCard()
    }

    @Test
    fun `when setActionPermissionDialog isCameraGranted is false and isNeverAskAgain is true`() {
        //when
        presenter.setActionPermissionDialog(isCameraGranted = false, isNeverAskAgainSelected = true)

        //then
        verify(view).setShouldShowStatus()
        verify(view).setEnableButton()
    }

    @Test
    fun `when setActionPermissionDialog isCameraGranted is false and isNeverAskAgain is false`() {
        //when
        presenter.setActionPermissionDialog(isCameraGranted = false, isNeverAskAgainSelected = false)

        //then
        verify(view).setShouldShowStatus()
    }
}