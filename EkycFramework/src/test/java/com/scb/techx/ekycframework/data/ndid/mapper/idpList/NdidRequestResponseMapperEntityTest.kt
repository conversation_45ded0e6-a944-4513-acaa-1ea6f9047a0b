package com.scb.techx.ekycframework.data.ndid.mapper.idpList

import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidRequestResponseMapperEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.IdpEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidDataEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidRequestDataEntity
import com.scb.techx.ekycframework.data.ndid.model.idplist.NdidRequestResponseEntity
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class NdidRequestResponseMapperEntityTest {
    val mapper = NdidRequestResponseMapperEntity()

    private val ndidRequestResponseMock = NdidRequestResponseEntity(
        code = "code",
        description = "description",
        data = NdidRequestDataEntity(
            sessionId = "sessionId",
            referenceId = "referenceId",
            status = "status",
            expireTime = 60,
            idp = IdpEntity(
                nodeId = "nodeId",
                industryCode = "industryCode",
                companyCode = "companyCode",
                shortName = "shortName",
                marketingNameTh = "marketingNameTh",
                marketingNameEn = "marketingNameEn",
                smallIconPath = "smallIconPath",
                mediumIconPath = "mediumIconPath",
                largeIconPath = "largeIconPath",
                deepLinkAndroid = "deepLinkAndroid",
                deepLinkHuawei = "deepLinkHuawei",
                deepLinkIos = "deepLinkIos"
            ),
            ndidData = NdidDataEntity(
                requestId = "requestId"
            )
        )
    )

    private val ndidRequestResponseNullDataMock = NdidRequestResponseEntity(
        code = "code",
        description = "description",
        data = null
    )

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Test
    fun `when convert full ndid request response`() {
        val result = mapper.mapFromEntity(ndidRequestResponseMock)

        Assert.assertEquals(ndidRequestResponseMock.code, result.code)
        Assert.assertEquals(ndidRequestResponseMock.description, result.description)
        Assert.assertEquals(ndidRequestResponseMock.data?.sessionId, result.data?.sessionId)
        Assert.assertEquals(ndidRequestResponseMock.data?.referenceId, result.data?.referenceId)
        Assert.assertEquals(ndidRequestResponseMock.data?.status, result.data?.status)
        Assert.assertEquals(ndidRequestResponseMock.data?.expireTime, result.data?.expireTime)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.companyCode, result.data?.idp?.companyCode)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.shortName, result.data?.idp?.shortName)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.deepLinkAndroid, result.data?.idp?.deepLinkAndroid)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.deepLinkIos, result.data?.idp?.deepLinkIos)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.deepLinkHuawei, result.data?.idp?.deepLinkHuawei)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.industryCode, result.data?.idp?.industryCode)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.largeIconPath, result.data?.idp?.largeIconPath)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.marketingNameEn, result.data?.idp?.marketingNameEn)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.marketingNameTh, result.data?.idp?.marketingNameTh)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.mediumIconPath, result.data?.idp?.mediumIconPath)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.nodeId, result.data?.idp?.nodeId)
        Assert.assertEquals(ndidRequestResponseMock.data?.idp?.smallIconPath, result.data?.idp?.smallIconPath)
        Assert.assertEquals(ndidRequestResponseMock.data?.ndidData?.requestId, result.data?.ndidData?.requestId)
    }

    @Test
    fun `when convert full ndid request response with null`() {
        val result = mapper.mapFromEntity(ndidRequestResponseNullDataMock)

        Assert.assertEquals(ndidRequestResponseMock.code, result.code)
        Assert.assertEquals(ndidRequestResponseMock.description, result.description)
        Assert.assertNull(result.data)
    }
}