package com.scb.techx.ekycframework.data.facetec.mapper

import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanResponse
import com.scb.techx.ekycframework.data.facetec.mapper.response.GetMatch2D3DResponseMapperEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Match3D2DIdScanDataEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Match3D2DIdScanResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Match3D2DOcrDataEntity
import org.junit.Assert
import org.junit.Test

class GetMatch2D3DResponseMapperEntityTest {
    private val mapper: GetMatch2D3DResponseMapperEntity = GetMatch2D3DResponseMapperEntity()

    private fun checkOcrFull(
        entity: Match3D2DIdScanResponseEntity,
        result: Match3D2DIdScanResponse
    ) {
        Assert.assertEquals(entity.data?.ocrData?.titleEn, result.data?.ocrData?.titleEn)
        Assert.assertEquals(entity.data?.ocrData?.titleTh, result.data?.ocrData?.titleTh)
        Assert.assertEquals(entity.data?.ocrData?.firstNameEn, result.data?.ocrData?.firstNameEn)
        Assert.assertEquals(entity.data?.ocrData?.middleNameEn, result.data?.ocrData?.middleNameEn)
        Assert.assertEquals(entity.data?.ocrData?.lastNameEn, result.data?.ocrData?.lastNameEn)
        Assert.assertEquals(entity.data?.ocrData?.firstNameTh, result.data?.ocrData?.firstNameTh)
        Assert.assertEquals(entity.data?.ocrData?.middleNameTh, result.data?.ocrData?.middleNameTh)
        Assert.assertEquals(entity.data?.ocrData?.lastNameTh, result.data?.ocrData?.lastNameTh)
        Assert.assertEquals(entity.data?.ocrData?.dateOfBirth, result.data?.ocrData?.dateOfBirth)
        Assert.assertEquals(entity.data?.ocrData?.dateOfExpiry, result.data?.ocrData?.dateOfExpiry)
        Assert.assertEquals(
            entity.data?.ocrData?.dateOfBirthFlag,
            result.data?.ocrData?.dateOfBirthFlag
        )
        Assert.assertEquals(entity.data?.ocrData?.dateOfIssue, result.data?.ocrData?.dateOfIssue)
        Assert.assertEquals(entity.data?.ocrData?.laserId, result.data?.ocrData?.laserId)
        Assert.assertEquals(entity.data?.ocrData?.nationalId, result.data?.ocrData?.nationalId)
    }

    private fun checkOcrNull(result: Match3D2DIdScanResponse) {
        Assert.assertEquals(null, result.data?.ocrData?.titleEn)
        Assert.assertEquals(null, result.data?.ocrData?.titleTh)
        Assert.assertEquals(null, result.data?.ocrData?.firstNameEn)
        Assert.assertEquals(null, result.data?.ocrData?.middleNameEn)
        Assert.assertEquals(null, result.data?.ocrData?.lastNameEn)
        Assert.assertEquals(null, result.data?.ocrData?.firstNameTh)
        Assert.assertEquals(null, result.data?.ocrData?.middleNameTh)
        Assert.assertEquals(null, result.data?.ocrData?.lastNameTh)
        Assert.assertEquals(null, result.data?.ocrData?.dateOfBirth)
        Assert.assertEquals(null, result.data?.ocrData?.dateOfExpiry)
        Assert.assertEquals(null, result.data?.ocrData?.dateOfBirthFlag)
        Assert.assertEquals(null, result.data?.ocrData?.dateOfIssue)
        Assert.assertEquals(null, result.data?.ocrData?.laserId)
        Assert.assertEquals(null, result.data?.ocrData?.nationalId)
    }

    @Test
    fun `when convert match3D2DIdScanResponseEntity full`() {
        //given
        val match3D2DIdScanResponseEntityFull = Match3D2DIdScanResponseEntity(
            code = "code",
            description = "description",
            data = Match3D2DIdScanDataEntity(
                scanResultBlob = "scanResultBlob",
                wasProcessed = true,
                documentData = "documentData",
                ocrData = Match3D2DOcrDataEntity(
                    nationalId = "1234567890123",
                    titleEn = "MR.",
                    titleTh = "นาย",
                    firstNameEn = "ABC",
                    firstNameTh = "กขค",
                    middleNameEn = "",
                    middleNameTh = "",
                    lastNameEn = "DEF",
                    lastNameTh = "งจฉ",
                    dateOfBirth = "01AUG2022",
                    dateOfBirthFlag = "0",
                    dateOfIssue = "01AUG2022",
                    dateOfExpiry = "01AUG2022",
                    laserId = "123456789012"
                )
            )
        )

        //when
        val result = mapper.mapFromEntity(match3D2DIdScanResponseEntityFull)

        //then
        Assert.assertEquals(match3D2DIdScanResponseEntityFull.code, result.code)
        Assert.assertEquals(match3D2DIdScanResponseEntityFull.description, result.description)
        Assert.assertEquals(
            match3D2DIdScanResponseEntityFull.data?.scanResultBlob,
            result.data?.scanResultBlob
        )
        Assert.assertEquals(
            match3D2DIdScanResponseEntityFull.data?.wasProcessed,
            result.data?.wasProcessed,
        )
        Assert.assertEquals(
            match3D2DIdScanResponseEntityFull.data?.documentData,
            result.data?.documentData,
        )
        checkOcrFull(match3D2DIdScanResponseEntityFull, result)
    }

    @Test
    fun `when convert match3D2DIdScanResponseEntity null`() {
        //given
        val match3D2DIdScanResponseEntityNull = Match3D2DIdScanResponseEntity(
            code = "code",
            description = "description",
            data = null
        )

        //when
        val result = mapper.mapFromEntity(match3D2DIdScanResponseEntityNull)

        //then
        Assert.assertEquals(match3D2DIdScanResponseEntityNull.code, result.code)
        Assert.assertEquals(match3D2DIdScanResponseEntityNull.description, result.description)
        Assert.assertEquals(
            null,
            result.data?.scanResultBlob
        )
        Assert.assertEquals(
            null,
            result.data?.wasProcessed,
        )
        Assert.assertEquals(
            null,
            result.data?.documentData,
        )
        Assert.assertEquals(
            null,
            result.data,
        )
        checkOcrNull(result)
    }

    @Test
    fun `when convert match3D2DIdScanResponseEntity only null at ocr data`() {
        //given
        val match3D2DIdScanResponseEntityNullOcrData = Match3D2DIdScanResponseEntity(
            code = "code",
            description = "description",
            data = Match3D2DIdScanDataEntity(
                scanResultBlob = "scanResultBlob",
                wasProcessed = true,
                documentData = "documentData",
                ocrData = null
            )
        )

        //when
        val result = mapper.mapFromEntity(match3D2DIdScanResponseEntityNullOcrData)

        //then
        Assert.assertEquals(match3D2DIdScanResponseEntityNullOcrData.code, result.code)
        Assert.assertEquals(match3D2DIdScanResponseEntityNullOcrData.description, result.description)
        Assert.assertEquals(
            "scanResultBlob",
            result.data?.scanResultBlob
        )
        Assert.assertEquals(
            true,
            result.data?.wasProcessed,
        )
        Assert.assertEquals(
            "documentData",
            result.data?.documentData,
        )
        checkOcrNull(result)
    }
}