package com.scb.techx.ekycframework.data.facetec.mapper

import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.data.facetec.mapper.request.GetConfirmationInfoRequestMapperToEntity
import org.junit.Assert
import org.junit.Test

class GetConfirmationInfoRequestMapperToEntityTest {
    private val mapper: GetConfirmationInfoRequestMapperToEntity = GetConfirmationInfoRequestMapperToEntity()

    @Test
    fun `when convert confirmationInfoRequest`() {
        //given
        val confirmationInfoRequest: ConfirmationInfoRequest = ConfirmationInfoRequest(
            checkExpiredIdCard = false,
            checkDopa = true,
            data = ConfirmationInfoRequest.Data(
                nationalId = "nationalId",
                titleTh = "titleTh",
                titleEn = "titleEn",
                firstNameTh = "firstNameTh",
                firstNameEn = "firstNameEn",
                middleNameTh = "middleNameTh",
                middleNameEn = "middleNameEn",
                lastNameTh = "lastNameTh",
                lastNameEn = "lastNameEn",
                dateOfBirth = "dateOfBirth",
                dateOfIssue = "dateOfIssue",
                dateOfExpiry = "dateOfExpiry",
                laserId = "laserId"
            )
        )

        //when
        val result = mapper.mapToEntity(confirmationInfoRequest)

        //then
        Assert.assertEquals(confirmationInfoRequest.checkExpiredIdCard, result.checkExpiredIdCard)
        Assert.assertEquals(confirmationInfoRequest.checkDopa, result.checkDopa)
        Assert.assertEquals(confirmationInfoRequest.data.dateOfBirth, result.data.dateOfBirth)
        Assert.assertEquals(confirmationInfoRequest.data.dateOfExpiry, result.data.dateOfExpiry)
        Assert.assertEquals(confirmationInfoRequest.data.dateOfIssue, result.data.dateOfIssue)
        Assert.assertEquals(confirmationInfoRequest.data.laserId, result.data.laserId)
        Assert.assertEquals(confirmationInfoRequest.data.firstNameEn, result.data.firstNameEn)
        Assert.assertEquals(confirmationInfoRequest.data.firstNameTh, result.data.firstNameTh)
        Assert.assertEquals(confirmationInfoRequest.data.lastNameEn, result.data.lastNameEn)
        Assert.assertEquals(confirmationInfoRequest.data.lastNameTh, result.data.lastNameTh)
        Assert.assertEquals(confirmationInfoRequest.data.middleNameEn, result.data.middleNameEn)
        Assert.assertEquals(confirmationInfoRequest.data.middleNameTh, result.data.middleNameTh)
        Assert.assertEquals(confirmationInfoRequest.data.nationalId, result.data.nationalId)
        Assert.assertEquals(confirmationInfoRequest.data.titleEn, result.data.titleEn)
        Assert.assertEquals(confirmationInfoRequest.data.titleTh, result.data.titleTh)
    }
}