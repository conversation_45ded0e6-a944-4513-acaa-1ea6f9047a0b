package com.scb.techx.ekycframework.ui.processor

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Resources
import com.nhaarman.mockito_kotlin.any
import com.nhaarman.mockito_kotlin.verify
import com.nhaarman.mockito_kotlin.eq
import com.nhaarman.mockito_kotlin.doNothing
import com.nhaarman.mockito_kotlin.anyOrNull
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DIdScanData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Match3D2DOcrData
import com.scb.techx.ekycframework.ui.processor.photomatchid.presenter.PhotoMatchIDProcessorContract
import com.scb.techx.ekycframework.ui.processor.photomatchid.presenter.PhotoMatchIDProcessorPresenter
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import okhttp3.internal.http2.ErrorCode
import okhttp3.internal.http2.StreamResetException
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.mockito.junit.MockitoJUnitRunner
import java.util.concurrent.TimeUnit

@RunWith(MockitoJUnitRunner::class)
class PhotoMatchIDProcessorPresenterTest {
    lateinit var presenter: PhotoMatchIDProcessorPresenter
    lateinit var pref: EkycPreferenceUtil

    @Mock
    lateinit var editor: SharedPreferences.Editor

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var resources: Resources

    @Mock
    lateinit var processor: PhotoMatchIDProcessorContract.Processor

    @Mock
    lateinit var repository: FaceTecRepository

    lateinit var testScheduler: TestScheduler

    private fun mockForGetEnableReview(whatToReturn: Boolean) {
        Mockito.`when`(sharedPreferences.getBoolean("enableConfirmInfo", true)).thenReturn(whatToReturn)
    }


    private fun mockForClearPreference() {
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        Mockito.`when`(sharedPreferences.edit()).thenReturn(editor)
        Mockito.`when`(editor.putString(any(), any())).thenReturn(editor)
        Mockito.`when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()
    }

    private fun validateClearPreference() {
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Before
    fun setup() {

        MockitoAnnotations.initMocks(this)

        pref = EkycPreferenceUtil(context)
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"

        testScheduler = TestScheduler()

        presenter = PhotoMatchIDProcessorPresenter(
            processor, pref, testScheduler, testScheduler, repository
        )
    }

    @Test
    fun `when call face scan api and get 1000`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = true
            )
        )

        Mockito.`when`(repository.getEnrollment3D(
            any(),
            any()
        )).thenReturn(Single.just(response))

        //when
        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(processor).proceedToNextStepFaceScan(
            anyOrNull(),
            eq("mock")
        )
    }

    @Test
    fun `when call face scan api and get 1000 but was process is false`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-1000",
            description = "Some error",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(repository.getEnrollment3D(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).failCallbackWithMessage(
            eq("Some error")
        )
        verify(processor).faceTecCancelFaceScan(
            anyOrNull()
        )
    }

    @Test
    fun `KYC-1026 when call face scan api and get 2003`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-2003",
            description = "Data not found",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(repository.getEnrollment3D(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Data not found"
        )
    }

    @Test
    fun `KYC-1027 when call face scan api and get 9001`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-9001",
            description = "Timeout",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(repository.getEnrollment3D(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Timeout"
        )
    }

    @Test
    fun `KYC-1028 when call face scan api and get 9002`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-9002",
            description = "Connection error",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(repository.getEnrollment3D(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Connection error"
        )
    }

    @Test
    fun `KYC-1029 when call face scan api and get 1999`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-1999",
            description = "Unable to process",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(repository.getEnrollment3D(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Unable to process"
        )
    }

    @Test
    fun `when call front id scan api and get 1000`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = true,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanFront(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanFrontRequest(
                idScan = "Mock",
                idScanFrontImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(processor).proceedToNextStepIDScan(anyOrNull(),eq("mock"))
    }

    @Test
    fun `when call front id scan api and get 1000 but was process is false`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-1000",
            description = "Mock Fail",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanFront(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanFrontRequest(
                idScan = "Mock",
                idScanFrontImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Mock Fail"
        )
    }

    @Test
    fun `KYC-1030 when call front id scan api and get 2003`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-2003",
            description = "Data not found",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanFront(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanFrontRequest(
                idScan = "Mock",
                idScanFrontImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Data not found"
        )
    }

    @Test
    fun `KYC-1031 when call front id scan api and get 9001`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-9001",
            description = "Timeout",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanFront(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanFrontRequest(
                idScan = "Mock",
                idScanFrontImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Timeout"
        )
    }

    @Test
    fun `KYC-1032 when call front id scan and get 9002`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-9002",
            description = "Connection error",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanFront(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanFrontRequest(
                idScan = "Mock",
                idScanFrontImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Connection error"
        )
    }

    @Test
    fun `KYC-1033 when call front id scan api and get 1999`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-1999",
            description = "Unable to process",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanFront(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanFrontRequest(
                idScan = "Mock",
                idScanFrontImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Unable to process"
        )
    }

    @Test
    fun `when call back id scan api and get 1000 and enableReview is false`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = Match3D2DIdScanData(
                documentData = "{\"scannedValues\":{\"groups\":[{\"fields\":[{\"fieldKey\":\"firstName\",\"uiFieldType\":\"Alphabetic\",\"value\":\"MR. NATTAPAT\"},{\"fieldKey\":\"lastName\",\"uiFieldType\":\"Alphabetic\",\"value\":\"SAENGSARARAN\"},{\"fieldKey\":\"dateOfBirth\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"11 OCT. 1998\"},{\"fieldKey\":\"fullName\",\"uiFieldType\":\"Alphabetic\",\"value\":\"นณาย ณฐภหทร หห  สงสาราญ\"}],\"groupFriendlyName\":\"Your Information\",\"groupKey\":\"userInfo\"},{\"fields\":[{\"fieldKey\":\"idNumber\",\"uiFieldType\":\"Numeric\",\"value\":\"1104500006688\"},{\"fieldKey\":\"dateOfIssue\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"8 JUL. 2022\"},{\"fieldKey\":\"dateOfExpiration\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"OCT. 2030\"}],\"groupFriendlyName\":\"Photo ID Details\",\"groupKey\":\"idInfo\"},{\"fields\":[{\"fieldKey\":\"customField1\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"JC3< 1585404-03\"}],\"groupFriendlyName\":\"Additional Info\",\"groupKey\":\"customFields\"}]},\"templateInfo\":{\"templateName\":\"Thailand - National ID Card - 2015 - Horizontal [Developer Created]\",\"templateType\":\"Government Issued Photo ID\"}}",
                scanResultBlob = "mock",
                wasProcessed = true,
                ocrData = Match3D2DOcrData(
                    nationalId = "1234567890123",
                    titleEn = "MR.",
                    titleTh = "นาย",
                    firstNameEn = "ABC",
                    firstNameTh = "กขค",
                    middleNameEn = "",
                    middleNameTh = "",
                    lastNameEn = "DEF",
                    lastNameTh = "งจฉ",
                    dateOfBirth = "01AUG2022",
                    dateOfBirthFlag = "0",
                    dateOfIssue = "01AUG2022",
                    dateOfExpiry = "01AUG2022",
                    laserId = "123456789012"
                )
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()
        mockForGetEnableReview(false)

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).successCallbackWithMessage(
            eq("Success"),
            any()
        )
        verify(processor).proceedToNextStepIDScan(anyOrNull(),eq("mock"))
    }

    @Test
    fun `when call back id scan api and get 1000 and enableReview is true`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = Match3D2DIdScanData(
                documentData =
                "{\"scannedValues\":{\"groups\":[{\"fields\":[{\"fieldKey\":\"firstName\",\"uiFieldType\":\"Alphabetic\",\"value\":\"MR. NATTAPAT\"},{\"fieldKey\":\"lastName\",\"uiFieldType\":\"Alphabetic\",\"value\":\"SAENGSARARAN\"},{\"fieldKey\":\"dateOfBirth\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"11 OCT. 1998\"},{\"fieldKey\":\"fullName\",\"uiFieldType\":\"Alphabetic\",\"value\":\"นณาย ณฐภหทร หห  สงสาราญ\"}],\"groupFriendlyName\":\"Your Information\",\"groupKey\":\"userInfo\"},{\"fields\":[{\"fieldKey\":\"idNumber\",\"uiFieldType\":\"Numeric\",\"value\":\"1104500006688\"},{\"fieldKey\":\"dateOfIssue\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"8 JUL. 2022\"},{\"fieldKey\":\"dateOfExpiration\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"OCT. 2030\"}],\"groupFriendlyName\":\"Photo ID Details\",\"groupKey\":\"idInfo\"},{\"fields\":[{\"fieldKey\":\"customField1\",\"uiFieldType\":\"Alphanumeric\",\"value\":\"JC3< 1585404-03\"}],\"groupFriendlyName\":\"Additional Info\",\"groupKey\":\"customFields\"}]},\"templateInfo\":{\"templateName\":\"Thailand - National ID Card - 2015 - Horizontal [Developer Created]\",\"templateType\":\"Government Issued Photo ID\"}}",
                scanResultBlob = "mock",
                wasProcessed = true,
                ocrData = Match3D2DOcrData(
                    nationalId = "1234567890123",
                    titleEn = "MR.",
                    titleTh = "นาย",
                    firstNameEn = "ABC",
                    firstNameTh = "กขค",
                    middleNameEn = "",
                    middleNameTh = "",
                    lastNameEn = "DEF",
                    lastNameTh = "งจฉ",
                    dateOfBirth = "01AUG2022",
                    dateOfBirthFlag = "0",
                    dateOfIssue = "01AUG2022",
                    dateOfExpiry = "01AUG2022",
                    laserId = "123456789012"
                )
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()
        mockForGetEnableReview(true)

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).navigateToReviewInformation(any())
        verify(processor).proceedToNextStepIDScan(anyOrNull(),eq("mock"))
    }

    @Test
    fun `when call back id scan api and get 1000 but was process is false`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-1000",
            description = "Mock Fail",
            data = Match3D2DIdScanData(
                documentData =
                "{\"scannedValues\": null,\"templateInfo\":{\"templateName\":\"Thailand - National ID Card - 2015 - Horizontal [Developer Created]\",\"templateType\":\"Government Issued Photo ID\"}}",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = Match3D2DOcrData(
                    nationalId = "1234567890123",
                    titleEn = "MR.",
                    titleTh = "นาย",
                    firstNameEn = "ABC",
                    firstNameTh = "กขค",
                    middleNameEn = "",
                    middleNameTh = "",
                    lastNameEn = "DEF",
                    lastNameTh = "งจฉ",
                    dateOfBirth = "01AUG2022",
                    dateOfBirthFlag = "0",
                    dateOfIssue = "01AUG2022",
                    dateOfExpiry = "01AUG2022",
                    laserId = "123456789012"
                )
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()
        mockForGetEnableReview(true)

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Mock Fail"
        )
    }

    @Test
    fun `KYC-1034 when call back id scan api and get 2003`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-2003",
            description = "Data not found",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = Match3D2DOcrData(
                    nationalId = "1234567890123",
                    titleEn = "MR.",
                    titleTh = "นาย",
                    firstNameEn = "ABC",
                    firstNameTh = "กขค",
                    middleNameEn = "",
                    middleNameTh = "",
                    lastNameEn = "DEF",
                    lastNameTh = "งจฉ",
                    dateOfBirth = "01AUG2022",
                    dateOfBirthFlag = "0",
                    dateOfIssue = "01AUG2022",
                    dateOfExpiry = "01AUG2022",
                    laserId = "123456789012"
                )
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Data not found"
        )
    }

    @Test
    fun `KYC-1035 when call back id scan api and get 2004`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-2004",
            description = "Unable to decrypt image",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Unable to decrypt image"
        )
    }

    @Test
    fun `KYC-1036 when call back id scan api and get 9001`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-9001",
            description = "Timeout",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Timeout"
        )
    }

    @Test
    fun `KYC-1037 when call back id scan api and get 9002`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-9002",
            description = "Connection error",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Connection error"
        )
    }

    @Test
    fun `KYC-1038 when call back it scan api and get 1999`() {
        //given
        val response = Match3D2DIdScanResponse(
            code = "CUS-KYC-1999",
            description = "Unable to process",
            data = Match3D2DIdScanData(
                documentData = "mock",
                scanResultBlob = "mock",
                wasProcessed = false,
                ocrData = null
            )
        )

        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "Mock",
                idScanBackImage = "Mock",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            "Unable to process"
        )
    }

    @Test
    fun `when call facescan api and get string reset exception`() {
        //given
        Mockito.`when`(repository.getEnrollment3D(
            any(),
            any()
        )).thenReturn(
            Single.error(
                StreamResetException(ErrorCode.NO_ERROR)
            )
        )
        mockForClearPreference()

        //when
        presenter.sendAPIForFaceScan(
            faceTecFaceScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                faceScan = "",
                auditTrailImage = "",
                lowQualityAuditTrailImage = "",
                function = ""
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            eq("Connection error")
        )
    }

    @Test
    fun `when call front id scan api and get string reset exception`() {
        //given
        Mockito.`when`(repository.getMatch3D2DIdScanFront(
            any(),
            any()
        )).thenReturn(
            Single.error(
                StreamResetException(ErrorCode.NO_ERROR)
            )
        )
        mockForClearPreference()

        //when
        presenter.sendAPIForFrontIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanFrontRequest(
                idScan = "",
                idScanFrontImage = "",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            eq("Connection error")
        )
    }

    @Test
    fun `when call back id scan api and get string reset exception`() {
        //given
        Mockito.`when`(repository.getMatch3D2DIdScanBack(
            any(),
            any()
        )).thenReturn(
            Single.error(
                StreamResetException(ErrorCode.NO_ERROR)
            )
        )
        mockForClearPreference()

        //when
        presenter.sendAPIForBackIdCardScan(
            faceTecIDScanResultCallback = null,
            authedHeaders = AuthenticatedHeaders(),
            request = Match3D2DIdScanBackRequest(
                idScan = "",
                idScanBackImage = "",
                enableConfirmInfo = true
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelIDScan(anyOrNull())
        verify(processor).failCallbackWithMessage(
            eq("Connection error")
        )
    }
}