package com.scb.techx.ekycframework.data.facetec.mapper

import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanBackRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Match3D2DIdScanFrontRequest
import com.scb.techx.ekycframework.data.facetec.mapper.request.GetMatch2D3DRequestMapperToEntity
import org.junit.Assert
import org.junit.Test

class GetMatch2D3DRequestMapperToEntityTest {
    private val mapper: GetMatch2D3DRequestMapperToEntity = GetMatch2D3DRequestMapperToEntity()

    @Test
    fun `when convert match3D2DIdScanBackRequest`() {
        //given
        val match3D2DIdScanBackRequest: Match3D2DIdScanBackRequest = Match3D2DIdScanBackRequest(
            idScan = "idScan",
            idScanBackImage = "idScanBackImage",
            enableConfirmInfo = true
        )

        //when
        val result = mapper.mapToEntity(match3D2DIdScanBackRequest)

        //then
        Assert.assertEquals(match3D2DIdScanBackRequest.idScan, result.idScan)
        Assert.assertEquals(match3D2DIdScanBackRequest.idScanBackImage, result.idScanBackImage)
        Assert.assertEquals(match3D2DIdScanBackRequest.enableConfirmInfo, result.enableConfirmInfo)
    }

    @Test
    fun `when convert match3D2DIdScanFrontRequest`() {
        //given
        val match3D2DIdScanFrontRequest: Match3D2DIdScanFrontRequest = Match3D2DIdScanFrontRequest(
            idScan = "idScan",
            idScanFrontImage = "idScanFrontImage",
            enableConfirmInfo = true
        )

        //when
        val result = mapper.mapToEntity(match3D2DIdScanFrontRequest)

        //then
        Assert.assertEquals(match3D2DIdScanFrontRequest.idScan, result.idScan)
        Assert.assertEquals(match3D2DIdScanFrontRequest.idScanFrontImage, result.idScanFrontImage)
        Assert.assertEquals(match3D2DIdScanFrontRequest.enableConfirmInfo, result.enableConfirmInfo)
    }
}