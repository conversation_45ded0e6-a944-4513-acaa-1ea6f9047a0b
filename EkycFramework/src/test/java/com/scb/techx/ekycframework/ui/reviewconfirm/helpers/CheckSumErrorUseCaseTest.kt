package com.scb.techx.ekycframework.ui.reviewconfirm.helpers

import com.scb.techx.ekycframework.domain.ocrliveness.usecase.CheckSumErrorUseCase
import org.junit.Assert
import org.junit.Test

class CheckSumErrorUseCaseTest
{
    @Test
    fun `validate check sum id card number when id card is valid`() {
        val result = CheckSumErrorUseCase.isValidIdNumber("1234567890121")
        Assert.assertEquals(true, result)
    }

    @Test
    fun `validate check sum id card number when id card is empty`() {
        val result = CheckSumErrorUseCase.isValidIdNumber("")
        Assert.assertEquals(false, result)
    }

    @Test
    fun `validate check sum id card number when id card is not all number`() {
        val result = CheckSumErrorUseCase.isValidIdNumber("A234567890123")
        Assert.assertEquals(false, result)
    }

    @Test
    fun `validate check sum id card number when id card is not all 13 number`() {
        val result = CheckSumErrorUseCase.isValidIdNumber("12345")
        Assert.assertEquals(false, result)
    }

    @Test
    fun `validate check sum id card number when check sum number is invalid`() {
        val result = CheckSumErrorUseCase.isValidIdNumber("1234567890123")
        Assert.assertEquals(false, result)
    }
}
