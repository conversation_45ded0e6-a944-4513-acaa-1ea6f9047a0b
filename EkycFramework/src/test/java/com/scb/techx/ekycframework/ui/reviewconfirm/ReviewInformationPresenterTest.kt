package com.scb.techx.ekycframework.ui.reviewconfirm

import android.content.Context
import android.content.DialogInterface
import android.content.SharedPreferences
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.res.Resources
import com.nhaarman.mockito_kotlin.whenever
import com.nhaarman.mockito_kotlin.any
import com.nhaarman.mockito_kotlin.verify
import com.nhaarman.mockito_kotlin.eq
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.ConfirmationInfoRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.ConfirmationInfoResponse
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.DopaData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.ui.reviewconfirm.assets.ValidateType
import com.scb.techx.ekycframework.ui.reviewconfirm.helper.ReviewThemeHelper
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycContract
import com.scb.techx.ekycframework.ui.reviewconfirm.presenter.ReviewInformationEkycPresenter
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ocrliveness.usecase.ApplyIDFormatUseCase
import com.scb.techx.ekycframework.ui.reviewconfirm.model.PrefillDisplayedToggle
import com.scb.techx.ekycframework.ui.reviewconfirm.model.UserConfirmedValueDisplay
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.ResponseBody
import okhttp3.internal.http2.ErrorCode
import okhttp3.internal.http2.StreamResetException
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import java.util.concurrent.TimeUnit

class ReviewInformationPresenterTest {
    lateinit var presenter: ReviewInformationEkycPresenter
    lateinit var pref: EkycPreferenceUtil
    lateinit var testScheduler: TestScheduler

    @Mock
    lateinit var view: ReviewInformationEkycContract.View

    @Mock
    lateinit var dialogInterface: DialogInterface

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var repository: FaceTecRepository

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var resources: Resources

    @Mock
    lateinit var themeHelper: ReviewThemeHelper

    @Mock
    lateinit var packageManager: PackageManager

    lateinit var packageInfo: PackageInfo

    lateinit var request: ConfirmationInfoRequest

    lateinit var authenticatedHeaders: AuthenticatedHeaders

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"

        testScheduler = TestScheduler()

        pref = EkycPreferenceUtil(context)

        request = ConfirmationInfoRequest(
            checkExpiredIdCard = true,
            checkDopa = true,
            ConfirmationInfoRequest.Data(
                nationalId = "nationalId",
                titleTh = "titleTh",
                titleEn = "titleEn",
                firstNameTh = "firstNameTh",
                firstNameEn = "firstNameEn",
                middleNameTh = "middleNameTh",
                middleNameEn = "middleNameEn",
                lastNameTh = "lastNameTh",
                lastNameEn = "lastNameEn",
                dateOfBirth = "dateOfBirth",
                dateOfIssue = "dateOfIssue",
                dateOfExpiry = "dateOfExpiry",
                laserId = "laserId"
            )
        )

        authenticatedHeaders = AuthenticatedHeaders()

        packageInfo = PackageInfo()
        packageInfo.versionName = "Mock Version"

        presenter = ReviewInformationEkycPresenter(
            view,
            pref,
            context,
            testScheduler,
            testScheduler,
            repository
        )
    }

    @Test
    fun validateTextEnglish() {
        //when
        val resultNull: ValidateType = presenter.determineValidateTypeTextEnglish("")
        val resultInvalidCharacter: ValidateType = presenter.determineValidateTypeTextEnglish("Th0m4s")
        val resultInvalidDashDot: ValidateType = presenter.determineValidateTypeTextEnglish("Tho-.mas")
        val resultInvalidSpace: ValidateType = presenter.determineValidateTypeTextEnglish("Tho  mas")
        val resultValid: ValidateType = presenter.determineValidateTypeTextEnglish("Tho-mas")
        val resultValidWithSpace: ValidateType = presenter.determineValidateTypeTextEnglish("Tho mas")

        //then
        Assert.assertEquals(ValidateType.NULL_OR_EMPTY, resultNull)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidCharacter)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidDashDot)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidSpace)
        Assert.assertEquals(ValidateType.VALID, resultValid)
        Assert.assertEquals(ValidateType.VALID, resultValidWithSpace)
    }

    @Test
    fun validateTitleEnglish() {
        //when
        val resultNull: ValidateType = presenter.determineValidateTypeTitleEnglish("")
        val resultInvalidCharacter: ValidateType = presenter.determineValidateTypeTitleEnglish("MR.0")
        val resultInvalidDash: ValidateType = presenter.determineValidateTypeTitleEnglish("MR-")
        val resultDoubleSpace: ValidateType = presenter.determineValidateTypeTitleEnglish("M.  L.")
        val resultMultipleDot: ValidateType = presenter.determineValidateTypeTitleEnglish("LT ...SG")
        val resultValid: ValidateType = presenter.determineValidateTypeTitleEnglish("MR.")
        val resultValidWithSpace: ValidateType = presenter.determineValidateTypeTitleEnglish("M. L.")

        //then
        Assert.assertEquals(ValidateType.NULL_OR_EMPTY, resultNull)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidCharacter)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidDash)
        Assert.assertEquals(ValidateType.VALID, resultDoubleSpace)
        Assert.assertEquals(ValidateType.VALID, resultMultipleDot)
        Assert.assertEquals(ValidateType.VALID, resultValid)
        Assert.assertEquals(ValidateType.VALID, resultValidWithSpace)
    }

    @Test
    fun validateTitleThai() {
        //when
        val resultNull: ValidateType = presenter.determineValidateTypeTitleThai("")
        val resultInvalid: ValidateType = presenter.determineValidateTypeTitleThai("MR.")
        val resultMultipleSpace: ValidateType = presenter.determineValidateTypeTitleThai("น.ท.  ร.น.")
        val resultMultipleDot: ValidateType = presenter.determineValidateTypeTitleThai("น.ท. ...ร.น.")
        val resultValid: ValidateType = presenter.determineValidateTypeTitleThai("น.ส.")
        val resultValidWithSpace: ValidateType = presenter.determineValidateTypeTitleThai("น.ท. ร.น.")


        //then
        Assert.assertEquals(ValidateType.NULL_OR_EMPTY, resultNull)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalid)
        Assert.assertEquals(ValidateType.VALID, resultMultipleSpace)
        Assert.assertEquals(ValidateType.VALID, resultMultipleDot)
        Assert.assertEquals(ValidateType.VALID, resultValid)
        Assert.assertEquals(ValidateType.VALID, resultValidWithSpace)
    }

    @Test
    fun validateTextThai() {
        //when
        val resultNull: ValidateType = presenter.determineValidateTypeTextThai("")
        val resultInvalidLanguage: ValidateType = presenter.determineValidateTypeTextThai("กขA")
        val resultInvalidSpace: ValidateType = presenter.determineValidateTypeTextThai("กข  ค")
        val resultInvalid2Character: ValidateType = presenter.determineValidateTypeTextThai("กข.-ค")
        val resultInvalidDoubleDot: ValidateType = presenter.determineValidateTypeTextThai("กข..ค")
        val resultValid: ValidateType = presenter.determineValidateTypeTextThai("กขค")
        val resultValidWithDotDash: ValidateType = presenter.determineValidateTypeTextThai("ก. ขค - กขค")
        val resultValidWithSpace: ValidateType = presenter.determineValidateTypeTextThai("กขค ณ อยุธยา")

        //then
        Assert.assertEquals(ValidateType.NULL_OR_EMPTY, resultNull)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidLanguage)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalid2Character)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidDoubleDot)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidSpace)
        Assert.assertEquals(ValidateType.VALID, resultValidWithDotDash)
        Assert.assertEquals(ValidateType.VALID, resultValidWithSpace)
        Assert.assertEquals(ValidateType.VALID, resultValid)
    }

    @Test
    fun validateNationalId() {
        //when
        val resultNull: ValidateType = presenter.determineValidateTypeNationalId("")
        val resultLength: ValidateType = presenter.determineValidateTypeNationalId("12345w")
        val resultInvalid: ValidateType = presenter.determineValidateTypeNationalId("123w123456789")
        val resultSumInvalid: ValidateType = presenter.determineValidateTypeNationalId("0123456789123")
        val resultValid: ValidateType = presenter.determineValidateTypeNationalId("1234567890121")

        //then
        Assert.assertEquals(ValidateType.NULL_OR_EMPTY, resultNull)
        Assert.assertEquals(ValidateType.LENGTH, resultLength)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalid)
        Assert.assertEquals(ValidateType.INVALID_ID_SUM, resultSumInvalid)
        Assert.assertEquals(ValidateType.VALID, resultValid)

    }

    @Test
    fun validateLaserId() {
        //when
        val resultNull: ValidateType = presenter.determineValidateTypeLaserId("")
        val resultInvalidLetter: ValidateType = presenter.determineValidateTypeLaserId("Aw0123456789")
        val resultInvalidLetter12Less: ValidateType = presenter.determineValidateTypeLaserId("Aw01234")
        val resultInvalidPattern: ValidateType = presenter.determineValidateTypeLaserId("AW01234567BB")
        val resultInvalidPattern12Less: ValidateType = presenter.determineValidateTypeLaserId("AWW")
        val resultInvalidPatternAndWord: ValidateType = presenter.determineValidateTypeLaserId("Aw01234567BB")
        val resultLessThan2Letter: ValidateType = presenter.determineValidateTypeLaserId("A")
        val resultLessThan12Letter: ValidateType = presenter.determineValidateTypeLaserId("AW23")
        val resultValid: ValidateType = presenter.determineValidateTypeLaserId("AW0123456789")

        //then
        Assert.assertEquals(ValidateType.NULL_OR_EMPTY, resultNull)
        Assert.assertEquals(ValidateType.LENGTH, resultLessThan2Letter)
        Assert.assertEquals(ValidateType.LENGTH, resultLessThan12Letter)
        Assert.assertEquals(ValidateType.WRONG_LETTER, resultInvalidLetter12Less)
        Assert.assertEquals(ValidateType.WRONG_LETTER, resultInvalidLetter)
        Assert.assertEquals(ValidateType.WRONG_LETTER, resultInvalidPatternAndWord)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidPattern)
        Assert.assertEquals(ValidateType.VALIDATE_INVALID, resultInvalidPattern12Less)
        Assert.assertEquals(ValidateType.VALID, resultValid)
    }

    @Test
    fun getCurrentFormat() {
        //when
        val resultNotFull = ApplyIDFormatUseCase.getIdCardFormat("123456".toCharArray(), "##-#-####-##")
        val resultFull = ApplyIDFormatUseCase.getIdCardFormat("123456789".toCharArray(), "##-#-####-##")

        //then
        Assert.assertEquals("##-#-###", resultNotFull)
        Assert.assertEquals("##-#-####-##", resultFull)
    }

    @Test
    fun applyIdPattern() {
        //when
        val resultNotAcceptWordTrue = ApplyIDFormatUseCase.applyIdPattern("123456789", "##-#-####-##", true)
        val resultNotAcceptWordFalse = ApplyIDFormatUseCase.applyIdPattern("*********", "##-#-####-##", true)
        val resultAcceptWordTrue = ApplyIDFormatUseCase.applyIdPattern("*********", "##-#-####-##", false)
        val resultAcceptWordFalse = ApplyIDFormatUseCase.applyIdPattern("12=456789", "##-#-####-##", false)

        //then
        Assert.assertEquals("12-3-4567-89", resultNotAcceptWordTrue)
        Assert.assertEquals("*********", resultNotAcceptWordFalse)
        Assert.assertEquals("JA-3-4567-89", resultAcceptWordTrue)
        Assert.assertEquals("12=456789", resultAcceptWordFalse)
    }

    @Test
    fun `test ConfirmationInfoResponse with CUS1000`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "",
                    laserId = ""
                )
            )
        )

        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).confirmationInfoCallback(eq(response))
    }

    @Test
    fun `test ConfirmationInfoResponse with CUS3004`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-3004",
            description = "Success",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "",
                    laserId = ""
                )
            )
        )

        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(R.string.Ekyc_review_expire_within_seven_days)).thenReturn("Wanted Message")

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).setErrorDateOfExpire(
            eq("Wanted Message")
        )
    }

    @Test
    fun `test ConfirmationInfoResponse with CUS7201`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-7201",
            description = "Success",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "",
                    laserId = ""
                )
            )
        )

        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(R.string.Ekyc_dopa_fail)).thenReturn("Wanted Message")

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).showEkycErrorDialog(
            eq("Wanted Message"),
            eq(response)
        )
    }

    @Test
    fun `test ConfirmationInfoResponse with CUS7202`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-7202",
            description = "Success",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "",
                    laserId = ""
                )
            )
        )

        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)
        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(R.string.Ekyc_dopa_limit_reached)).thenReturn("Wanted Message")

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).showEkycErrorDialog(
            eq("Wanted Message"),
            eq(response)
        )
    }

    @Test
    fun `test ConfirmationInfoResponse with other code`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-other",
            description = "Success",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "",
                    laserId = ""
                )
            )
        )

        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).callBackToClient(any())
    }

    @Test
    fun `test ConfirmationInfoResponse throw http exception 401`() {
        //given
        val exception = retrofit2.HttpException(retrofit2.Response.error<Any>(401, ResponseBody.create(
            "plain/text".toMediaTypeOrNull(), ""
        )))
        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    exception
                )
            )
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS);

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).callBackToClient(eq("Invalid token or token expired"))
    }

    @Test
    fun `test ConfirmationInfoResponse throw http exception 504`() {
        //given
        val exception = retrofit2.HttpException(retrofit2.Response.error<Any>(504, ResponseBody.create(
            "plain/text".toMediaTypeOrNull(), ""
        )))
        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    exception
                )
            )
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS);

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).callBackToClient(eq("Timeout"))
    }

    @Test
    fun `test ConfirmationInfoResponse throw other http exception`() {
        //given
        val exception = retrofit2.HttpException(retrofit2.Response.error<Any>(500, ResponseBody.create(
            "plain/text".toMediaTypeOrNull(), ""
        )))
        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    exception
                )
            )
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS);

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).callBackToClient(eq("Unable to process"))
    }

    @Test
    fun `test ConfirmationInfoResponse throw stream reset error`() {
        //given
        val exception = StreamResetException(ErrorCode.CONNECT_ERROR)
        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    exception
                )
            )
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS);

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).callBackToClient(eq("Connection error"))
    }

    @Test
    fun `test ConfirmationInfoResponse throw other exception`() {
        //given
        val exception = java.lang.RuntimeException()
        whenever(repository.getConfirmationInfo(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    exception
                )
            )
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(context.packageManager).thenReturn(packageManager)
        whenever(context.packageName).thenReturn("packageName")
        whenever(packageManager.getPackageInfo("packageName", 0)).thenReturn(packageInfo)

        //when
        presenter.getConfirmationInfo(request, authenticatedHeaders)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS);

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).callBackToClient(eq("Unable to process"))
    }

    @Test
    fun `test when no expiration date box is checked`() {
        //when
        presenter.setUpNoExpirationDateCheckBoxOnCheck(isChecked = true)

        //then
        verify(view).disableExpiration()
    }

    @Test
    fun `test when no expiration date box is not checked`() {
        //when
        presenter.setUpNoExpirationDateCheckBoxOnCheck(isChecked = false)

        //then
        verify(view).enableExpiration()
    }

    @Test
    fun `test when pick function for callback with dopa result`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "code",
            description = "description",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "dateOfExpiry",
                    laserId = "laserId"
                )
            )
        )
        //when
        presenter.decideTypeOfSuccessCallBack(
            data = response,
            isDopaEnable = true
        )

        //then
        verify(view).callbackWithDopa(
            eq(response)
        )
    }

    @Test
    fun `test when pick function for callback without dopa result`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "code",
            description = "description",
            data = null
        )
        //when
        presenter.decideTypeOfSuccessCallBack(
            data = response,
            isDopaEnable = false
        )

        //then
        verify(view).callbackWithoutDopa(
            eq(response)
        )
    }

    @Test
    fun `test when pick function for positive button in dopa error popup with CUS-KYC-7201`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-7201",
            description = "description",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "dateOfExpiry",
                    laserId = "laserId"
                )
            )
        )
        //when
        presenter.decideLogicForPopupPositiveButton(
            response,
            dialogInterface
        )

        //then
        verify(view).dismissDialog(
            any()
        )
    }

    @Test
    fun `test when pick function for positive button in dopa error popup with CUS-KYC-3004`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-3004",
            description = "description",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "dateOfExpiry",
                    laserId = "laserId"
                )
            )
        )
        //when
        presenter.decideLogicForPopupPositiveButton(
            response,
            dialogInterface
        )

        //then
        verify(view).dismissDialog(
            any()
        )
    }

    @Test
    fun `test when pick function for positive button in dopa error popup with CUS-KYC-7202`() {
        //given
        val response = ConfirmationInfoResponse(
            code = "CUS-KYC-7202",
            description = "description",
            data = DopaData(
                dopaCode = "dopaCode",
                dopaDesc = "dopaDesc",
                userConfirmedValue = UserConfirmedValue(
                    nationalId = "nodeId",
                    titleTh = "companyCode",
                    firstNameTh = "shortName",
                    middleNameTh = "marketingNameEn",
                    lastNameTh = "marketingNameTh",
                    titleEn = "smallIconPath",
                    firstNameEn = "mediumIconPath",
                    middleNameEn = "largeIconPath",
                    lastNameEn = "deepLinkIos",
                    dateOfBirth = "deepLinkAndroid",
                    dateOfIssue = "deepLinkHuawei",
                    dateOfExpiry = "dateOfExpiry",
                    laserId = "laserId"
                )
            )
        )
        //when
        presenter.decideLogicForPopupPositiveButton(
            response,
            dialogInterface
        )

        //then
        verify(view).callBackAfterFailDopa(
            eq(response),
            any()
        )
    }

    @Test
    fun `test when prefill is set as true`() {
        //given
        PrefillDisplayedToggle.titleThFlag = true
        PrefillDisplayedToggle.titleEnFlag = true
        PrefillDisplayedToggle.firstNameThFlag = true
        PrefillDisplayedToggle.firstNameEnFlag = true
        PrefillDisplayedToggle.lastNameThFlag = true
        PrefillDisplayedToggle.lastNameEnFlag = true
        PrefillDisplayedToggle.dateOfBirthFlag = true
        PrefillDisplayedToggle.dateOfIssueFlag = true
        PrefillDisplayedToggle.dateOfExpiryFlag = true
        PrefillDisplayedToggle.laserIdFlag = true
        val mockUserConfirmedValueDisplay = UserConfirmedValueDisplay(
            nationalId = "1234567890121" ,
            titleTh = "นาย",
            firstNameTh = "ชื่อ",
            middleNameTh = "กลาง",
            lastNameTh = "สกุล",
            titleEn = "MR.",
            firstNameEn = "NAME",
            middleNameEn = "MID",
            lastNameEn = "LASTNAME",
            dateOfBirth = "10/10/2000",
            dateOfIssue = "10/10/2010",
            dateOfExpiry = "10/10/2030",
            laserId = "JC1234567890"
        )

        //when
        val result = presenter.handleOcrPrefill(mockUserConfirmedValueDisplay)

        //then
        Assert.assertEquals("1234567890121", result.nationalId)
        Assert.assertEquals("นาย", result.titleTh)
        Assert.assertEquals("ชื่อ", result.firstNameTh)
        Assert.assertEquals("กลาง", result.middleNameTh)
        Assert.assertEquals("สกุล", result.lastNameTh)
        Assert.assertEquals("MR.", result.titleEn)
        Assert.assertEquals("NAME", result.firstNameEn)
        Assert.assertEquals("MID", result.middleNameEn)
        Assert.assertEquals("LASTNAME", result.lastNameEn)
        Assert.assertEquals("10/10/2000", result.dateOfBirth)
        Assert.assertEquals("10/10/2010", result.dateOfIssue)
        Assert.assertEquals("10/10/2030", result.dateOfExpiry)
        Assert.assertEquals("JC1234567890", result.laserId)
    }

    @Test
    fun `test when prefill is set as false`() {
        //given
        PrefillDisplayedToggle.titleThFlag = false
        PrefillDisplayedToggle.titleEnFlag = false
        PrefillDisplayedToggle.firstNameThFlag = false
        PrefillDisplayedToggle.firstNameEnFlag = false
        PrefillDisplayedToggle.lastNameThFlag = false
        PrefillDisplayedToggle.lastNameEnFlag = false
        PrefillDisplayedToggle.dateOfBirthFlag = false
        PrefillDisplayedToggle.dateOfIssueFlag = false
        PrefillDisplayedToggle.dateOfExpiryFlag = false
        PrefillDisplayedToggle.laserIdFlag = false
        val mockUserConfirmedValueDisplay = UserConfirmedValueDisplay(
            nationalId = "1234567890121" ,
            titleTh = "นาย",
            firstNameTh = "ชื่อ",
            middleNameTh = "กลาง",
            lastNameTh = "สกุล",
            titleEn = "MR.",
            firstNameEn = "NAME",
            middleNameEn = "MID",
            lastNameEn = "LASTNAME",
            dateOfBirth = "10/10/2000",
            dateOfIssue = "10/10/2010",
            dateOfExpiry = "10/10/2030",
            laserId = "JC1234567890"
        )

        //when
        val result = presenter.handleOcrPrefill(mockUserConfirmedValueDisplay)

        //then
        Assert.assertEquals("1234567890121", result.nationalId)
        Assert.assertEquals("", result.titleTh)
        Assert.assertEquals("", result.firstNameTh)
        Assert.assertEquals("กลาง", result.middleNameTh)
        Assert.assertEquals("", result.lastNameTh)
        Assert.assertEquals("", result.titleEn)
        Assert.assertEquals("", result.firstNameEn)
        Assert.assertEquals("MID", result.middleNameEn)
        Assert.assertEquals("", result.lastNameEn)
        Assert.assertEquals("", result.dateOfBirth)
        Assert.assertEquals("", result.dateOfIssue)
        Assert.assertEquals("", result.dateOfExpiry)
        Assert.assertEquals("", result.laserId)
    }
}