package com.scb.techx.ekycframework.data.ndid.mapper

import com.scb.techx.ekycframework.data.ndid.model.*
import com.scb.techx.ekycframework.data.ndid.mapper.NdidStatusMapperEntity
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class NdidStatusMapperEntityTest {
    val mapper: NdidStatusMapperEntity = NdidStatusMapperEntity()
    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    val ndidStatusEntityFull: NdidStatusEntity = NdidStatusEntity(
        code = "code",
        description = "description",
        dataEntity = DataStatusEntity(
            sessionId = "sessionId",
            referenceId = "referenceId",
            status = "status",
            expireTime = 100,
            idpEntity = IdpStatusEntity(
                nodeId = "nodeId",
                industryCode = "industryCode",
                companyCode = "companyCode",
                shortName = "shortName",
                marketingNameTh = "marketingNameTh",
                marketingNameEn = "marketingNameEn",
                smallIconPath = "smallIconPath",
                mediumIconPath = "mediumIconPath",
                largeIconPath = "largeIconPath",
                deepLinkAndroid = "deepLinkAndroid",
                deepLinkIos = "deepLinkIos",
                deepLinkHuawei = "deepLinkHuawei"
            ),
            ndidErrorEntity = NdidErrorStatusEntity(
                code = "code",
                description = "description",
                messageEn = "messageEn",
                messageTh = "messageTh"
            ),
            ndidData = NdidDataStatusEntity(
                requestId = "requestId"
            )
        )
    )

    val ndidStatusEntityEmpty: NdidStatusEntity = NdidStatusEntity(
        code = "code",
        description = "description",
        dataEntity = null
    )

    @Test
    fun `when convert full ndidStatusEntity`() {
        val resultFull = mapper.mapFromEntity(ndidStatusEntityFull)

        //when
        Assert.assertEquals(ndidStatusEntityFull.code, resultFull.code)
        Assert.assertEquals(ndidStatusEntityFull.description, resultFull.description)

        Assert.assertEquals(ndidStatusEntityFull.dataEntity?.status, resultFull.data.status)
        Assert.assertEquals(ndidStatusEntityFull.dataEntity?.referenceId, resultFull.data.referenceId)
        Assert.assertEquals(ndidStatusEntityFull.dataEntity?.expireTime, resultFull.data.expireTime)
        Assert.assertEquals(ndidStatusEntityFull.dataEntity?.sessionId, resultFull.data.sessionId)

        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.ndidData?.requestId,
            resultFull.data.ndidStatusNdidData.requestId
        )

        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.companyCode,
            resultFull.data.idpStatus.companyCode
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.deepLinkAndroid,
            resultFull.data.idpStatus.deepLinkAndroid
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.deepLinkHuawei,
            resultFull.data.idpStatus.deepLinkHuawei
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.deepLinkIos,
            resultFull.data.idpStatus.deepLinkIos
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.largeIconPath,
            resultFull.data.idpStatus.largeIconPath
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.marketingNameEn,
            resultFull.data.idpStatus.marketingNameEn
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.marketingNameTh,
            resultFull.data.idpStatus.marketingNameTh
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.mediumIconPath,
            resultFull.data.idpStatus.mediumIconPath
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.nodeId,
            resultFull.data.idpStatus.nodeId
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.shortName,
            resultFull.data.idpStatus.shortName
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.idpEntity?.smallIconPath,
            resultFull.data.idpStatus.smallIconPath
        )

        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.ndidErrorEntity?.messageEn,
            resultFull.data.ndidError.messageEn
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.ndidErrorEntity?.messageTh,
            resultFull.data.ndidError.messageTh
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.ndidErrorEntity?.code,
            resultFull.data.ndidError.code
        )
        Assert.assertEquals(
            ndidStatusEntityFull.dataEntity?.ndidErrorEntity?.description,
            resultFull.data.ndidError.description
        )
    }

    @Test
    fun `when convert empty ndidStatusEntity`() {
        val resultFull = mapper.mapFromEntity(ndidStatusEntityEmpty)

        //when
        Assert.assertEquals("code", resultFull.code)
        Assert.assertEquals("description", resultFull.description)

        Assert.assertEquals("", resultFull.data.status)
        Assert.assertEquals("", resultFull.data.referenceId)
        Assert.assertEquals(0, resultFull.data.expireTime)
        Assert.assertEquals("", resultFull.data.sessionId)

        Assert.assertEquals(
            "",
            resultFull.data.ndidStatusNdidData.requestId
        )

        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.companyCode
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.deepLinkAndroid
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.deepLinkHuawei
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.deepLinkIos
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.largeIconPath
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.marketingNameEn
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.marketingNameTh
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.mediumIconPath
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.nodeId
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.shortName
        )
        Assert.assertEquals(
            "",
            resultFull.data.idpStatus.smallIconPath
        )

        Assert.assertEquals(
            "",
            resultFull.data.ndidError.messageEn
        )
        Assert.assertEquals(
            "",
            resultFull.data.ndidError.messageTh
        )
        Assert.assertEquals(
            "",
            resultFull.data.ndidError.code
        )
        Assert.assertEquals(
            "",
            resultFull.data.ndidError.description
        )
    }
}