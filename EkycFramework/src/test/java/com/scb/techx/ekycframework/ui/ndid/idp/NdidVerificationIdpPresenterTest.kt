package com.scb.techx.ekycframework.ui.ndid.idp

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Resources
import com.nhaarman.mockito_kotlin.*
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidIdpResponse
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpData
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestData
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpList
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidRequestResponse
import com.scb.techx.ekycframework.domain.ndid.model.response.Idp
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidData
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.ui.ndidverification.idplist.presenter.NdidIdpListContract
import com.scb.techx.ekycframework.ui.ndidverification.idplist.presenter.NdidIdpListPresenter
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.Mockito.`when`

import org.mockito.junit.MockitoJUnitRunner
import java.util.concurrent.TimeUnit

@RunWith(MockitoJUnitRunner::class)
class NdidVerificationIdpPresenterTest {

    lateinit var presenter: NdidIdpListPresenter
    lateinit var pref: EkycPreferenceUtil

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var resources: Resources

    @Mock
    lateinit var view: NdidIdpListContract.View

    @Mock
    lateinit var repository: NdidRepository

    lateinit var testScheduler: TestScheduler

    @Before
    fun setup() {

        MockitoAnnotations.initMocks(this)
//        view = mock()
//        repository = mock()
        pref = EkycPreferenceUtil(context)
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"

        testScheduler = TestScheduler()

        presenter = NdidIdpListPresenter(
            view, pref, context, testScheduler, testScheduler, repository
        )
    }

    @Test
    fun `when call api for idp list with code 1000`() {
        //given
        val response = NdidIdpResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = IdpData(
                registeredIdpList = mutableListOf(
                    IdpList(
                        nodeId = "node 1",
                        industryCode = "industry 1",
                        companyCode = "company 1",
                        marketingNameTh = "marketingNameTh 1",
                        marketingNameEn = "marketingNameEn 1",
                        smallIconPath = "smallIconPath 1",
                        mediumIconPath = "mediumIconPath 1",
                        largeIconPath = "largeIconPath 1",
                        deepLinkIos = "deepLinkIos 1",
                        deepLinkAndroid = "deepLinkAndroid 1",
                        deepLinkHuawei = "deepLinkHuawei 1"
                    ),
                    IdpList(
                        nodeId = "node 2",
                        industryCode = "industry 2",
                        companyCode = "company 2",
                        marketingNameTh = "marketingNameTh 2",
                        marketingNameEn = "marketingNameEn 2",
                        smallIconPath = "smallIconPath 2",
                        mediumIconPath = "mediumIconPath 2",
                        largeIconPath = "largeIconPath 2",
                        deepLinkIos = "deepLinkIos 2",
                        deepLinkAndroid = "deepLinkAndroid 2",
                        deepLinkHuawei = "deepLinkHuawei 2"
                    )
                ),
                idpList = mutableListOf(
                    IdpList(
                        nodeId = "node 3",
                        industryCode = "industry 3",
                        companyCode = "company 3",
                        marketingNameTh = "marketingNameTh 3",
                        marketingNameEn = "marketingNameEn 3",
                        smallIconPath = "smallIconPath 3",
                        mediumIconPath = "mediumIconPath 3",
                        largeIconPath = "largeIconPath 3",
                        deepLinkIos = "deepLinkIos 3",
                        deepLinkAndroid = "deepLinkAndroid 3",
                        deepLinkHuawei = "deepLinkHuawei 3"
                    ),
                    IdpList(
                        nodeId = "node 4",
                        industryCode = "industry 4",
                        companyCode = "company 4",
                        marketingNameTh = "marketingNameTh 4",
                        marketingNameEn = "marketingNameEn 4",
                        smallIconPath = "smallIconPath 4",
                        mediumIconPath = "mediumIconPath 4",
                        largeIconPath = "largeIconPath 4",
                        deepLinkIos = "deepLinkIos 4",
                        deepLinkAndroid = "deepLinkAndroid 4",
                        deepLinkHuawei = "deepLinkHuawei 4"
                    ),
                )
            )
        )

        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(repository.getIdpList(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getIdpList()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).callBackIdpResponse(eq(response.data))
    }

    @Test
    fun `when call api for idp list with code 7301`() {
        //given
        val response = NdidIdpResponse(
            code = "CUS-KYC-7301",
            description = "Success",
            data = IdpData(
                registeredIdpList = mutableListOf(
                    IdpList(
                        nodeId = "node 1",
                        industryCode = "industry 1",
                        companyCode = "company 1",
                        marketingNameTh = "marketingNameTh 1",
                        marketingNameEn = "marketingNameEn 1",
                        smallIconPath = "smallIconPath 1",
                        mediumIconPath = "mediumIconPath 1",
                        largeIconPath = "largeIconPath 1",
                        deepLinkIos = "deepLinkIos 1",
                        deepLinkAndroid = "deepLinkAndroid 1",
                        deepLinkHuawei = "deepLinkHuawei 1"
                    ),
                    IdpList(
                        nodeId = "node 2",
                        industryCode = "industry 2",
                        companyCode = "company 2",
                        marketingNameTh = "marketingNameTh 2",
                        marketingNameEn = "marketingNameEn 2",
                        smallIconPath = "smallIconPath 2",
                        mediumIconPath = "mediumIconPath 2",
                        largeIconPath = "largeIconPath 2",
                        deepLinkIos = "deepLinkIos 2",
                        deepLinkAndroid = "deepLinkAndroid 2",
                        deepLinkHuawei = "deepLinkHuawei 2"
                    )
                ),
                idpList = mutableListOf(
                    IdpList(
                        nodeId = "node 3",
                        industryCode = "industry 3",
                        companyCode = "company 3",
                        marketingNameTh = "marketingNameTh 3",
                        marketingNameEn = "marketingNameEn 3",
                        smallIconPath = "smallIconPath 3",
                        mediumIconPath = "mediumIconPath 3",
                        largeIconPath = "largeIconPath 3",
                        deepLinkIos = "deepLinkIos 3",
                        deepLinkAndroid = "deepLinkAndroid 3",
                        deepLinkHuawei = "deepLinkHuawei 3"
                    ),
                    IdpList(
                        nodeId = "node 4",
                        industryCode = "industry 4",
                        companyCode = "company 4",
                        marketingNameTh = "marketingNameTh 4",
                        marketingNameEn = "marketingNameEn 4",
                        smallIconPath = "smallIconPath 4",
                        mediumIconPath = "mediumIconPath 4",
                        largeIconPath = "largeIconPath 4",
                        deepLinkIos = "deepLinkIos 4",
                        deepLinkAndroid = "deepLinkAndroid 4",
                        deepLinkHuawei = "deepLinkHuawei 4"
                    ),
                )
            )
        )

        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(repository.getIdpList(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(any()))
            .thenReturn(
                "You have not registered to use NDID Service. Please register NDID Service first."
            )

        //when
        presenter.getIdpList()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).showDialog(
            eq("You have not registered to use NDID Service. Please register NDID Service first."),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `when call api for idp list with code 2003`() {
        //given
        val response = NdidIdpResponse(
            code = "CUS-KYC-2003",
            description = "Success",
            data = IdpData(
                registeredIdpList = mutableListOf(
                    IdpList(
                        nodeId = "node 1",
                        industryCode = "industry 1",
                        companyCode = "company 1",
                        marketingNameTh = "marketingNameTh 1",
                        marketingNameEn = "marketingNameEn 1",
                        smallIconPath = "smallIconPath 1",
                        mediumIconPath = "mediumIconPath 1",
                        largeIconPath = "largeIconPath 1",
                        deepLinkIos = "deepLinkIos 1",
                        deepLinkAndroid = "deepLinkAndroid 1",
                        deepLinkHuawei = "deepLinkHuawei 1"
                    ),
                    IdpList(
                        nodeId = "node 2",
                        industryCode = "industry 2",
                        companyCode = "company 2",
                        marketingNameTh = "marketingNameTh 2",
                        marketingNameEn = "marketingNameEn 2",
                        smallIconPath = "smallIconPath 2",
                        mediumIconPath = "mediumIconPath 2",
                        largeIconPath = "largeIconPath 2",
                        deepLinkIos = "deepLinkIos 2",
                        deepLinkAndroid = "deepLinkAndroid 2",
                        deepLinkHuawei = "deepLinkHuawei 2"
                    )
                ),
                idpList = mutableListOf(
                    IdpList(
                        nodeId = "node 3",
                        industryCode = "industry 3",
                        companyCode = "company 3",
                        marketingNameTh = "marketingNameTh 3",
                        marketingNameEn = "marketingNameEn 3",
                        smallIconPath = "smallIconPath 3",
                        mediumIconPath = "mediumIconPath 3",
                        largeIconPath = "largeIconPath 3",
                        deepLinkIos = "deepLinkIos 3",
                        deepLinkAndroid = "deepLinkAndroid 3",
                        deepLinkHuawei = "deepLinkHuawei 3"
                    ),
                    IdpList(
                        nodeId = "node 4",
                        industryCode = "industry 4",
                        companyCode = "company 4",
                        marketingNameTh = "marketingNameTh 4",
                        marketingNameEn = "marketingNameEn 4",
                        smallIconPath = "smallIconPath 4",
                        mediumIconPath = "mediumIconPath 4",
                        largeIconPath = "largeIconPath 4",
                        deepLinkIos = "deepLinkIos 4",
                        deepLinkAndroid = "deepLinkAndroid 4",
                        deepLinkHuawei = "deepLinkHuawei 4"
                    ),
                )
            )
        )

        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(repository.getIdpList(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(any()))
            .thenReturn(
                "You have not registered to use NDID Service. Please register NDID Service first."
            )

        //when
        presenter.getIdpList()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).showDialog(
            eq("You have not registered to use NDID Service. Please register NDID Service first."),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `when call api for idp list with other code`() {
        //given
        val response = NdidIdpResponse(
            code = "CUS-KYC-other",
            description = "Success",
            data = IdpData(
                registeredIdpList = mutableListOf(
                    IdpList(
                        nodeId = "node 1",
                        industryCode = "industry 1",
                        companyCode = "company 1",
                        marketingNameTh = "marketingNameTh 1",
                        marketingNameEn = "marketingNameEn 1",
                        smallIconPath = "smallIconPath 1",
                        mediumIconPath = "mediumIconPath 1",
                        largeIconPath = "largeIconPath 1",
                        deepLinkIos = "deepLinkIos 1",
                        deepLinkAndroid = "deepLinkAndroid 1",
                        deepLinkHuawei = "deepLinkHuawei 1"
                    ),
                    IdpList(
                        nodeId = "node 2",
                        industryCode = "industry 2",
                        companyCode = "company 2",
                        marketingNameTh = "marketingNameTh 2",
                        marketingNameEn = "marketingNameEn 2",
                        smallIconPath = "smallIconPath 2",
                        mediumIconPath = "mediumIconPath 2",
                        largeIconPath = "largeIconPath 2",
                        deepLinkIos = "deepLinkIos 2",
                        deepLinkAndroid = "deepLinkAndroid 2",
                        deepLinkHuawei = "deepLinkHuawei 2"
                    )
                ),
                idpList = mutableListOf(
                    IdpList(
                        nodeId = "node 3",
                        industryCode = "industry 3",
                        companyCode = "company 3",
                        marketingNameTh = "marketingNameTh 3",
                        marketingNameEn = "marketingNameEn 3",
                        smallIconPath = "smallIconPath 3",
                        mediumIconPath = "mediumIconPath 3",
                        largeIconPath = "largeIconPath 3",
                        deepLinkIos = "deepLinkIos 3",
                        deepLinkAndroid = "deepLinkAndroid 3",
                        deepLinkHuawei = "deepLinkHuawei 3"
                    ),
                    IdpList(
                        nodeId = "node 4",
                        industryCode = "industry 4",
                        companyCode = "company 4",
                        marketingNameTh = "marketingNameTh 4",
                        marketingNameEn = "marketingNameEn 4",
                        smallIconPath = "smallIconPath 4",
                        mediumIconPath = "mediumIconPath 4",
                        largeIconPath = "largeIconPath 4",
                        deepLinkIos = "deepLinkIos 4",
                        deepLinkAndroid = "deepLinkAndroid 4",
                        deepLinkHuawei = "deepLinkHuawei 4"
                    ),
                )
            )
        )

        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(repository.getIdpList(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))
        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(any()))
            .thenReturn(
                "Sorry, your request cannot be proceeded at the moment. Please try again"
            )

        //when
        presenter.getIdpList()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).showDialog(
            eq("Sorry, your request cannot be proceeded at the moment. Please try again"),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getIdpList throw`() {
        //given
        whenever(repository.getIdpList(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    Exception("Mock Error")
                )
            )
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getIdpList()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleHttpException(any())
    }

    @Test
    fun `when call api for ndid request with code 1000`() {
        //given
        val response = NdidRequestResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidRequestData(
                sessionId = "sessionId",
                referenceId = "re",
                status = "s",
                expireTime = 100,
                idp = Idp(
                    nodeId = "node",
                    industryCode = "industryCode",
                    companyCode = "company",
                    shortName = "short name",
                    marketingNameTh = "marketingNameTh",
                    marketingNameEn = "marketingNameEn",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidData = NdidData(
                    requestId = "requestId"
                )
            )
        )

        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(repository.getNdidRequest(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))

        //when
        presenter.getNdidRequest("node", "company", true, "industryCode")
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToCountDown(
            any()
        )
    }

    @Test
    fun `when call api for ndid request with other code`() {
        //given
        val response = NdidRequestResponse(
            code = "CUS-KYC-other",
            description = "Success",
            data = NdidRequestData(
                sessionId = "sessionId",
                referenceId = "re",
                status = "s",
                expireTime = 100,
                idp = Idp(
                    nodeId = "node",
                    industryCode = "industryCode",
                    companyCode = "company",
                    shortName = "short name",
                    marketingNameTh = "marketingNameTh",
                    marketingNameEn = "marketingNameEn",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidData = NdidData(
                    requestId = "requestId"
                )
            )
        )

        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(repository.getNdidRequest(
            any(),
            any()
        ))
            .thenReturn(Single.just(response))

        //when
        presenter.getNdidRequest("node", "company", true, "industryCode")
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleErrorEkyc(
            eq(response.code)
        )
    }

    @Test
    fun `test getNdidRequest throw`() {
        //given
        whenever(repository.getNdidRequest(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    Exception("Mock Error")
                )
            )
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidRequest("idpNodeId", "idpCompanyCode", true, "industryCode")
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleHttpException(any())
    }
}