package com.scb.techx.ekycframework.ui.ndid.enrollment

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Resources
import android.os.CountDownTimer
import com.nhaarman.mockito_kotlin.whenever

import com.nhaarman.mockito_kotlin.any
import com.nhaarman.mockito_kotlin.anyOrNull
import com.nhaarman.mockito_kotlin.eq
import com.nhaarman.mockito_kotlin.verify
import com.scb.techx.ekycframework.R
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatusData
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatusNdidData
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidErrorStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.presenter.NdidVerificationEnrollmentContract
import com.scb.techx.ekycframework.ui.ndidverification.enrollmentfragment.presenter.NdidVerificationEnrollmentPresenter
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.mockito.Mockito.`when`

import org.mockito.junit.MockitoJUnitRunner
import java.util.concurrent.TimeUnit

@RunWith(MockitoJUnitRunner::class)
class NdidVerificationEnrollmentPresenterTest {

    lateinit var presenter: NdidVerificationEnrollmentPresenter
    lateinit var pref: EkycPreferenceUtil

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var resources: Resources

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var view: NdidVerificationEnrollmentContract.View

    @Mock
    lateinit var repository: NdidRepository

    @Mock
    lateinit var countDownTimer: CountDownTimer

    lateinit var testScheduler: TestScheduler

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)

        pref = EkycPreferenceUtil(context)
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"

        testScheduler = TestScheduler()

        presenter = NdidVerificationEnrollmentPresenter(
            view, pref, context, testScheduler, testScheduler, repository
        )
    }

    @Test
    fun `test getNDID with CUS1000 and user-select-idp`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "user-select-idp",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        `when`(context.resources)
            .thenReturn(this.resources)
        `when`(resources.getString(any()))
            .thenReturn("Identification String")

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).stopTimer()
        verify(view).showDialog(
            eq("Identification String"),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
        verify(view).setTimer(
            eq((100 * 1000).toLong())
        )
        verify(view).startTimer()
    }

    @Test
    fun `test getNDID with CUS1000 and idp-pending`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        `when`(context.resources)
            .thenReturn(this.resources)
        `when`(resources.getString(any()))
            .thenReturn("Identification String")

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).showDialog(
            eq("Identification String"),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
        verify(view).setTimer(
            eq((100 * 1000).toLong())
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-confirmed-accept`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        `when`(context.resources)
            .thenReturn(this.resources)
        `when`(resources.getString(any()))
            .thenReturn("Identification String")

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).showDialog(
            eq("Identification String"),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
        verify(view).setTimer(
            eq((100 * 1000).toLong())
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-confirmed-accept and is verifyClick = false`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(false)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view, Mockito.times(0)).showDialog(
            any(), any(), any(), any(),eq(null), eq(null)
        )
        verify(view).setTimer(
            eq((100 * 1000).toLong())
        )
    }

    @Test
    fun `test getNDID with CUS1000 and as-data-completed`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-data-completed",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-errored`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "40200",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and as-errored`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "40200",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-pending-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "40200",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-confirmed-accept-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "40200",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and ndid-connection-fail`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "ndid-connection-fail",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "40200",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-confirmed-reject`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-reject",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "40200",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(
            repository.getNdidStatus(
                any()
            )
        )
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(isVerifyClick = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and other status`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "other-status",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "40200",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID throw`() {
        //given
        whenever(repository.getNdidStatus(
            any()
        ))
            .thenReturn(
                Single.error(
                    Exception("Mock Error")
                )
            )
        `when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus(true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleHttpException(any())
    }

    @Test
    fun clickCancel() {
        //given
        `when`(context.resources).thenReturn(resources)
        `when`(resources.getString(R.string.Ekyc_ndid_holding_confirm_cancel)).thenReturn(
            "Do you want to cancel authentication?"
        )

        //when
        presenter.onClickCancel()

        //then
        verify(view).showDialog(
            any(),
            any(),
            any(),
            any(),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun start() {
        //when
        presenter.start()

        //then
        verify(view).startTimer()
    }

    @Test
    fun stop() {
        //when
        presenter.stop()

        //then
        verify(view).stopTimer()
    }

    @Test
    fun onClickBankTitleDeeplinkAndroid() {
        //given
        `when`(view.isPackageInstalled(any())).thenReturn(true)

        //when
        presenter.onClickBankTile("deeplinkAndroid", null)

        //then
        verify(view).navigateDeeplink(eq("deeplinkAndroid"))
    }

    @Test
    fun onClickBankTitleDeeplinkHuawei() {
        //given
        `when`(view.isPackageInstalled(any())).thenReturn(false)

        //when
        presenter.onClickBankTile("deeplinkHuawei", "deeplinkHuawei")

        //then
        verify(view).navigateDeeplink(eq("deeplinkHuawei"))
    }

    @Test
    fun onClickBankTitleDeeplinkAndroidIsNull() {
        //given
        `when`(context.resources).thenReturn(resources)
        `when`(resources.getString(R.string.Ekyc_ndid_holding_deeplink_alert)).thenReturn(
            "Please verify your identification at IdP\\'s mobile\napplication"
        )

        //when
        presenter.onClickBankTile(null, null)

        //then
        verify(view).showDialog(any(), any(), any(), anyOrNull(), eq(null), eq(null))
    }

    @Test
    fun timerCalculated() {
        //when
        val result = presenter.timerCalculated(1)

        //then
        assertEquals(1000, result)
    }
}