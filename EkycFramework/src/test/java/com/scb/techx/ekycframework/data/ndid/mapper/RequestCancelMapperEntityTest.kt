package com.scb.techx.ekycframework.data.ndid.mapper

import com.scb.techx.ekycframework.data.ndid.model.DataRequestCancelEntity
import com.scb.techx.ekycframework.data.ndid.model.NdidDataEntity
import com.scb.techx.ekycframework.data.ndid.model.RequestCancelEntity
import com.scb.techx.ekycframework.data.ndid.mapper.RequestCancelMapperEntity
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class RequestCancelMapperEntityTest {
    val mapper: RequestCancelMapperEntity = RequestCancelMapperEntity()
    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    val requestCancelEntityFull: RequestCancelEntity = RequestCancelEntity(
        code = "code",
        description = "description",
        dataEntity = DataRequestCancelEntity(
            sessionId = "sessionId",
            referenceId = "referenceId",
            status = "status"
        ),
        ndidDataEntity = NdidDataEntity(
            requestId = "requestId"
        )
    )

    val requestCancelEntityEmpty: RequestCancelEntity = RequestCancelEntity(
        code = "code",
        description = "description",
        dataEntity = null,
        ndidDataEntity = null
    )

    @Test
    fun `when convert full requestCancelEntity`() {
        val resultFull = mapper.mapFromEntity(requestCancelEntityFull)

        //when
        Assert.assertEquals(requestCancelEntityFull.code, resultFull.code)
        Assert.assertEquals(requestCancelEntityFull.description, resultFull.description)

        Assert.assertEquals(requestCancelEntityFull.dataEntity?.referenceId, resultFull.data?.referenceId)
        Assert.assertEquals(requestCancelEntityFull.dataEntity?.sessionId, resultFull.data?.sessionId)
        Assert.assertEquals(requestCancelEntityFull.dataEntity?.status, resultFull.data?.status)

        Assert.assertEquals(requestCancelEntityFull.ndidDataEntity?.requestId, resultFull.ndidData?.requestId)
    }

    @Test
    fun `when convert empty requestCancelEntity`() {
        val resultFull = mapper.mapFromEntity(requestCancelEntityEmpty)

        //when
        Assert.assertEquals(requestCancelEntityFull.code, resultFull.code)
        Assert.assertEquals(requestCancelEntityFull.description, resultFull.description)

        Assert.assertEquals(null, resultFull.data?.referenceId)
        Assert.assertEquals(null, resultFull.data?.sessionId)
        Assert.assertEquals(null, resultFull.data?.status)

        Assert.assertEquals(null, resultFull.ndidData?.requestId)
    }
}