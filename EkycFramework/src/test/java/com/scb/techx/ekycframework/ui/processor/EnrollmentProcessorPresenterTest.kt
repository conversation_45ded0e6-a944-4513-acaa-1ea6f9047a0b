package com.scb.techx.ekycframework.ui.processor

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Resources
import com.nhaarman.mockito_kotlin.eq
import com.nhaarman.mockito_kotlin.any
import com.nhaarman.mockito_kotlin.anyOrNull
import com.nhaarman.mockito_kotlin.verify
import com.nhaarman.mockito_kotlin.doNothing
import com.scb.techx.ekycframework.domain.apihelper.usecase.AuthenticatedHeaders
import com.scb.techx.ekycframework.ui.processor.enrollment.presenter.EnrollmentProcessorContract
import com.scb.techx.ekycframework.ui.processor.enrollment.presenter.EnrollmentProcessorPresenter
import com.scb.techx.ekycframework.domain.ocrliveness.model.request.Enrollment3DRequest
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DData
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.Enrollment3DResponse
import com.scb.techx.ekycframework.domain.ocrliveness.repository.FaceTecRepository
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.ResponseBody
import okhttp3.internal.http2.ErrorCode
import okhttp3.internal.http2.StreamResetException
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.mockito.junit.MockitoJUnitRunner
import retrofit2.HttpException
import retrofit2.Response
import java.util.concurrent.TimeUnit

@RunWith(MockitoJUnitRunner::class)
class EnrollmentProcessorPresenterTest {
    lateinit var presenter: EnrollmentProcessorPresenter
    lateinit var pref: EkycPreferenceUtil

    @Mock
    lateinit var editor: SharedPreferences.Editor

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var resources: Resources

    @Mock
    lateinit var processor: EnrollmentProcessorContract.Processor

    @Mock
    lateinit var repository: FaceTecRepository

    lateinit var testScheduler: TestScheduler

    private fun mockForClearPreference() {
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        Mockito.`when`(sharedPreferences.edit()).thenReturn(editor)
        Mockito.`when`(editor.putString(any(), any())).thenReturn(editor)
        Mockito.`when`(editor.putBoolean(any(), any())).thenReturn(editor)
        doNothing().`when`(editor).apply()
    }

    private fun validateClearPreference() {
        verify(editor).putString(eq("ekycToken"), eq(""))
        verify(editor).putString(eq("sessionFaceTec"), eq(""))
        verify(editor).putString(eq("productionKey"), eq(""))
        verify(editor).putString(eq("deviceKey"), eq(""))
        verify(editor).putString(eq("encryptionKey"), eq(""))
        verify(editor).putString(eq("sdkEncryptionKeyOcr"), eq(""))
        verify(editor).putString(eq("sdkEncryptionIv"), eq(""))
        verify(editor).putBoolean(eq("enableConfirmInfo"), eq(true))
    }

    @Before
    fun setup() {

        MockitoAnnotations.initMocks(this)

        pref = EkycPreferenceUtil(context)
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"

        testScheduler = TestScheduler()

        presenter = EnrollmentProcessorPresenter(
            processor, pref, testScheduler, testScheduler, repository
        )
    }

    @Test
    fun `when call face scan api and get 1000`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-1000",
            description = "Success",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = true
            )
        )

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.just(response))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).proceedToNextStepFaceScan(
            anyOrNull(),
            eq("mock")
        )
    }

    @Test
    fun `when call face scan api and get 1000 but was process is false`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-1000",
            description = "Mock Error",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.just(response))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Mock Error")
        )
    }

    @Test
    fun `when call face scan api and get 2003`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-2003",
            description = "Data not found",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Data not found")
        )
    }

    @Test
    fun `when call face scan api and get 9001`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-9001",
            description = "Timeout",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Timeout")
        )
    }

    @Test
    fun `when call face scan api and get 9002`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-9002",
            description = "Connection error",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Connection error")
        )
    }

    @Test
    fun `when call face scan api and get 1999`() {
        //given
        val response = Enrollment3DResponse(
            code = "CUS-KYC-1999",
            description = "Unable to process",
            data = Enrollment3DData(
                scanResultBlob = "mock",
                wasProcessed = false
            )
        )

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.just(response))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Unable to process")
        )
    }

    @Test
    fun `when call face scan api and get http exception 401`() {
        //given
        val throwable = HttpException(
            Response.error<Any>(401, ResponseBody.create(
                "plain/text".toMediaType(), ""
            )))

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.error(
            throwable
        ))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Invalid token or token expired")
        )
    }

    @Test
    fun `when call face scan api and get other http exception`() {
        //given
        val throwable = HttpException(
            Response.error<Any>(500, ResponseBody.create(
                "plain/text".toMediaType(), ""
            )))

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.error(
            throwable
        ))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Unable to process")
        )
    }

    @Test
    fun `when call face scan api and get stream reset exception`() {
        //given
        val throwable = StreamResetException(ErrorCode.CONNECT_ERROR)

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.error(
            throwable
        ))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Connection error")
        )
    }

    @Test
    fun `when call face scan api and get other exception`() {
        //given
        val throwable = RuntimeException()

        Mockito.`when`(
            repository.getEnrollment3D(
                any(),
                any()
            )
        ).thenReturn(Single.error(
            throwable
        ))
        mockForClearPreference()

        //when
        presenter.sendApiForEnrollment3D(
            faceTecFaceScanResultCallback = null,
            authenticatedHeaders = AuthenticatedHeaders(),
            request = Enrollment3DRequest(
                auditTrailImage = "auditTrialImage",
                faceScan = "facescan",
                lowQualityAuditTrailImage = "lowQualityAuditTrailImage",
                function = "liveness"
            )
        )
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        validateClearPreference()
        verify(processor).faceTecCancelFaceScan(
            anyOrNull(),
            eq("Unable to process")
        )
    }
}