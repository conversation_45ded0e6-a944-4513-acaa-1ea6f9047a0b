package com.scb.techx.ekycframework.ui.ocridcard.mapper

import com.scb.techx.ekycframework.data.ocridcard.mapper.InitFlowResponseMapperEntity
import com.scb.techx.ekycframework.data.ocridcard.model.InitFlowDataEntity
import com.scb.techx.ekycframework.data.ocridcard.model.InitFlowResponseEntity
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class InitFlowResponseMapperEntityTest {
    val mapper: InitFlowResponseMapperEntity = InitFlowResponseMapperEntity()
    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    private val initFlowResponseEntityFull: InitFlowResponseEntity = InitFlowResponseEntity(
        code = "code",
        description = "description",
        data = InitFlowDataEntity(
            sdkEncryptionIv = "sdkEncryptionIv",
            sdkEncryptionKeyOcr = "sdkEncryptionKeyOcr"
        )
    )

    private val initFlowResponseEntityEmpty: InitFlowResponseEntity = InitFlowResponseEntity(
        code = "code",
        description = "description",
        data = null
    )

    @Test
    fun `when convert full requestCancelEntity`() {
        val resultFull = mapper.mapFromEntity(initFlowResponseEntityFull)

        //when
        Assert.assertEquals(initFlowResponseEntityFull.code, resultFull.code)
        Assert.assertEquals(initFlowResponseEntityFull.description, resultFull.description)

        print(resultFull.data.sdkEncryptionIv)
        print(resultFull.data.sdkEncryptionKeyOcr)

        Assert.assertEquals(initFlowResponseEntityFull.data?.sdkEncryptionIv, resultFull.data.sdkEncryptionIv)
        Assert.assertEquals(initFlowResponseEntityFull.data?.sdkEncryptionKeyOcr, resultFull.data.sdkEncryptionKeyOcr)
    }

    @Test
    fun `when convert empty requestCancelEntity`() {
        val resultEmpty = mapper.mapFromEntity(initFlowResponseEntityEmpty)

        //when
        Assert.assertEquals(initFlowResponseEntityEmpty.code, resultEmpty.code)
        Assert.assertEquals(initFlowResponseEntityEmpty.description, resultEmpty.description)

        Assert.assertEquals("", resultEmpty.data.sdkEncryptionIv)
        Assert.assertEquals("", resultEmpty.data.sdkEncryptionKeyOcr)
    }
}