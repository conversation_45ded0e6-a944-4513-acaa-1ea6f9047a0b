package com.scb.techx.ekycframework.ui.ocridcard.mapper

import com.scb.techx.ekycframework.data.ocridcard.mapper.InitFlowRequestMapperToEntity
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowRequest
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class InitFlowRequestMapperToEntityTest {
    val mapper: InitFlowRequestMapperToEntity = InitFlowRequestMapperToEntity()
    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    private val initFlowRequestTrue: InitFlowRequest = InitFlowRequest(
        ocr = true
    )

    private val initFlowRequestFalse: InitFlowRequest = InitFlowRequest(
        ocr = false
    )

    @Test
    fun `when convert init flow request ocr = true`() {
        val resultFull = mapper.mapToEntity(initFlowRequestTrue)

        //when
        Assert.assertEquals("1", resultFull.ocr)
    }

    @Test
    fun `when convert init flow request ocr = false`() {
        val resultFull = mapper.mapToEntity(initFlowRequestFalse)

        //when
        Assert.assertEquals("0", resultFull.ocr)
    }
}