package com.scb.techx.ekycframework.data.ndid.mapper.idpList

import com.scb.techx.ekycframework.data.ndid.mapper.idplist.NdidRequestRequestMapperToEntity
import com.scb.techx.ekycframework.domain.ndid.model.request.NdidIdpRequestRequest
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class NdidRequestRequestMapperToEntityTest {
    val mapper = NdidRequestRequestMapperToEntity()

    private val idpRequestMock = NdidIdpRequestRequest(
        idpNodeId = "idpNodeId",
        idpIndustryCode = "idpIndustryCode",
        idpCompanyCode = "idpCompanyCode",
        idpRegistered = true,
        identifierType = "identifierType",
        identifierValue = "identifierValue",
        serviceId = "serviceId"
    )

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Test
    fun `when convert full get session response`() {
        val result = mapper.mapToEntity(idpRequestMock)

        Assert.assertEquals(idpRequestMock.idpNodeId, result.idpNodeId)
        Assert.assertEquals(idpRequestMock.idpIndustryCode, result.idpIndustryCode)
        Assert.assertEquals(idpRequestMock.idpCompanyCode, result.idpCompanyCode)
        Assert.assertEquals(idpRequestMock.idpRegistered, result.idpRegistered)
        Assert.assertEquals(idpRequestMock.identifierType, result.identifierType)
        Assert.assertEquals(idpRequestMock.identifierValue, result.identifierValue)
        Assert.assertEquals(idpRequestMock.serviceId, result.serviceId)
    }
}