package com.scb.techx.ekycframework.ui.ndid

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Resources
import com.nhaarman.mockito_kotlin.*
import com.scb.techx.ekycframework.HandleCallback
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.data.ndid.datarepository.NdidDataRepository
import com.scb.techx.ekycframework.domain.ndid.repository.NdidRepository
import com.scb.techx.ekycframework.data.ndid.api.NdidApi
import com.scb.techx.ekycframework.ui.ndidverification.presenter.NdidVerificationContract
import com.scb.techx.ekycframework.ui.ndidverification.presenter.NdidVerificationPresenter
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import com.scb.techx.ekycframework.domain.ndid.model.response.IdpStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidErrorStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatus
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatusData
import com.scb.techx.ekycframework.domain.ndid.model.response.NdidStatusNdidData
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import java.util.concurrent.TimeUnit

class NdidVerificationPresenterTest {
    private lateinit var presenter: NdidVerificationPresenter
    private lateinit var pref: EkycPreferenceUtil

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var view: NdidVerificationContract.View

    private lateinit var ndidRepository: NdidRepository

    @Mock
    lateinit var ndidRepositoryMock: NdidRepository

    @Mock
    lateinit var service : NdidApi

    @Mock
    lateinit var resources: Resources

    private lateinit var testScheduler: TestScheduler

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)

        pref = EkycPreferenceUtil(context)
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"

        testScheduler = TestScheduler()

        presenter = NdidVerificationPresenter(
            view, pref, context, testScheduler, testScheduler, ndidRepositoryMock
        )
        ndidRepository = NdidDataRepository(service)
    }

    @Test
    fun `when call start`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "user-select-idp",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.start()

        //then
        verify(ndidRepositoryMock).getNdidStatus(any())
    }

    @Test
    fun `test getNDID with CUS1000 and user-select-idp`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "user-select-idp",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS1000 and idp-pending`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS1000 and idp-confirmed-accept`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS1000 and get non-pending ndid status`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-data-completed",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(
            eq(responseNdidStatus)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and as-data-completed`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-data-completed",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).navigateToSuccess(
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(responseNdidStatus.data.referenceId),
            eq(responseNdidStatus.data.ndidStatusNdidData.requestId)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-errored`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageEn),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and as-errored english`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        HandleCallback.language = "en"
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageEn),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and as-errored thai`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        HandleCallback.language = "th"
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageTh),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-pending-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-confirmed-accept-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS1000 and ndid-connection-fail`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "ndid-connection-fail",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS1000 and idp-confirmed-reject`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-reject",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(any())).thenReturn("You have rejected your identity verification. Please retry or select another Identity Provider.")
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq("You have rejected your identity verification. Please retry or select another Identity Provider."),
            eq(true),
            any(),
            eq(null),
            any(),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS1000 and default status`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-1000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "enter-else",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).navigateToIdpList()
    }

    @Test
    fun `test getNDID with CUS3001 and user-select-idp`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "user-select-idp",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS3001 and idp-pending`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS3001 and idp-confirmed-accept`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS3001 and non-pending status`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-data-completed",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(responseNdidStatus)
    }

    @Test
    fun `test getNDID with CUS3001 and as-data-completed`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-data-completed",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).navigateToSuccess(
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(responseNdidStatus.data.referenceId),
            eq(responseNdidStatus.data.ndidStatusNdidData.requestId)
        )
    }

    @Test
    fun `test getNDID with CUS3001 and idp-errored`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageEn),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS3001 and as-errored english`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        HandleCallback.language = "en"
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageEn),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS3001 and as-errored thai`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        HandleCallback.language = "th"
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageTh),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS3001 and idp-pending-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS3001 and idp-confirmed-accept-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS3001 and ndid-connection-fail`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "ndid-connection-fail",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS3001 and idp-confirmed-reject`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-reject",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(any())).thenReturn("You have rejected your identity verification. Please retry or select another Identity Provider.")
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq("You have rejected your identity verification. Please retry or select another Identity Provider."),
            eq(true),
            any(),
            eq(null),
            any(),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS3001 and default status`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-3001",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "enter-else",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).navigateToIdpList()
    }

    @Test
    fun `test getNDID with CUS7301 and user-select-idp`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "user-select-idp",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS7301 and idp-pending`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS7301 and idp-confirmed-accept`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnrollment(any())
    }

    @Test
    fun `test getNDID with CUS7301 and non-pending status`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-data-completed",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleNotPendingNdidStatus(responseNdidStatus)
    }

    @Test
    fun `test getNDID with CUS7301 and as-data-completed`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-data-completed",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).navigateToSuccess(
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(responseNdidStatus.data.referenceId),
            eq(responseNdidStatus.data.ndidStatusNdidData.requestId)
        )
    }

    @Test
    fun `test getNDID with CUS7301 and idp-errored`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageEn),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS7301 and as-errored english`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        HandleCallback.language = "en"
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageEn),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS7301 and as-errored thai`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "as-errored",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        HandleCallback.language = "th"
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq(responseNdidStatus.data.ndidError.messageTh),
            eq(true),
            any(),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS7301 and idp-pending-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-pending-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS7301 and idp-confirmed-accept-timeout`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-accept-timeout",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS7301 and ndid-connection-fail`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "ndid-connection-fail",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).handleCallback(
            eq(false),
            eq(responseNdidStatus.description),
            eq(responseNdidStatus.data.status),
            eq(null),
            argWhere { it.requestId == "requestId"}
        )
    }

    @Test
    fun `test getNDID with CUS7301 and idp-confirmed-reject`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "idp-confirmed-reject",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(context.resources).thenReturn(resources)
        whenever(resources.getString(any())).thenReturn("You have rejected your identity verification. Please retry or select another Identity Provider.")
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showDialog(
            eq("You have rejected your identity verification. Please retry or select another Identity Provider."),
            eq(true),
            any(),
            eq(null),
            any(),
            eq(null)
        )
    }

    @Test
    fun `test getNDID with CUS7301 and default status`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-7301",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "enter-else",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.handleNdidNotPendingStatus(responseNdidStatus)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).navigateToIdpList()
    }

    @Test
    fun `test getNDID with CUS2003`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-2003",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "status",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToIdpList()
    }

    @Test
    fun `test getNDID with other code`() {
        //given
        val responseNdidStatus = NdidStatus(
            code = "CUS-KYC-0000",
            description = "Success",
            data = NdidStatusData(
                sessionId = "sessionId",
                referenceId = "referenceId",
                status = "status",
                expireTime = 100,
                idpStatus = IdpStatus(
                    nodeId = "nodeId",
                    industryCode = "industryCode",
                    companyCode = "companyCode",
                    shortName = "shortName",
                    marketingNameEn = "marketingNameEn",
                    marketingNameTh = "marketingNameTh",
                    smallIconPath = "smallIconPath",
                    mediumIconPath = "mediumIconPath",
                    largeIconPath = "largeIconPath",
                    deepLinkIos = "deepLinkIos",
                    deepLinkAndroid = "deepLinkAndroid",
                    deepLinkHuawei = "deepLinkHuawei"
                ),
                ndidError = NdidErrorStatus(
                    code = "100",
                    description = "description",
                    messageEn = "messageEn",
                    messageTh = "messageTh"
                ),
                ndidStatusNdidData = NdidStatusNdidData(
                    requestId = "requestId"
                )
            )
        )

        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(Single.just(responseNdidStatus))
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleErrorEkyc(eq(responseNdidStatus.code))
    }

    @Test
    fun `test getNDID throw`() {
        //given
        whenever(ndidRepositoryMock.getNdidStatus(
            any()
        ))
            .thenReturn(
                Single.error(
                    Exception("Mock Error")
                )
            )
        Mockito.`when`(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)

        //when
        presenter.getNdidStatus()
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        //then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleHttpException(any())
    }
}