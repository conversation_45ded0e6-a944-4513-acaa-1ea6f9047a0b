package com.scb.techx.ekycframework.data.facetec.mapper

import com.scb.techx.ekycframework.data.facetec.mapper.response.GetEnrollment3DResponseMapperEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Enrollment3DResponseEntity
import com.scb.techx.ekycframework.data.facetec.model.response.Enrollment3DDataEntity
import org.junit.Assert
import org.junit.Test

class GetEnrollment3DResponseMapperEntityTest {
    private val mapper: GetEnrollment3DResponseMapperEntity = GetEnrollment3DResponseMapperEntity()

    @Test
    fun `when convert enrollment3DResponseEntityFull`() {
        //given
        val enrollment3DResponseEntityFull: Enrollment3DResponseEntity = Enrollment3DResponseEntity(
            code = "code",
            description = "description",
            data = Enrollment3DDataEntity(
                scanResultBlob = "scanResultBlob",
                wasProcessed = true
            )
        )

        //when
        val result = mapper.mapFromEntity(enrollment3DResponseEntityFull)

        //then
        Assert.assertEquals(enrollment3DResponseEntityFull.code, result.code)
        Assert.assertEquals(enrollment3DResponseEntityFull.description, result.description)
        Assert.assertEquals(
            enrollment3DResponseEntityFull.data?.scanResultBlob,
            result.data?.scanResultBlob
        )
        Assert.assertEquals(
            enrollment3DResponseEntityFull.data?.wasProcessed,
            result.data?.wasProcessed,
        )
    }

    @Test
    fun `when convert enrollment3DResponseEntityNull`() {
        //given
        val enrollment3DResponseEntityNull: Enrollment3DResponseEntity = Enrollment3DResponseEntity(
            code = "code",
            description = "description",
            data = null
        )

        //when
        val result = mapper.mapFromEntity(enrollment3DResponseEntityNull)

        //then
        Assert.assertEquals(enrollment3DResponseEntityNull.code, result.code)
        Assert.assertEquals(enrollment3DResponseEntityNull.description, result.description)
        Assert.assertEquals(
            null,
            result.data?.scanResultBlob
        )
        Assert.assertEquals(
            null,
            result.data?.wasProcessed,
        )
        Assert.assertEquals(
            null,
            result.data,
        )
    }
}