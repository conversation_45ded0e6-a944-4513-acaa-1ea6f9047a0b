package com.scb.techx.ekycframework.ui.ocridcard

import android.content.Context
import android.content.SharedPreferences
import com.nhaarman.mockito_kotlin.*
import com.scb.techx.ekycframework.ui.processor.Config
import com.scb.techx.ekycframework.domain.ocridcard.repository.OcrIdCardRepository
import com.scb.techx.ekycframework.Constants
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowData
import com.scb.techx.ekycframework.domain.ocridcard.model.InitFlowResponse
import com.scb.techx.ekycframework.ui.ocridcard.presenter.OcrIdCardContract
import com.scb.techx.ekycframework.ui.ocridcard.presenter.OcrIdCardPresenter
import com.scb.techx.ekycframework.domain.common.usecase.EkycPreferenceUtil
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.TestScheduler
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.ResponseBody
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import retrofit2.HttpException
import retrofit2.Response
import java.util.concurrent.TimeUnit

class OcrIdCardPresenterTest {
    private lateinit var presenter: OcrIdCardPresenter
    private lateinit var pref: EkycPreferenceUtil

    @Mock
    lateinit var editor: SharedPreferences.Editor

    @Mock
    lateinit var context: Context

    @Mock
    lateinit var sharedPreferences: SharedPreferences

    @Mock
    lateinit var view: OcrIdCardContract.View

    @Mock
    lateinit var repository: OcrIdCardRepository

    private lateinit var testScheduler: TestScheduler

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)

        pref = EkycPreferenceUtil(context)
        Config.baseUrl = "https://ekyc-ekyc-alpha.np.scbtechx.io"

        testScheduler = TestScheduler()

        presenter = OcrIdCardPresenter(
            view, pref, testScheduler, testScheduler, repository
        )
    }

    @Test
    fun `when initFlow and code is CUS 1000 and response data is full and camera permission is granted`() {
        val responseInitFlow = InitFlowResponse(
            code = "CUS-KYC-1000",
            description = "description",
            data = InitFlowData(
                sdkEncryptionKeyOcr = "sdkEncryptionKeyOcr",
                sdkEncryptionIv = "sdkEncryptionIv"
            )
        )

        // given
        whenever(repository.getInitFlow(any(), any())).thenReturn(
            Single.just(responseInitFlow)
        )
        whenever(view.isCameraEnable()).thenReturn(true)
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "sdkEncryptionKeyOcr")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "sdkEncryptionIv")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToFrontIdScan()
        verify(editor).putString("sdkEncryptionKeyOcr", "sdkEncryptionKeyOcr")
        verify(editor).putString("sdkEncryptionIv", "sdkEncryptionIv")
    }

    @Test
    fun `when initFlow and code is CUS 1000 and response data is full and camera permission is not granted`() {
        val responseInitFlow = InitFlowResponse(
            code = "CUS-KYC-1000",
            description = "description",
            data = InitFlowData(
                sdkEncryptionKeyOcr = "sdkEncryptionKeyOcr",
                sdkEncryptionIv = "sdkEncryptionIv"
            )
        )

        // given
        whenever(repository.getInitFlow(any(), any())).thenReturn(
            Single.just(responseInitFlow)
        )
        whenever(view.isCameraEnable()).thenReturn(false)
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "sdkEncryptionKeyOcr")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "sdkEncryptionIv")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).navigateToEnableCamera()
        verify(editor).putString("sdkEncryptionKeyOcr", "sdkEncryptionKeyOcr")
        verify(editor).putString("sdkEncryptionIv", "sdkEncryptionIv")
    }

    @Test
    fun `when initFlow and code is CUS 1000 and response data is null`() {
        val responseInitFlow = InitFlowResponse(
            code = "CUS-KYC-1000",
            description = "description",
            data = InitFlowData(
                sdkEncryptionKeyOcr = "",
                sdkEncryptionIv = ""
            )
        )

        // given
        whenever(repository.getInitFlow(any(), any())).thenReturn(
            Single.just(responseInitFlow)
        )
        whenever(view.isCameraEnable()).thenReturn(true)
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleCallback(
            success = eq(false),
            description = eq("description"),
            dopaResult = eq(null),
            userConfirmedValue = eq(null),
            userOcrValue = eq(null)
        )
    }

    @Test
    fun `when initFlow and code is not CUS 1000`() {
        val responseInitFlow = InitFlowResponse(
            code = "CUS-KYC-1999",
            description = "description",
            data = InitFlowData(
                sdkEncryptionKeyOcr = "",
                sdkEncryptionIv = ""
            )
        )

        // given
        whenever(repository.getInitFlow(any(), any())).thenReturn(
            Single.just(responseInitFlow)
        )
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleCallback(
            eq(false),
            eq("description"),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `when initFlow and response is throw`() {
        val exception = HttpException(Response.error<Any>(409, ResponseBody.create(
            "plain/text".toMediaTypeOrNull(), ""
        )))
        // given
        whenever(repository.getInitFlow(
            any(),
            any()
        ))
            .thenReturn(
                Single.error(
                    exception
                )
            )
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).showLoadingDialog()
        verify(view).hideLoadingDialog()
        verify(view).handleHttpException(eq(exception))
    }

    @Test
    fun `when both key and iv is not empty and camera permission is granted`() {
        // given
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(view.isCameraEnable()).thenReturn(true)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("not empty")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("not empty")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).navigateToFrontIdScan()
    }

    @Test
    fun `when both key and iv is not empty and camera permission is not granted`() {
        // given
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(view.isCameraEnable()).thenReturn(false)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("not empty")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("not empty")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).navigateToEnableCamera()
    }

    @Test
    fun `when either key or iv is empty`() {
        // given
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(view.isCameraEnable()).thenReturn(false)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.getString("sdkEncryptionKeyOcr", "")).thenReturn("not empty")
        whenever(sharedPreferences.getString("sdkEncryptionIv", "")).thenReturn("")

        // when
        presenter.initOcr(ocr = true)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).handleCallback(
            eq(false),
            eq(Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `when http exception is 401`() {
        val throwable = HttpException(
            Response.error<Any>(401, ResponseBody.create(
            "plain/text".toMediaType(), ""
        )))

        // given
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("ekycToken", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sessionFaceTec", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("productionKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("deviceKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("encryptionKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putBoolean("enableConfirmInfo", true)).thenReturn(editor)

        // when
        presenter.handleHttpException(throwable, view)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).handleCallback(
            eq(false),
            eq(Constants.EkycCallbackMessage.INVALID_TOKEN_MESSAGE),
            eq(null),
            eq(null),
            eq(null)
        )
        Assert.assertEquals(pref.ekycToken, "")
        Assert.assertEquals(pref.sessionFaceTec, "")
        Assert.assertEquals(pref.deviceKey, "")
        Assert.assertEquals(pref.encryptionKey, "")
        Assert.assertEquals(pref.productionKey, "")
        Assert.assertEquals(pref.sdkEncryptionKeyOcr, "")
        Assert.assertEquals(pref.sdkEncryptionIv, "")
    }

    @Test
    fun `when http exception is 504`() {
        val throwable = HttpException(
            Response.error<Any>(504, ResponseBody.create(
                "plain/text".toMediaType(), ""
            )))

        // given
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("ekycToken", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sessionFaceTec", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("productionKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("deviceKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("encryptionKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putBoolean("enableConfirmInfo", true)).thenReturn(editor)

        // when
        presenter.handleHttpException(throwable, view)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).handleCallback(
            eq(false),
            eq(Constants.EkycCallbackMessage.TIMEOUT_MESSAGE),
            eq(null),
            eq(null),
            eq(null)
        )
        Assert.assertEquals(pref.ekycToken, "")
        Assert.assertEquals(pref.sessionFaceTec, "")
        Assert.assertEquals(pref.deviceKey, "")
        Assert.assertEquals(pref.encryptionKey, "")
        Assert.assertEquals(pref.productionKey, "")
        Assert.assertEquals(pref.sdkEncryptionKeyOcr, "")
        Assert.assertEquals(pref.sdkEncryptionIv, "")
    }

    @Test
    fun `when http exception is return with other message`() {
        val throwable = HttpException(
            Response.error<Any>(404, ResponseBody.create(
                "plain/text".toMediaType(), ""
            )))

        // given
        whenever(context.getSharedPreferences("EkycSharedPreferences", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("ekycToken", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sessionFaceTec", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("productionKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("deviceKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("encryptionKey", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionKeyOcr", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putString("sdkEncryptionIv", "")).thenReturn(editor)
        whenever(sharedPreferences.edit().putBoolean("enableConfirmInfo", true)).thenReturn(editor)

        // when
        presenter.handleHttpException(throwable, view)
        testScheduler.advanceTimeBy(6, TimeUnit.SECONDS)

        // then
        verify(view).handleCallback(
            eq(false),
            eq(Constants.EkycCallbackMessage.COMMON_ERROR_MESSAGE),
            eq(null),
            eq(null),
            eq(null)
        )
        Assert.assertEquals(pref.ekycToken, "")
        Assert.assertEquals(pref.sessionFaceTec, "")
        Assert.assertEquals(pref.deviceKey, "")
        Assert.assertEquals(pref.encryptionKey, "")
        Assert.assertEquals(pref.productionKey, "")
        Assert.assertEquals(pref.sdkEncryptionKeyOcr, "")
        Assert.assertEquals(pref.sdkEncryptionIv, "")
    }
}