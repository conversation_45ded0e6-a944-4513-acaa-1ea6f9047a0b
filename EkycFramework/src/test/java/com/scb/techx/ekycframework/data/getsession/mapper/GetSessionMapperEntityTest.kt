package com.scb.techx.ekycframework.data.getsession.mapper

import com.scb.techx.ekycframework.data.getsession.model.response.SessionTokenDataEntity
import com.scb.techx.ekycframework.data.getsession.model.response.SessionTokenResponseEntity
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations

class GetSessionMapperEntityTest {
    val mapper = GetSessionResponseMapperEntity()

    private val getSessionResponseMock = SessionTokenResponseEntity(
        code = "CUS_1000",
        description = "Success",
        data = SessionTokenDataEntity(
            ekycToken = "ekycToken"
        )
    )

    private val getSessionResponseMockDataNull = SessionTokenResponseEntity(
        code = "CUS_1000",
        description = "Success",
        data = null
    )

    private val getSessionResponseMockTokenNull = SessionTokenResponseEntity(
        code = "CUS_1000",
        description = "Success",
        data = SessionTokenDataEntity(
            ekycToken = null
        )
    )

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Test
    fun `when convert full get session response`() {
        val result = mapper.mapFromEntity(getSessionResponseMock)

        Assert.assertEquals(getSessionResponseMock.code, result.code)
        Assert.assertEquals(getSessionResponseMock.description, result.description)
        Assert.assertEquals(getSessionResponseMock.data?.ekycToken, result.data?.ekycToken)
    }

    @Test
    fun `when convert null data get session response`() {
        val result = mapper.mapFromEntity(getSessionResponseMockDataNull)

        Assert.assertEquals(getSessionResponseMockDataNull.code, result.code)
        Assert.assertEquals(getSessionResponseMockDataNull.description, result.description)
        Assert.assertNull(result.data?.ekycToken)
        Assert.assertNull(result.data)
    }

    @Test
    fun `when convert null token get session response`() {
        val result = mapper.mapFromEntity(getSessionResponseMockTokenNull)

        Assert.assertEquals(getSessionResponseMockTokenNull.code, result.code)
        Assert.assertEquals(getSessionResponseMockTokenNull.description, result.description)
        Assert.assertNull(result.data?.ekycToken)
        Assert.assertNotNull(result.data)
    }
}