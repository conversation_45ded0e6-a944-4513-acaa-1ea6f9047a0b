plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'com.kezong.fat-aar'
    id "org.sonarqube" version "3.3"
}

android {
    namespace = "com.scb.techx.ekycframework"
    compileSdk 33

    defaultConfig {
        minSdk 23
        targetSdk 33
        versionCode 1
        versionName project.ext.versionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    // Add this
    packagingOptions {
        pickFirst '**/libPhoenixAndroid.so'
        pickFirst '**/libe9a3.so'
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    testOptions {
        unitTests.returnDefaultValues = true
    }

}

afterEvaluate {
    android.libraryVariants.all { variant ->
        File outputFile = variant.outputs.first().outputFile
        tasks.named("assemble${variant.name.capitalize()}").configure {
            doLast {
                copy {
                    from outputFile
                    into "../app/libs"
                    rename outputFile.name, "EkycFrameworkAndroid_$project.ext.versionName" + ".aar"
                }
            }
        }
        
        if (variant.name.toLowerCase().contains("debug")) {
            tasks.named("extractDeepLinksDebug") {
                dependsOn "explodeEkycProjectFacetec-sdk-${project.ext.facetecVersion}Debug"
            }
        } else if (variant.name.toLowerCase().contains("release")) {
            tasks.named("extractDeepLinksRelease") {
                dependsOn "explodeEkycProjectFacetec-sdk-${project.ext.facetecVersion}Release"
            }
        }
    }
}

fataar {
    /**
     * If transitive is true, local jar module and remote library's dependencies will be embed.
     * If transitive is false, just embed first level dependency
     * Local aar project does not support transitive, always embed first level
     * Default value is false
     * @since 1.3.0
     */
    transitive = true
}

dependencies {
    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'com.google.code.gson:gson:2.8.9'
    implementation 'com.android.support:design:33.1.0'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.3'
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'

    //retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava3:2.9.0'

    //Rx
    implementation 'com.jakewharton.rxbinding4:rxbinding:4.0.0'
    implementation "io.reactivex.rxjava3:rxandroid:3.0.0"
    implementation "io.reactivex.rxjava3:rxkotlin:3.0.0"
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'

    //glide
    implementation 'com.github.bumptech.glide:glide:4.13.0'

    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    testImplementation 'com.nhaarman:mockito-kotlin:1.5.0'
    testImplementation("org.mockito:mockito-core:4.1.0")
    testImplementation("org.mockito:mockito-inline:4.1.0")
    testImplementation("com.android.support.test:runner:1.0.0")
    testImplementation("com.android.support.test:rules:1.0.0")
    androidTestCompileOnly("org.mockito:mockito-android:2.8.47")
    testImplementation("com.android.support.test.espresso:espresso-intents:3.4.0")
    testImplementation("com.android.support.test.espresso:espresso-contrib:3.4.0")

    embed project(":facetec")
    testImplementation(project(":facetec"))
}

ext {
    PUBLISH_GROUP_ID = 'com.scb.techx.ekycframework'
    PUBLISH_VERSION = project.ext.versionName
    PUBLISH_ARTIFACT_ID = 'ekyc'
}

apply from: "publish-module.gradle"