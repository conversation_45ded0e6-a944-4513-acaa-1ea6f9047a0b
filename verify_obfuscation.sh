#!/bin/bash

# eKYC Framework Obfuscation Verification Script
# This script verifies that library-level obfuscation is working correctly

echo "🔍 eKYC Framework Obfuscation Verification"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Step 1: Clean and build
echo -e "\n${BLUE}Step 1: Building obfuscated AAR${NC}"
echo "--------------------------------"

print_info "Cleaning previous builds..."
./gradlew clean > /dev/null 2>&1
print_status $? "Clean completed"

print_info "Building release AAR with obfuscation..."
./gradlew :EkycFramework:assembleRelease
BUILD_STATUS=$?
print_status $BUILD_STATUS "AAR build completed"

if [ $BUILD_STATUS -ne 0 ]; then
    echo -e "${RED}Build failed. Please check the build output above.${NC}"
    exit 1
fi

# Step 2: Verify AAR exists
echo -e "\n${BLUE}Step 2: Verifying AAR output${NC}"
echo "-----------------------------"

AAR_PATH="EkycFramework/build/outputs/aar/EkycFramework-release.aar"
if [ -f "$AAR_PATH" ]; then
    print_status 0 "AAR file exists: $AAR_PATH"
    AAR_SIZE=$(ls -lh "$AAR_PATH" | awk '{print $5}')
    print_info "AAR size: $AAR_SIZE"
else
    print_status 1 "AAR file not found: $AAR_PATH"
    exit 1
fi

# Step 3: Verify mapping file
echo -e "\n${BLUE}Step 3: Verifying obfuscation mapping${NC}"
echo "-------------------------------------"

MAPPING_PATH="EkycFramework/build/outputs/mapping/release/mapping.txt"
if [ -f "$MAPPING_PATH" ]; then
    print_status 0 "Mapping file exists: $MAPPING_PATH"
    
    # Check if internal classes are obfuscated
    OBFUSCATED_REPOS=$(grep -c "datarepository.*->" "$MAPPING_PATH" 2>/dev/null || echo "0")
    OBFUSCATED_MAPPERS=$(grep -c "mapper.*->" "$MAPPING_PATH" 2>/dev/null || echo "0")
    
    if [ "$OBFUSCATED_REPOS" -gt 0 ]; then
        print_status 0 "Repository classes are obfuscated ($OBFUSCATED_REPOS found)"
    else
        print_warning "No obfuscated repository classes found"
    fi
    
    if [ "$OBFUSCATED_MAPPERS" -gt 0 ]; then
        print_status 0 "Mapper classes are obfuscated ($OBFUSCATED_MAPPERS found)"
    else
        print_warning "No obfuscated mapper classes found"
    fi
    
    # Check if public API is preserved
    PUBLIC_API_PRESERVED=$(grep -c "EkycUtilities -> .*EkycUtilities" "$MAPPING_PATH" 2>/dev/null || echo "0")
    if [ "$PUBLIC_API_PRESERVED" -gt 0 ] || ! grep -q "EkycUtilities" "$MAPPING_PATH"; then
        print_status 0 "Public API (EkycUtilities) is preserved"
    else
        print_warning "EkycUtilities might be obfuscated (check manually)"
    fi
    
else
    print_status 1 "Mapping file not found: $MAPPING_PATH"
    print_warning "This might indicate obfuscation is not enabled"
fi

# Step 4: Verify consumer rules
echo -e "\n${BLUE}Step 4: Verifying consumer rules${NC}"
echo "--------------------------------"

CONSUMER_RULES="EkycFramework/consumer-rules.pro"
if [ -f "$CONSUMER_RULES" ]; then
    print_status 0 "Consumer rules file exists"
    
    RULES_COUNT=$(wc -l < "$CONSUMER_RULES")
    print_info "Consumer rules file has $RULES_COUNT lines"
    
    # Check for key rules
    if grep -q "EkycUtilities" "$CONSUMER_RULES"; then
        print_status 0 "EkycUtilities preservation rule found"
    else
        print_warning "EkycUtilities preservation rule not found"
    fi
    
    if grep -q "retrofit2" "$CONSUMER_RULES"; then
        print_status 0 "Retrofit preservation rules found"
    else
        print_warning "Retrofit preservation rules not found"
    fi
    
else
    print_status 1 "Consumer rules file not found: $CONSUMER_RULES"
fi

# Step 5: Test app build
echo -e "\n${BLUE}Step 5: Testing app build with obfuscated AAR${NC}"
echo "---------------------------------------------"

print_info "Building app with obfuscated library..."
./gradlew :app:assembleRelease > /dev/null 2>&1
APP_BUILD_STATUS=$?
print_status $APP_BUILD_STATUS "App build with obfuscated AAR"

if [ $APP_BUILD_STATUS -eq 0 ]; then
    APP_APK="app/build/outputs/apk/*/release/app-*-release.apk"
    if ls $APP_APK 1> /dev/null 2>&1; then
        print_status 0 "App APK generated successfully"
        APK_SIZE=$(ls -lh $APP_APK | head -1 | awk '{print $5}')
        print_info "APK size: $APK_SIZE"
    else
        print_warning "App APK not found in expected location"
    fi
fi

# Step 6: Summary
echo -e "\n${BLUE}Summary${NC}"
echo "======="

if [ -f "$MAPPING_PATH" ] && [ $BUILD_STATUS -eq 0 ] && [ $APP_BUILD_STATUS -eq 0 ]; then
    echo -e "${GREEN}✅ Library-level obfuscation is working correctly!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Test the app on a device to verify runtime functionality"
    echo "2. Check that all eKYC features work as expected"
    echo "3. Verify no crashes occur during API calls"
    echo "4. Test callback interfaces and theme customization"
else
    echo -e "${RED}❌ Issues detected with obfuscation setup${NC}"
    echo ""
    echo "Please review:"
    echo "1. Build configuration in EkycFramework/build.gradle"
    echo "2. ProGuard rules in proguard-rules.pro"
    echo "3. Consumer rules in consumer-rules.pro"
    echo "4. Build output for any errors or warnings"
fi

echo ""
echo "For detailed analysis, check these files:"
echo "- Build output: EkycFramework/build/outputs/"
echo "- Mapping file: $MAPPING_PATH"
echo "- Consumer rules: $CONSUMER_RULES"
echo ""
echo "📖 See LIBRARY_OBFUSCATION_GUIDE.md for detailed testing procedures"
