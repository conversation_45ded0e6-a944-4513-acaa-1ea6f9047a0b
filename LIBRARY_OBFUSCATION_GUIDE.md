# eKYC Framework Library-Level Obfuscation Guide

## Overview

The eKYC framework now builds with obfuscation enabled at the library level, meaning the AAR file contains obfuscated code while maintaining a clean public API surface for consuming applications.

## Changes Made

### 1. Build Configuration Updates

**EkycFramework/build.gradle:**
- ✅ Enabled `minifyEnabled true` for release builds
- ✅ Added debug build type with `minifyEnabled false` for development
- ✅ Ensured `consumerProguardFiles` is properly configured

### 2. ProGuard Rules Strategy

**Three-Layer Protection:**

1. **Library Rules (`proguard-rules.pro`)**: Applied during AAR build
   - Preserves public API surface
   - Obfuscates internal implementation
   - Maintains Retrofit + RxJava functionality

2. **Consumer Rules (`consumer-rules.pro`)**: Applied to consuming apps
   - Ensures proper integration with obfuscated library
   - Preserves essential framework classes
   - Maintains third-party library compatibility

3. **App Rules (`app/proguard-rules.pro`)**: Additional app-level safety

### 3. Public API Preservation

**Classes Preserved (Not Obfuscated):**
- `EkycUtilities` - Main entry point
- `EkycUtilities$*` - All callback interfaces
- `Config` and `Config$*` - Configuration classes
- `HandleCallback` - Callback handler
- `Constants` and `Constants$*` - Framework constants
- `com.scb.techx.ekycframework.ui.theme.**` - Theme customization
- All `@Keep` annotated classes

**Classes Obfuscated (Internal Implementation):**
- Repository implementations
- Mapper classes
- Processors and presenters
- Internal utility classes
- API interface implementations (but not signatures)

## Testing Procedures

### Step 1: Build Obfuscated AAR

```bash
# Clean previous builds
./gradlew clean

# Build release AAR with obfuscation
./gradlew :EkycFramework:assembleRelease

# Verify AAR is created
ls -la EkycFramework/build/outputs/aar/
```

### Step 2: Verify Obfuscation Applied

**Check mapping file:**
```bash
# Library mapping file should exist
ls -la EkycFramework/build/outputs/mapping/release/mapping.txt

# Verify internal classes are obfuscated
grep "datarepository" EkycFramework/build/outputs/mapping/release/mapping.txt
grep "mapper" EkycFramework/build/outputs/mapping/release/mapping.txt
```

**Expected in mapping.txt:**
```
# Public API should NOT be obfuscated:
com.scb.techx.ekycframework.util.EkycUtilities -> com.scb.techx.ekycframework.util.EkycUtilities

# Internal classes SHOULD be obfuscated:
com.scb.techx.ekycframework.data.getsession.datarepository.GetSessionDataRepository -> a.b.c.d
com.scb.techx.ekycframework.data.getsession.mapper.GetSessionResponseMapperEntity -> a.b.c.e
```

### Step 3: Test AAR Integration

**Build consuming app:**
```bash
# Build app with obfuscated library
./gradlew :app:assembleRelease

# Check for ProGuard warnings
grep -i "warning" app/build/outputs/mapping/release/configuration.txt
```

### Step 4: Runtime Verification

**Test all public API methods:**
1. `EkycUtilities.initEkyc()` - Should work without errors
2. All callback interfaces - Should receive proper responses
3. Theme customization - Should apply correctly
4. API calls - Should complete successfully without type errors

**Verify no crashes occur:**
- No `IllegalArgumentException` for call adapters
- No `IllegalStateException` for Single parameterization
- No `ClassNotFoundException` for obfuscated classes
- No reflection errors for preserved classes

### Step 5: Verify Consumer Rules Applied

**Check app's ProGuard configuration:**
```bash
# Consumer rules should be automatically included
grep -i "consumer" app/build/outputs/mapping/release/configuration.txt
```

## Expected Benefits

### 1. Enhanced Security
- Internal implementation details are obfuscated
- API logic is protected from reverse engineering
- Sensitive algorithms and business logic are hidden

### 2. Maintained Functionality
- Public API remains accessible and functional
- All callback interfaces work correctly
- Theme customization continues to work
- Retrofit + RxJava integration preserved

### 3. Seamless Integration
- Consumer apps don't need to change their code
- ProGuard rules are automatically applied
- No additional configuration required for consuming apps

## Troubleshooting

### Issue: ClassNotFoundException at Runtime
**Solution:** Check if the missing class should be in consumer-rules.pro

### Issue: Retrofit Call Adapter Errors
**Solution:** Verify generic type preservation rules are applied

### Issue: Gson Serialization Failures
**Solution:** Ensure @SerializedName fields are preserved

### Issue: Callback Interface Not Working
**Solution:** Verify callback interfaces are in public API preservation rules

## Verification Checklist

- [ ] AAR builds successfully with obfuscation enabled
- [ ] Mapping file shows internal classes are obfuscated
- [ ] Public API classes are NOT obfuscated
- [ ] Consumer rules are automatically applied to apps
- [ ] All API calls work without type errors
- [ ] Callback interfaces function correctly
- [ ] Theme customization works
- [ ] No runtime crashes related to obfuscation
- [ ] App builds successfully with obfuscated AAR
- [ ] All eKYC features work as expected

## File Structure

```
EkycFramework/
├── build.gradle                 # minifyEnabled true for release
├── proguard-rules.pro          # Library-level obfuscation rules
├── consumer-rules.pro          # Auto-applied to consuming apps
└── build/outputs/
    ├── aar/                    # Obfuscated AAR files
    └── mapping/release/        # Obfuscation mapping
        └── mapping.txt
```

This configuration provides maximum protection for your framework's internal implementation while maintaining a clean, functional public API for consuming applications.
