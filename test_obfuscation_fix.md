# eKYC Framework Obfuscation Fix - Testing Guide

## Problem Summary
The eKYC framework was experiencing runtime crashes when obfuscation was enabled due to:
- Loss of generic type information in Retrofit + RxJava integration
- Missing ProGuard rules for preserving API interface signatures
- Insufficient protection of critical framework classes

## Solution Applied

### 1. Enhanced ProGuard Rules
- **consumer-rules.pro**: Added comprehensive rules that automatically apply to consuming apps
- **proguard-rules.pro**: Updated both library and app rules for consistency
- **Key protections added**:
  - Retrofit interfaces and method signatures
  - RxJava generic type preservation
  - Gson serialization classes
  - eKYC framework specific classes

### 2. Added @Keep Annotations
- `GetSessionDataRepository` class
- `GetSessionResponseMapperEntity` class
- All API response/request entities already had @Keep

### 3. Generic Type Preservation
- Added `-keepattributes Signature` rules
- Protected parameterized types in API interfaces
- Preserved RxJava Single<T> type information

## Testing Instructions

### Step 1: Clean Build
```bash
./gradlew clean
```

### Step 2: Build Release with Obfuscation
```bash
./gradlew assembleRelease
```

### Step 3: Verify ProGuard Rules Applied
Check the build output for:
- No warnings about missing classes in eKYC framework
- Successful R8/ProGuard processing
- Generated mapping.txt should show preserved API classes

### Step 4: Runtime Testing
1. Install the release APK on a device
2. Trigger the `getSessionToken()` method call
3. Verify no `IllegalArgumentException` or `IllegalStateException` occurs
4. Check that the API call completes successfully

### Step 5: Verify Specific Classes Preserved
In the generated `mapping.txt` file, verify these classes are NOT obfuscated:
- `com.scb.techx.ekycframework.data.getsession.api.GetSessionAPI`
- `com.scb.techx.ekycframework.data.getsession.datarepository.GetSessionDataRepository`
- `com.scb.techx.ekycframework.data.getsession.model.response.SessionTokenResponseEntity`
- `com.scb.techx.ekycframework.domain.getsession.model.SessionTokenResponse`

## Expected Results

### Before Fix
```
java.lang.IllegalArgumentException: Unable to create call adapter for class o2.g 
for method GetSessionAPI.getSessionToken
Caused by: java.lang.IllegalStateException: Single return type must be parameterized 
as Single<Foo> or Single<? extends Foo>
```

### After Fix
- No runtime exceptions
- Successful API calls
- Proper type resolution in Retrofit + RxJava integration

## Additional Verification

### Check ProGuard Mapping
Look for these patterns in `app/build/outputs/mapping/release/mapping.txt`:
```
# Classes should NOT be obfuscated (should appear as-is):
com.scb.techx.ekycframework.data.getsession.api.GetSessionAPI -> com.scb.techx.ekycframework.data.getsession.api.GetSessionAPI
```

### Verify Consumer Rules Applied
The consumer rules from the library should automatically be applied to the app build.
Check build logs for: "Applying consumer proguard rules from..."

## Troubleshooting

If issues persist:

1. **Check R8 Full Mode**: Ensure you're not using R8 full mode without proper rules
2. **Verify Dependencies**: Ensure all Retrofit and RxJava dependencies are compatible
3. **Add Debug Logging**: Temporarily add logging to verify which classes are being called
4. **Check Mapping File**: Verify critical classes are preserved in the mapping output

## Files Modified

1. `EkycFramework/consumer-rules.pro` - Added comprehensive consumer rules
2. `EkycFramework/proguard-rules.pro` - Enhanced library rules
3. `app/proguard-rules.pro` - Added supplementary app rules
4. `GetSessionDataRepository.kt` - Added @Keep annotation
5. `GetSessionResponseMapperEntity.kt` - Added @Keep annotation

## Key ProGuard Rules Added

- Retrofit interface preservation
- RxJava generic type preservation  
- Gson serialization protection
- eKYC framework class protection
- Generic signature preservation
- Method signature preservation for debugging
