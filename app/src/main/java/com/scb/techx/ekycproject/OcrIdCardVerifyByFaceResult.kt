package com.scb.techx.ekycproject

import android.os.Parcelable
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult
import kotlinx.parcelize.Parcelize

@Parcelize
data class OcrIdCardVerifyByFaceResult(
    var success: Boolean,
    var description: String,
    var userOcrValue: UserConfirmedValue?,
    var userConfirmedValue: UserConfirmedValue?,
    var dopaResult: DopaResult?
) : Parcelable