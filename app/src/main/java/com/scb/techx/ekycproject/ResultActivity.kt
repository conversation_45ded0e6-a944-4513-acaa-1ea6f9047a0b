package com.scb.techx.ekycproject

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class ResultActivity : AppCompatActivity() {
    private val tvField1Title: TextView by lazy { findViewById(R.id.tv_field1_title) }
    private val tvField2Title: TextView by lazy { findViewById(R.id.tv_field2_title) }
    private val tvField3Title: TextView by lazy { findViewById(R.id.tv_field3_title) }
    private val tvField4Title: TextView by lazy { findViewById(R.id.tv_field4_title) }
    private val tvField5Title: TextView by lazy { findViewById(R.id.tv_field5_title) }
    private val tvField6Title: TextView by lazy { findViewById(R.id.tv_field6_title) }
    private val tvField7Title: TextView by lazy { findViewById(R.id.tv_field7_title) }
    private val tvField8Title: TextView by lazy { findViewById(R.id.tv_field8_title) }
    private val tvField9Title: TextView by lazy { findViewById(R.id.tv_field9_title) }
    private val tvField10Title: TextView by lazy { findViewById(R.id.tv_field10_title) }
    private val tvField11Title: TextView by lazy { findViewById(R.id.tv_field11_title) }
    private val tvField12Title: TextView by lazy { findViewById(R.id.tv_field12_title) }
    private val tvField13Title: TextView by lazy { findViewById(R.id.tv_field13_title) }
    private val tvField14Title: TextView by lazy { findViewById(R.id.tv_field14_title) }
    private val tvField15Title: TextView by lazy { findViewById(R.id.tv_field15_title) }
    private val tvField16Title: TextView by lazy { findViewById(R.id.tv_field16_title) }
    private val tvField17Title: TextView by lazy { findViewById(R.id.tv_field17_title) }
    private val tvField1Data: TextView by lazy { findViewById(R.id.tv_field1_data) }
    private val tvField2Data: TextView by lazy { findViewById(R.id.tv_field2_data) }
    private val tvField3Data: TextView by lazy { findViewById(R.id.tv_field3_data) }
    private val tvField4Data: TextView by lazy { findViewById(R.id.tv_field4_data) }
    private val tvField5Data: TextView by lazy { findViewById(R.id.tv_field5_data) }
    private val tvField6Data: TextView by lazy { findViewById(R.id.tv_field6_data) }
    private val tvField7Data: TextView by lazy { findViewById(R.id.tv_field7_data) }
    private val tvField8Data: TextView by lazy { findViewById(R.id.tv_field8_data) }
    private val tvField9Data: TextView by lazy { findViewById(R.id.tv_field9_data) }
    private val tvField10Data: TextView by lazy { findViewById(R.id.tv_field10_data) }
    private val tvField11Data: TextView by lazy { findViewById(R.id.tv_field11_data) }
    private val tvField12Data: TextView by lazy { findViewById(R.id.tv_field12_data) }
    private val tvField13Data: TextView by lazy { findViewById(R.id.tv_field13_data) }
    private val tvField14Data: TextView by lazy { findViewById(R.id.tv_field14_data) }
    private val tvField15Data: TextView by lazy { findViewById(R.id.tv_field15_data) }
    private val tvField16Data: TextView by lazy { findViewById(R.id.tv_field16_data) }
    private val tvField17Data: TextView by lazy { findViewById(R.id.tv_field17_data) }

    companion object {
        private var RESULT_TYPE = "RESULT_TYPE"
        private var NDID_CALLBACK_RESULT = "NDID_CALLBACK_RESULT"
        private var OCR_ID_CARD_VERIFY_BY_FACE_RESULT = "OCR_ID_CARD_VERIFY_BY_FACE_RESULT"

        @JvmStatic
        fun startActivity(
            context: Context?,
            result: NdidCallbackResult?,
            resultType: ResultType?
        ) {
            context?.let {
                val intent = Intent(context, ResultActivity::class.java)

                intent.putExtra(NDID_CALLBACK_RESULT, result)
                intent.putExtra(RESULT_TYPE, resultType)
                it.startActivity(intent)
            }
        }

        @JvmStatic
        fun startActivity(
            context: Context?,
            result: OcrIdCardVerifyByFaceResult?,
            resultType: ResultType?
        ) {
            context?.let {
                val intent = Intent(context, ResultActivity::class.java)

                intent.putExtra(OCR_ID_CARD_VERIFY_BY_FACE_RESULT, result)
                intent.putExtra(RESULT_TYPE, resultType)
                it.startActivity(intent)
            }
        }
    }

    private fun emptyAllField() {
        tvField1Title.visibility = View.INVISIBLE
        tvField2Title.visibility = View.INVISIBLE
        tvField3Title.visibility = View.INVISIBLE
        tvField4Title.visibility = View.INVISIBLE
        tvField5Title.visibility = View.INVISIBLE
        tvField6Title.visibility = View.INVISIBLE
        tvField7Title.visibility = View.INVISIBLE
        tvField8Title.visibility = View.INVISIBLE
        tvField9Title.visibility = View.INVISIBLE
        tvField10Title.visibility = View.INVISIBLE
        tvField11Title.visibility = View.INVISIBLE
        tvField12Title.visibility = View.INVISIBLE
        tvField13Title.visibility = View.INVISIBLE
        tvField14Title.visibility = View.INVISIBLE
        tvField15Title.visibility = View.INVISIBLE
        tvField16Title.visibility = View.INVISIBLE
        tvField17Title.visibility = View.INVISIBLE
        tvField1Data.visibility = View.INVISIBLE
        tvField2Data.visibility = View.INVISIBLE
        tvField3Data.visibility = View.INVISIBLE
        tvField4Data.visibility = View.INVISIBLE
        tvField5Data.visibility = View.INVISIBLE
        tvField6Data.visibility = View.INVISIBLE
        tvField7Data.visibility = View.INVISIBLE
        tvField8Data.visibility = View.INVISIBLE
        tvField9Data.visibility = View.INVISIBLE
        tvField10Data.visibility = View.INVISIBLE
        tvField11Data.visibility = View.INVISIBLE
        tvField12Data.visibility = View.INVISIBLE
        tvField13Data.visibility = View.INVISIBLE
        tvField14Data.visibility = View.INVISIBLE
        tvField15Data.visibility = View.INVISIBLE
        tvField16Data.visibility = View.INVISIBLE
        tvField17Data.visibility = View.INVISIBLE
    }

    private fun ndidResultTextSetting(result: NdidCallbackResult) {
        tvField1Title.text = "success:"
        tvField2Title.text = "description:"
        tvField3Title.text = "ndidStatus:"
        tvField4Title.text = "ndidError.code:"
        tvField1Data.text = result.success.toString()
        tvField2Data.text = result.description
        tvField3Data.text = if (result.ndidStatus == null) "null" else result.ndidStatus
        tvField4Data.text = if (result.ndidError == null) "null" else result.ndidError
        setNdidVisibility()
    }

    private fun setNdidVisibility() {
        tvField1Title.visibility = View.VISIBLE
        tvField2Title.visibility = View.VISIBLE
        tvField3Title.visibility = View.VISIBLE
        tvField4Title.visibility = View.VISIBLE
        tvField1Data.visibility = View.VISIBLE
        tvField2Data.visibility = View.VISIBLE
        tvField3Data.visibility = View.VISIBLE
        tvField4Data.visibility = View.VISIBLE
    }

    private fun ocrResultTextSetting(result: OcrIdCardVerifyByFaceResult) {
        tvField1Title.text = "success:"
        tvField2Title.text = "description:"
        tvField3Title.text = "ocrValue.nationalId:"
        tvField4Title.text = "ocrValue.titleTh:"
        tvField5Title.text = "ocrValue.firstNameTh:"
        tvField6Title.text = "ocrValue.middleTh:"
        tvField7Title.text = "ocrValue.lastnameTh:"
        tvField8Title.text = "ocrValue.titleEn:"
        tvField9Title.text = "ocrValue.firstNameEn:"
        tvField10Title.text = "ocrValue.middleEn:"
        tvField11Title.text = "ocrValue.lastnameEn:"
        tvField12Title.text = "ocrValue.dateOfBirth:"
        tvField13Title.text = "ocrValue.dateOfIssue:"
        tvField14Title.text = "ocrValue.dateOfExpiry:"
        tvField15Title.text = "ocrValue.laserId:"
        tvField16Title.text = "dopaResult.code:"
        tvField17Title.text = "dopaResult.desc:"
        tvField1Data.text = result.success.toString()
        tvField2Data.text = result.description
        tvField3Data.text = if (result.userConfirmedValue?.nationalId != null) {
            result.userConfirmedValue?.nationalId
        } else if (result.userOcrValue?.nationalId != null) {
            result.userOcrValue?.nationalId
        } else {
            "null"
        }
        tvField4Data.text = if (result.userConfirmedValue?.titleTh != null) {
            result.userConfirmedValue?.titleTh
        } else if (result.userOcrValue?.titleTh != null) {
            result.userOcrValue?.titleTh
        } else {
            "null"
        }
        tvField5Data.text = if (result.userConfirmedValue?.firstNameTh != null) {
            result.userConfirmedValue?.firstNameTh
        } else if (result.userOcrValue?.firstNameTh != null) {
            result.userOcrValue?.firstNameTh
        } else {
            "null"
        }
        tvField6Data.text = if (result.userConfirmedValue?.middleNameTh != null) {
            result.userConfirmedValue?.middleNameTh
        } else if (result.userOcrValue?.middleNameTh != null) {
            result.userOcrValue?.middleNameTh
        } else {
            "null"
        }
        tvField7Data.text = if (result.userConfirmedValue?.lastNameTh != null) {
            result.userConfirmedValue?.lastNameTh
        } else if (result.userOcrValue?.lastNameTh != null) {
            result.userOcrValue?.lastNameTh
        } else {
            "null"
        }
        tvField8Data.text = if (result.userConfirmedValue?.titleEn != null) {
            result.userConfirmedValue?.titleEn
        } else if (result.userOcrValue?.titleEn != null) {
            result.userOcrValue?.titleEn
        } else {
            "null"
        }
        tvField9Data.text = if (result.userConfirmedValue?.firstNameEn != null) {
            result.userConfirmedValue?.firstNameEn
        } else if (result.userOcrValue?.firstNameEn != null) {
            result.userOcrValue?.firstNameEn
        } else {
            "null"
        }
        tvField10Data.text = if (result.userConfirmedValue?.middleNameEn != null) {
            result.userConfirmedValue?.middleNameEn
        } else if (result.userOcrValue?.middleNameEn != null) {
            result.userOcrValue?.middleNameEn
        } else {
            "null"
        }
        tvField11Data.text = if (result.userConfirmedValue?.lastNameEn != null) {
            result.userConfirmedValue?.lastNameEn
        } else if (result.userOcrValue?.lastNameEn != null) {
            result.userOcrValue?.lastNameEn
        } else {
            "null"
        }
        tvField12Data.text = if (result.userConfirmedValue?.dateOfBirth != null) {
            result.userConfirmedValue?.dateOfBirth
        } else if (result.userOcrValue?.dateOfBirth != null) {
            result.userOcrValue?.dateOfBirth
        } else {
            "null"
        }
        tvField13Data.text = if (result.userConfirmedValue?.dateOfIssue != null) {
            result.userConfirmedValue?.dateOfIssue
        } else if (result.userOcrValue?.dateOfIssue != null) {
            result.userOcrValue?.dateOfIssue
        } else {
            "null"
        }
        tvField14Data.text = if (result.userConfirmedValue?.dateOfExpiry != null) {
            result.userConfirmedValue?.dateOfExpiry
        } else if (result.userOcrValue?.dateOfExpiry != null) {
            result.userOcrValue?.dateOfExpiry
        } else {
            "null"
        }
        tvField15Data.text = if (result.userConfirmedValue?.laserId != null) {
            result.userConfirmedValue?.laserId
        } else if (result.userOcrValue?.laserId != null) {
            result.userOcrValue?.laserId
        } else {
            "null"
        }
        tvField16Data.text = if (result.dopaResult?.code != null) {
            result.dopaResult?.code
        } else {
            "null"
        }
        tvField17Data.text = if (result.dopaResult?.desc != null) {
            result.dopaResult?.desc
        } else {
            "null"
        }

        setOcrVisibility()
    }

    private fun setOcrVisibility() {
        tvField1Title.visibility = View.VISIBLE
        tvField2Title.visibility = View.VISIBLE
        tvField3Title.visibility = View.VISIBLE
        tvField4Title.visibility = View.VISIBLE
        tvField5Title.visibility = View.VISIBLE
        tvField6Title.visibility = View.VISIBLE
        tvField7Title.visibility = View.VISIBLE
        tvField8Title.visibility = View.VISIBLE
        tvField9Title.visibility = View.VISIBLE
        tvField10Title.visibility = View.VISIBLE
        tvField11Title.visibility = View.VISIBLE
        tvField12Title.visibility = View.VISIBLE
        tvField13Title.visibility = View.VISIBLE
        tvField14Title.visibility = View.VISIBLE
        tvField15Title.visibility = View.VISIBLE
        tvField16Title.visibility = View.VISIBLE
        tvField17Title.visibility = View.VISIBLE
        tvField1Data.visibility = View.VISIBLE
        tvField2Data.visibility = View.VISIBLE
        tvField3Data.visibility = View.VISIBLE
        tvField4Data.visibility = View.VISIBLE
        tvField5Data.visibility = View.VISIBLE
        tvField6Data.visibility = View.VISIBLE
        tvField7Data.visibility = View.VISIBLE
        tvField8Data.visibility = View.VISIBLE
        tvField9Data.visibility = View.VISIBLE
        tvField10Data.visibility = View.VISIBLE
        tvField11Data.visibility = View.VISIBLE
        tvField12Data.visibility = View.VISIBLE
        tvField13Data.visibility = View.VISIBLE
        tvField14Data.visibility = View.VISIBLE
        tvField15Data.visibility = View.VISIBLE
        tvField16Data.visibility = View.VISIBLE
        tvField17Data.visibility = View.VISIBLE
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_result)
        val resultType = intent.getSerializableExtra(RESULT_TYPE) as ResultType?

        emptyAllField()
        if (resultType == ResultType.NDID) {
            val result: NdidCallbackResult? = intent.getParcelableExtra(NDID_CALLBACK_RESULT)

            if (result != null) {
                ndidResultTextSetting(result)
            }
        } else if (resultType == ResultType.OCR) {
            val result: OcrIdCardVerifyByFaceResult? =
                intent.getParcelableExtra(OCR_ID_CARD_VERIFY_BY_FACE_RESULT)

            if (result != null) {
                ocrResultTextSetting(result)
            }
        }
    }
}