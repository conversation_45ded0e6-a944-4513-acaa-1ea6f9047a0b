{"TileList": {"test1Tile": "ทดสอบ 1", "test2Tile": "ทดสอบ 2", "auto1Tile": "ทดสอบ ไอดีพี อัตโนมัติ 1", "auto2Tile": "ทดสอบ ไอดีพี อัตโนมัติ 2", "scb": "ธนาคารไทยพาณิชย์"}, "IdpPopUpMessage": {"idp30000": "ผู้ให้บริการยืนยันตัวตนที่คุณเลือกไม่สามารถให้บริการได้ในขณะนี้ กรุณาทำรายการใหม่หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp40000": "ขออภัย คุณไม่สามารถทำรายการได้ในขณะนี้ กรุณาทำรายการใหม่", "noDeepLink": "กรุณาไปยืนยันตัวตนที่โมบายแอปพลิเคชันของผู้ให้บริการที่คุณเลือก", "idpReject": "คุณได้ปฏิเสธการยืนยันตัวตนของผู้ให้บริการยืนยันตัวตนที่คุณเลือก กรุณาทำรายการใหม่หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30200": "ไม่สามารถทำรายการได้ในขณะนี้ กรุณาตรวจสอบเลขบัตรประชาชนของคุณและทำรายการใหม่ หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30300": "ไม่พบข้อมูลของคุณในฐานข้อมูลผู้ใช้บริการของผู้ให้บริการยืนยันตัวตนที่คุณเลือก กรุณาเลือกผู้ให้บริการยืนยันตัวตนรายอื่นที่คุณเคยลงทะเบียนและมีโมบายแอปพลิเคชัน", "idp30400": "ขออภัย ข้อมูลของคุณไม่เป็นปัจจุบัน กรุณาติดต่อผู้ให้บริการยืนยันตัวตนที่คุณเลือกเพื่ออัปเดทข้อมูล หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30500": "การยืนยันตัวตนไม่สำเร็จเนื่องจากคุณระบุ PIN ผิด, ภาพถ่ายไม่ชัดเจน กรุณาทำรายการใหม่ หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30510": "การยืนยันตัวตนไม่สำเร็จเนื่องจากคุณระบุ PIN/Password ไม่ถูกต้อง กรุณาทำรายการใหม่ หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30520": "การยืนยันตัวตนไม่สำเร็จเนื่องจากคุณยืนยันตัวตนด้วยระบบจดจำใบหน้า (Face Recognition) ไม่ผ่านตามเงื่อนไขที่กำหนด กรุณาทำรายการใหม่ หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30530": "การยืนยันตัวตนไม่สำเร็จเนื่องจากคุณระบุรหัส OTP ไม่ถูกต้อง กรุณาทำรายการใหม่ หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30600": "การยืนยันตัวตนไม่สำเร็จ เนื่องจากคุณได้ยกเลิกรายการยืนยันตัวตนที่โมบายแอปพลิเคชันของผู้ให้บริการยืนยันตัวตน กรุณาเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30610": "การยืนยันตัวตนไม่สำเร็จ เนื่องจากคุณไม่ได้ยอมรับเงื่อนไขการให้บริการยืนยันตัวตนของผู้ให้บริการยืนยันตัวตนที่คุณเลือก กรุณาทำรายการใหม่ หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30700": "การยืนยันตัวตนไม่สำเร็จ เนื่องจากผู้ให้บริการยืนยันตัวตนที่คุณเลือกไม่สามารถให้บริการได้ในขณะนี้ กรุณาทำรายการใหม่ หรือเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp30800": "คุณยังไม่ได้ลงทะเบียนและยอมรับเงื่อนไขการใช้บริการ NDID ที่โมบายแอปพลิเคชันของผู้ให้บริการยืนยันตัวตน กรุณาดำเนินการก่อนทำรายการ", "idp30900": "ไม่สามารถทำรายการได้ในขณะนี้ เนื่องจากอยู่นอกเวลาการให้บริการของ ทดสอบ ไอดีพี อัตโนมัติ 1 กรุณาเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp40200": "ขออภัย คุณไม่สามารถดำเนินการต่อได้", "idp40300": "ขออภัย ไม่สามารถดำเนินการต่อได้ กรุณาเลือกผู้ให้บริการยืนยันตัวตนรายอื่น", "idp40400": "ขออภัย ไม่สามารถดำเนินการต่อได้เนื่องจากข้อมูลไม่ถูกต้อง กรุณาทำรายการใหม่อีกครั้ง"}, "Enrollment": {"tvSubHeaderEnrollment": "กรุณายืนยันตัวตนที่โมบายแอปพลิเคชันของผู้ให้บริการยืนยันตัวตนที่คุณได้เลือกไว้", "tvDescriptionEnrollmentMockAuto1": "ท่านกำลังยืนยันตัวตนเพื่อใช้ตามวัตถุประสงค์ของ SCBS และ ประสงค์ให้ส่งข้อมูลจาก ทดสอบ ไอดีพี อัตโนมัติ 1", "tvDescriptionEnrollmentMockAuto2": "ท่านกำลังยืนยันตัวตนเพื่อใช้ตามวัตถุประสงค์ของ SCBS และ ประสงค์ให้ส่งข้อมูลจาก ทดสอบ ไอดีพี อัตโนมัติ 2", "tvDescriptionEnrollmentSCB": "ท่านกำลังยืนยันตัวตนเพื่อใช้ตามวัตถุประสงค์ของ SCBS และ ประสงค์ให้ส่งข้อมูลจาก ธนาคารไทยพาณิชย์", "tvSuccessSubtext": "กรุณาไปยืนยันตัวตนที่โมบายแอปพลิเคชันของผู้ให้บริการที่คุณเลือก ภายใน 60 นาที และกลับมาทำรายการต่อที่นี่", "tvAskCancelDialog": "คุณต้องการยกเลิกการยืนยันตัวตนใช่หรือไม่", "tvAlertCancelDialog": "คุณได้ยกเลิกรายการคำขอหรือเปลี่ยนผู้ให้บริการยืนยันตัวตน กรุณาเลือกผู้ให้บริการยืนยันตัวตน (Identity Provider) รายอื่น"}, "Idp": {"tvBankTitle": "เลือกผู้ให้บริการยืนยันตัวตน (NDID)", "tvDescription": "กรุณาเลือกผู้ให้บริการยืนยันตัวตนที่คุณต้องการลงทะเบียนบริการ NDID (คุณต้องมีโมบายแอปพลิเคชันกับผู้ให้บริการยืนยันตัวตนดังกล่าวอยู่แล้ว)", "tvRegistered": "ผู้ให้บริการยืนยันตัวตนที่เคยลงทะเบียนไว้ สามารถยืนยันตัวตนได้ทันที", "tvUnregistered": "ผู้ให้บริการยืนยันตัวตนอื่นที่คุณสามารถลงทะเบียนได้"}, "Success": {"tvSuccessText": "ยืนยันตัวตนเรียบร้อยแล้ว", "tvSuccessSubtext": "ข้อมูลขอเปิดบัญชีได้ถูกส่งไปแล้ว"}}