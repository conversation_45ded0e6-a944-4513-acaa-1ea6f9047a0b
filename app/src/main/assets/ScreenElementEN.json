{"TileList": {"test1Tile": "Mock 1", "test2Tile": "Mock 2", "auto1Tile": "Mock IdP Auto 1", "auto2Tile": "Mock IdP Auto 2", "scb": "Siam Commercial Bank (SCB)"}, "IdpPopUpMessage": {"idp30000": "The Identity Provider you choose is currently unavailable. Please retry or select another IdP.", "idp40000": "Sorry, your request cannot be proceeded at the moment. Please try again", "noDeepLink": "Please verify your identification at IdP's mobile application.", "idpReject": "You have rejected your identity verification. Please retry or select another Identity Provider.", "idp30200": "Sorry, your request cannot be proceeded. Please check your citizen ID and retry or select another IdP.", "idp30300": "Sorry, your information could not be found. Please select another Identity Provider that you have been registered and have a mobile application activated.", "idp30400": "You select Identity Provider (IdP) that your information is not up to date. Kindly contact that IdP for update later. You may select another IdP", "idp30500": "Identity authentication fails due to invalid PIN, or incorrect Selfie Photo. Please retry or select another IdP.", "idp30510": "Identity authentication fails due to Invalid PIN/Password. Please retry or select another IdP.", "idp30520": "Identity authentication fails due to incorrect Face Recognition. Please retry or select another IdP.", "idp30530": "Identity authentication fails due to Invalid OTP. Please retry or select another Identity Provider.", "idp30600": "Authentication fails due to your cancelling your identity authentication at Identity Provider’s Mobile App. Please select another Identity Provider.", "idp30610": "Your identity authentication fails because you do not accept the terms of service of the Identity Provider that you have selected. Please retry or select another Identity Provider.", "idp30700": "Your identity authentication fails due to unavailable Identity Provider at the moment. Please retry and select another IdP.", "idp30800": "You have not registered NDID Service and accepted T&C. Please complete before proceeding the transaction.", "idp30900": "Cannot proceed the request at the moment due to out of Mock IdP Auto 1 service hours. Please select another IdP.", "idp40200": "Sorry, unable to proceed", "idp40300": "Sorry, the request cannot be proceeded. Please select another IdP.", "idp40400": "Sorry, unable to proceed due to incorrect information. Please retry"}, "Enrollment": {"tvSubHeaderEnrollment": "Please go to the selected Identity Provider's mobile application to authenticate your identity", "tvDescriptionEnrollmentMockAuto1": "You are authenticating you identity in accordance with SCBS' objective and are agreeing to send back your information from Mock IdP Auto 1", "tvDescriptionEnrollmentMockAuto2": "You are authenticating you identity in accordance with SCBS' objective and are agreeing to send back your information from Mock IdP Auto 2", "tvDescriptionEnrollmentSCB": "You are authenticating you identity in accordance with SCBS' objective and are agreeing to send back your information from Siam Commercial Bank (SCB)", "tvSuccessSubtext": "Please verify your identification at [IdP] mobile application within 60 minutes and return to continue your transaction here", "tvAskCancelDialog": "Do you want to cancel identification?", "tvAlertCancelDialog": "You have canceled the request or changed the Identity Provider, please select another Identity Provider."}, "Idp": {"tvBankTitle": "Choose an Identity Provider to register for NDID service", "tvDescription": "Please choose an Identity Provider to register NDID Service (you should already have mobile application with that Identity Provider)", "tvRegistered": "Identity Providers you have previously registered with (Additional verification with the Identity Provider is not required):", "tvUnregistered": "Identity Providers that allow on-the-fly uplifting and enrollment"}, "Success": {"tvSuccessText": "Successfully identity authentication.", "tvSuccessSubtext": "The request to open an account has been sent."}}