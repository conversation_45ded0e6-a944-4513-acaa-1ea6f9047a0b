<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.EkycProject" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" >?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>
    <!--    This will be used when try to overwrite theme-->
    <!--    <style name="Popup" parent="Theme.AppCompat.Dialog.Alert">-->
    <!--        <item name="colorBackgroundFloating">#FFDADA</item>-->
    <!--        <item name="android:textColorPrimary">#990055</item>-->
    <!--        <item name="buttonBarPositiveButtonStyle">@style/PositiveButtonStyle</item>-->
    <!--        <item name="buttonBarNegativeButtonStyle">@style/NegativeButtonStyle</item>-->
    <!--    </style>-->
    <!--    <style name="PositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">-->
    <!--        <item name="android:textColor">#d81b60</item>-->
    <!--        <item name="rippleColor">#ad1457</item>-->
    <!--    </style>-->
    <!--    <style name="NegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">-->
    <!--        <item name="android:textColor">#757575</item>-->
    <!--        <item name="rippleColor">#a4a4a4</item>-->
    <!--    </style>-->
</resources>