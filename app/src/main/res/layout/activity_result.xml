<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ResultActivity">

    <TextView
        android:id="@+id/tv_field1_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="@string/data_placeholder"
        app:layout_constraintStart_toStartOf="@+id/gl_horizontal_half"
        app:layout_constraintTop_toTopOf="@+id/gl_vertical_one_tenth" />

    <TextView
        android:id="@+id/tv_field2_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/data_placeholder"
        app:layout_constraintStart_toStartOf="@+id/tv_field1_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field1_data" />

    <TextView
        android:id="@+id/tv_field3_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/data_placeholder"
        app:layout_constraintStart_toStartOf="@+id/tv_field2_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field2_data" />

    <TextView
        android:id="@+id/tv_field4_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/data_placeholder"
        app:layout_constraintStart_toStartOf="@+id/tv_field3_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field3_data" />

    <TextView
        android:id="@+id/tv_field5_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/data_placeholder"
        app:layout_constraintStart_toStartOf="@+id/tv_field3_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field4_data" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_horizontal_half"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_vertical_one_tenth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.05" />

    <TextView
        android:id="@+id/tv_field1_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/title_placeholder"
        app:layout_constraintEnd_toStartOf="@+id/gl_horizontal_half"
        app:layout_constraintTop_toTopOf="@+id/tv_field1_data" />

    <TextView
        android:id="@+id/tv_field2_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/title_placeholder"
        app:layout_constraintEnd_toStartOf="@+id/gl_horizontal_half"
        app:layout_constraintTop_toTopOf="@+id/tv_field2_data" />

    <TextView
        android:id="@+id/tv_field3_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/title_placeholder"
        app:layout_constraintEnd_toStartOf="@+id/gl_horizontal_half"
        app:layout_constraintTop_toTopOf="@+id/tv_field3_data" />

    <TextView
        android:id="@+id/tv_field4_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/title_placeholder"
        app:layout_constraintEnd_toStartOf="@+id/gl_horizontal_half"
        app:layout_constraintTop_toTopOf="@+id/tv_field4_data" />

    <TextView
        android:id="@+id/tv_field5_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/title_placeholder"
        app:layout_constraintEnd_toEndOf="@+id/tv_field4_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field5_data" />

    <TextView
        android:id="@+id/tv_field6_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field5_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field5_data" />

    <TextView
        android:id="@+id/tv_field6_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field5_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field6_data" />

    <TextView
        android:id="@+id/tv_field7_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field6_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field7_data" />

    <TextView
        android:id="@+id/tv_field7_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field6_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field6_data" />

    <TextView
        android:id="@+id/tv_field8_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field7_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field8_data" />

    <TextView
        android:id="@+id/tv_field9_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field8_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field9_data" />

    <TextView
        android:id="@+id/tv_field10_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field9_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field10_data" />

    <TextView
        android:id="@+id/tv_field8_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field7_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field7_data" />

    <TextView
        android:id="@+id/tv_field9_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field8_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field8_data" />

    <TextView
        android:id="@+id/tv_field10_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field9_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field9_data" />

    <TextView
        android:id="@+id/tv_field11_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field10_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field10_data" />

    <TextView
        android:id="@+id/tv_field12_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field11_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field11_data" />

    <TextView
        android:id="@+id/tv_field13_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field12_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field12_data" />

    <TextView
        android:id="@+id/tv_field14_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field13_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field13_data" />

    <TextView
        android:id="@+id/tv_field15_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field14_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field14_data" />

    <TextView
        android:id="@+id/tv_field16_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field15_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field15_data" />

    <TextView
        android:id="@+id/tv_field17_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="data"
        app:layout_constraintStart_toStartOf="@+id/tv_field16_data"
        app:layout_constraintTop_toBottomOf="@+id/tv_field16_data" />

    <TextView
        android:id="@+id/tv_field11_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field10_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field11_data" />

    <TextView
        android:id="@+id/tv_field12_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field11_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field12_data" />

    <TextView
        android:id="@+id/tv_field13_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field12_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field13_data" />

    <TextView
        android:id="@+id/tv_field14_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field13_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field14_data" />

    <TextView
        android:id="@+id/tv_field15_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field14_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field15_data" />

    <TextView
        android:id="@+id/tv_field16_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field15_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field16_data" />

    <TextView
        android:id="@+id/tv_field17_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="title"
        app:layout_constraintEnd_toEndOf="@+id/tv_field16_title"
        app:layout_constraintTop_toTopOf="@+id/tv_field17_data" />

</androidx.constraintlayout.widget.ConstraintLayout>