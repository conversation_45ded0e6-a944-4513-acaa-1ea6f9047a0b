<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/sw_language"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:textOn="TH"
        android:textOff="EN" />

    <TextView
        android:id="@+id/tv_language"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/sw_language"
        android:layout_centerHorizontal="true" />

    <Button
        android:id="@+id/bt_go_to_liveness_ocr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="@string/go_to_liveness_ocr" />

    <Button
        android:id="@+id/bt_go_to_liveness"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bt_go_to_liveness_ocr"
        android:text="@string/go_to_liveness" />

    <Button
        android:id="@+id/bt_go_to_ndid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bt_go_to_liveness"
        android:text="@string/go_to_ndid" />

    <Button
        android:id="@+id/bt_go_to_ocr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bt_go_to_ndid"
        android:text="@string/go_to_ocr" />

    <Button
        android:id="@+id/bt_go_to_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bt_go_to_ocr"
        android:text="@string/go_to_result" />

</RelativeLayout>