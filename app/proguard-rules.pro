# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-dontwarn javax.annotation.Nullable
-dontwarn com.facetec.sdk.**
-keep class com.facetec.sdk.**
{ *; }

-keepattributes InnerClasses

-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }

# Keep Conscrypt classes
-keep class org.conscrypt.** { *; }
-dontwarn org.conscrypt.**

# Keep OpenJSSE classes (if you are using it, less common than Conscrypt)
-keep class org.openjsse.** { *; }
-dontwarn org.openjsse.**

# Keep OkHttp's internal platform classes that might reference Conscrypt/OpenJSSE
-keep class okhttp3.internal.platform.** { *; }
-dontwarn okhttp3.internal.platform.**

-dontwarn org.bouncycastle.**

# ===== EKYC FRAMEWORK ADDITIONAL RULES =====
# These rules supplement the consumer-rules.pro from the eKYC framework

# Keep Retrofit and RxJava integration
-keep class retrofit2.adapter.rxjava3.** { *; }
-dontwarn retrofit2.adapter.rxjava3.**

# Keep Gson TypeToken and related classes
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# Additional safety for generic types in API calls
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault

# Keep all classes that might be used in reflection
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# Prevent obfuscation of classes that extend HashMap (like AuthenticatedHeaders)
-keep class * extends java.util.HashMap { *; }

# Keep method signatures for debugging
-keepattributes SourceFile,LineNumberTable

# Additional RxJava safety
-dontwarn io.reactivex.rxjava3.**
-keep class io.reactivex.rxjava3.internal.** { *; }

# Keep Kotlin metadata for proper function signatures
-keep class kotlin.Metadata { *; }
-keepclassmembers class * {
    @kotlin.jvm.JvmStatic *;
}

# Additional Retrofit safety
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# Keep classes with native methods
-keepclasseswithmembernames class * {
    native <methods>;
}